<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 80px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: white;
        line-height: 1.2;
        max-width: 1200px;
        letter-spacing: -1px;
    }
    
    .timeline-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }
    
    .timeline-content {
        position: relative;
        height: 500px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .timeline-main-line {
        position: absolute;
        left: 0;
        right: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.4);
        top: 50%;
        transform: translateY(-50%);
    }
    
    .timeline-item {
        width: 380px;
        color: white;
        position: relative;
    }
    
    .timeline-item.q1 {
        align-self: flex-start;
    }
    
    .timeline-item.q2 {
        align-self: flex-end;
    }
    
    .timeline-item.q3 {
        align-self: flex-start;
    }
    
    .quarter-label {
        font-size: 20px;
        font-weight: 400;
        margin-bottom: 8px;
        opacity: 0.9;
    }
    
    .period-label {
        font-size: 28px;
        font-weight: 500;
        margin-bottom: 15px;
        letter-spacing: -0.5px;
    }
    
    .timeline-description {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.6;
        opacity: 0.85;
        letter-spacing: 0.2px;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: rgba(255, 255, 255, 0.3);
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: white;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">SaaS Evolution Timeline Transforming Business Over the Years</h1>
</div>

<div class="timeline-wrapper">
    <div class="timeline-content">
        <div class="timeline-main-line"></div>
        
        <div class="timeline-item q1">
            <div class="quarter-label">Q1</div>
            <div class="period-label">Jan - March</div>
            <div class="timeline-description">Strategic Vision Blueprint, Platform Prototype Development, Foundational Partnership Agreements, Team Expansion & Talent Acquisition.</div>
        </div>
        
        <div class="timeline-item q2">
            <div class="quarter-label">Q2</div>
            <div class="period-label">April - June</div>
            <div class="timeline-description">Market Entry Strategy for Asia, Beta Testing & User Feedback Loop, Community Engagement Programs, Initial Brand Campaign Launch.</div>
        </div>
        
        <div class="timeline-item q3">
            <div class="quarter-label">Q3</div>
            <div class="period-label">July - August</div>
            <div class="timeline-description">Platform Security Integration, Community Rewards Program, Referral & Growth Incentive Program, Advanced Gamification Features.</div>
        </div>
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 9</div>
</div>
</body>
</html>