<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px;
    }
    
    .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        z-index: 10;
    }
    
    .year, .company-name {
        font-size: 24px;
        font-weight: 400;
        color: white;
        letter-spacing: 0.5px;
    }
    
    .main-content {
        position: absolute;
        bottom: 240px;
        left: 108px;
        right: 108px;
        z-index: 2;
    }
    
    .thank-you-text {
        font-size: 200px;
        font-weight: 300;
        color: white;
        letter-spacing: 8px;
        line-height: 1;
        text-transform: uppercase;
        margin-bottom: 50px;
    }
    
    .divider {
        width: 100%;
        height: 2px;
        background: rgba(255, 255, 255, 0.4);
    }
    
    .contact-section {
        position: absolute;
        bottom: 80px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        z-index: 2;
    }
    
    .contact-item {
        flex: 1;
    }
    
    .contact-label {
        font-size: 20px;
        font-weight: 500;
        color: white;
        margin-bottom: 12px;
        letter-spacing: 0.5px;
    }
    
    .contact-value {
        font-size: 18px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
        letter-spacing: 0.3px;
    }
    
    /* Subtle background texture */
    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
        z-index: 1;
    }
    
    .header {
        position: relative;
        z-index: 2;
    }
</style>

<div class="header">
    <div class="year">2024</div>
    <div class="company-name">Elevate</div>
</div>

<div class="main-content">
    <div class="thank-you-text">THANK YOU</div>
    <div class="divider"></div>
</div>

<div class="contact-section">
    <div class="contact-item">
        <div class="contact-label">Phone</div>
        <div class="contact-value">18+ *********** 55</div>
    </div>
    
    <div class="contact-item">
        <div class="contact-label">Email</div>
        <div class="contact-value"><EMAIL></div>
    </div>
    
    <div class="contact-item">
        <div class="contact-label">Website</div>
        <div class="contact-value">www.elevate.media</div>
    </div>
    
    <div class="contact-item">
        <div class="contact-label">Social Media</div>
        <div class="contact-value">@elevate</div>
    </div>
</div>
</body>
</html>