<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: #f8f9fa;
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 60px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: #888;
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: #1a1a1a;
        line-height: 1.2;
        max-width: 1100px;
        letter-spacing: -1px;
    }
    
    .cards-container {
        display: flex;
        gap: 50px;
        align-items: stretch;
        flex: 1;
        padding-bottom: 30px;
    }
    
    .feature-card {
        flex: 1;
        border-radius: 20px;
        padding: 50px 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-8px);
    }
    
    .feature-card.gray {
        background: #e8e8e8;
    }
    
    .feature-card.green {
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
    }
    
    .icon-wrapper {
        width: 80px;
        height: 80px;
        margin-bottom: 80px;
    }
    
    .icon-wrapper svg {
        width: 100%;
        height: 100%;
    }
    
    .card-description {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.6;
        letter-spacing: 0.2px;
        margin-bottom: 30px;
    }
    
    .gray .card-description {
        color: #666;
    }
    
    .green .card-description {
        color: white;
    }
    
    .card-divider {
        width: 100%;
        height: 1px;
        margin-bottom: 20px;
    }
    
    .gray .card-divider {
        background: #d0d0d0;
    }
    
    .green .card-divider {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .card-title {
        font-size: 24px;
        font-weight: 400;
        letter-spacing: -0.5px;
    }
    
    .gray .card-title {
        color: #1a1a1a;
    }
    
    .green .card-title {
        color: white;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: #d0d0d0;
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">The Transformative Role of Elevate in Reshaping Modern Business</h1>
</div>

<div class="cards-container">
    <div class="feature-card gray">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="#1a1a1a" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                <path d="M2 17l10 5 10-5"/>
                <path d="M2 12l10 5 10-5"/>
            </svg>
        </div>
        <div>
            <p class="card-description">Streamlined workflows and automated processes that eliminate redundancies, enabling teams to focus on strategic initiatives and drive meaningful business outcomes.</p>
            <div class="card-divider"></div>
            <h3 class="card-title">Process Optimization</h3>
        </div>
    </div>
    
    <div class="feature-card gray">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="#1a1a1a" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 6v6l4 2"/>
            </svg>
        </div>
        <div>
            <p class="card-description">Real-time collaboration tools and integrated communication channels that break down silos and foster seamless teamwork across departments and locations.</p>
            <div class="card-divider"></div>
            <h3 class="card-title">Enhanced Collaboration</h3>
        </div>
    </div>
    
    <div class="feature-card green">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 3v18"/>
                <path d="M16 15l-4-4 4-4"/>
            </svg>
        </div>
        <div>
            <p class="card-description">Advanced analytics and predictive insights that transform raw data into strategic intelligence, empowering data-driven decision making at every level.</p>
            <div class="card-divider"></div>
            <h3 class="card-title">Data Intelligence</h3>
        </div>
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 6</div>
</div>
</body>
</html>