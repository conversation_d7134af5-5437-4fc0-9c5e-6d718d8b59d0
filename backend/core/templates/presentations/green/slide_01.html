<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: linear-gradient(135deg, #1a4d2e 0%, #2d5f3f 100%);
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
        padding: 80px 108px;
    }
    
    .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        z-index: 10;
    }
    
    .year {
        font-size: 24px;
        font-weight: 400;
        color: white;
        letter-spacing: 0.5px;
    }
    
    .deck-type {
        font-size: 24px;
        font-weight: 400;
        color: white;
        letter-spacing: 0.5px;
    }
    
    .main-content {
        position: absolute;
        bottom: 140px;
        left: 108px;
        z-index: 2;
    }
    
    .text-wrapper {
        display: inline-block;
        position: relative;
    }
    
    .elevate-text {
        font-size: 264px;
        font-weight: 300;
        color: white;
        letter-spacing: -4px;
        line-height: 0.9;
        text-transform: uppercase;
        margin-bottom: 30px;
        display: block;
        white-space: nowrap;
    }
    
    .underline {
        width: 100%;
        height: 1px;
        background: white;
        opacity: 0.8;
        display: block;
    }
    
    /* Subtle background texture */
    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
        z-index: 1;
    }
    
    .header {
        position: relative;
        z-index: 2;
    }
</style>

<div class="header">
    <div class="year">2024</div>
    <div class="deck-type">Pitch Deck</div>
</div>

<div class="main-content">
    <div class="text-wrapper">
        <div class="elevate-text">ELEVATE.</div>
        <div class="underline"></div>
    </div>
</div>
</body>
</html>