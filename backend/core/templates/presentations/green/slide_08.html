<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 8</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: #f8f9fa;
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 40px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: #888;
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: #1a1a1a;
        line-height: 1.2;
        max-width: 1100px;
        letter-spacing: -1px;
    }
    
    .content-section {
        display: flex;
        gap: 80px;
        align-items: flex-end;
        flex: 1;
        padding-bottom: 40px;
    }
    
    .left-section {
        flex: 0 0 700px;
        display: flex;
        flex-direction: column;
        gap: 0;
    }
    
    .quarter-box {
        border-radius: 20px;
        padding: 35px 45px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }
    
    .quarter-box.q1 {
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
        height: 280px;
        z-index: 3;
    }
    
    .quarter-box.q2 {
        background: #e8e8e8;
        height: 220px;
        margin-top: -20px;
        z-index: 2;
    }
    
    .quarter-box.q3 {
        background: #f5f5f5;
        height: 160px;
        margin-top: -20px;
        z-index: 1;
    }
    
    .quarter-label {
        font-size: 22px;
        font-weight: 300;
        letter-spacing: 0.5px;
    }
    
    .q1 .quarter-label {
        color: white;
    }
    
    .q2 .quarter-label,
    .q3 .quarter-label {
        color: #999;
    }
    
    .quarter-value {
        font-weight: 300;
        letter-spacing: -3px;
        line-height: 1;
    }
    
    .q1 .quarter-value {
        font-size: 100px;
        color: white;
    }
    
    .q2 .quarter-value {
        font-size: 80px;
        color: #d0d0d0;
    }
    
    .q3 .quarter-value {
        font-size: 60px;
        color: #d0d0d0;
    }
    
    .right-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-end;
        padding-bottom: 20px;
    }
    
    .product-label {
        font-size: 24px;
        font-weight: 400;
        color: #888;
        margin-bottom: 20px;
        letter-spacing: 0.5px;
    }
    
    .product-stat {
        font-size: 140px;
        font-weight: 700;
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
        letter-spacing: -5px;
        margin-bottom: 25px;
        position: relative;
    }
    
    .product-stat::after {
        content: '▲';
        position: absolute;
        top: 0;
        right: -50px;
        font-size: 35px;
        color: #2d5f3f;
    }
    
    .product-description {
        font-size: 18px;
        font-weight: 400;
        color: #666;
        line-height: 1.6;
        letter-spacing: 0.2px;
        max-width: 400px;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: #d0d0d0;
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">The Growing Business Value of SaaS Across Diverse Market Sectors</h1>
</div>

<div class="content-section">
    <div class="left-section">
        <div class="quarter-box q1">
            <div class="quarter-label">Q1</div>
            <div class="quarter-value">425%</div>
        </div>
        <div class="quarter-box q2">
            <div class="quarter-label">Q2</div>
            <div class="quarter-value">350%</div>
        </div>
        <div class="quarter-box q3">
            <div class="quarter-label">Q3</div>
            <div class="quarter-value">215%</div>
        </div>
    </div>
    
    <div class="right-section">
        <div class="product-label">Product 1</div>
        <div class="product-stat">425%</div>
        <div class="product-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris finibus.</div>
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 8</div>
</div>
</body>
</html>