<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: #f8f9fa;
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 80px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: #888;
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: #1a1a1a;
        line-height: 1.2;
        max-width: 1100px;
        letter-spacing: -1px;
    }
    
    .content-section {
        display: flex;
        gap: 100px;
        align-items: center;
        flex: 1;
        padding-top: 200px;
    }
    
    .stat-number {
        font-size: 280px;
        font-weight: 400;
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
        letter-spacing: -8px;
    }
    
    .description-text {
        flex: 1;
        font-size: 20px;
        font-weight: 400;
        color: #666;
        line-height: 1.8;
        letter-spacing: 0.2px;
        max-width: 900px;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: #d0d0d0;
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">The Growing Business Value of SaaS Across Diverse Market Sectors</h1>
</div>

<div class="content-section">
    <div class="stat-number">45%</div>
    <div class="description-text">
        The SaaS market continues to demonstrate remarkable growth across diverse sectors, with enterprise adoption increasing by 45% year-over-year. Organizations are recognizing the transformative potential of cloud-based solutions in driving operational efficiency, reducing costs, and enabling rapid scalability. This growth trajectory reflects the fundamental shift in how businesses approach technology infrastructure and digital transformation initiatives.
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 7</div>
</div>
</body>
</html>