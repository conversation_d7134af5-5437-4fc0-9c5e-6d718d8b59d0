<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: #f8f9fa;
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 80px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: #888;
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: #1a1a1a;
        line-height: 1.2;
        max-width: 1200px;
        letter-spacing: -1px;
    }
    
    .advantages-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 40px;
        justify-content: center;
    }
    
    .advantage-row {
        display: flex;
        align-items: center;
        gap: 50px;
        padding: 40px 50px;
        border-radius: 20px;
        transition: transform 0.3s ease;
    }
    
    .advantage-row.white {
        background: transparent;
    }
    
    .advantage-row.green {
        background: linear-gradient(135deg, #2d5f3f 0%, #1a4d2e 100%);
    }
    
    .advantage-row:hover {
        transform: translateX(10px);
    }
    
    .icon-wrapper {
        width: 70px;
        height: 70px;
        flex-shrink: 0;
    }
    
    .icon-wrapper svg {
        width: 100%;
        height: 100%;
    }
    
    .advantage-title {
        font-size: 28px;
        font-weight: 500;
        letter-spacing: -0.5px;
        min-width: 350px;
    }
    
    .advantage-description {
        font-size: 18px;
        font-weight: 400;
        line-height: 1.6;
        letter-spacing: 0.2px;
        flex: 1;
    }
    
    .white .advantage-title,
    .white .advantage-description {
        color: #1a1a1a;
    }
    
    .white .advantage-description {
        color: #666;
    }
    
    .green .advantage-title,
    .green .advantage-description {
        color: white;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: #d0d0d0;
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">Key SaaS Advantages That Revolutionize How Businesses Operate Today</h1>
</div>

<div class="advantages-container">
    <div class="advantage-row white">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="#1a1a1a" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <path d="M21 15l-5-5L5 21"/>
            </svg>
        </div>
        <div class="advantage-title">Scalability & Flexibility</div>
        <div class="advantage-description">Cloud-based infrastructure enables seamless scaling to accommodate business growth, with flexible resource allocation that adapts to changing demands without infrastructure constraints.</div>
    </div>
    
    <div class="advantage-row green">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 6v6l4 2"/>
            </svg>
        </div>
        <div class="advantage-title">Cost Efficiency</div>
        <div class="advantage-description">Subscription-based pricing eliminates large upfront investments, reduces IT overhead costs, and provides predictable monthly expenses with pay-as-you-grow models that optimize budget allocation.</div>
    </div>
    
    <div class="advantage-row white">
        <div class="icon-wrapper">
            <svg viewBox="0 0 24 24" fill="none" stroke="#1a1a1a" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                <path d="M2 17l10 5 10-5"/>
                <path d="M2 12l10 5 10-5"/>
            </svg>
        </div>
        <div class="advantage-title">Automatic Updates</div>
        <div class="advantage-description">Continuous platform improvements and security patches deployed automatically, ensuring access to latest features without manual intervention or system downtime for maintenance.</div>
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 10</div>
</div>
</body>
</html>