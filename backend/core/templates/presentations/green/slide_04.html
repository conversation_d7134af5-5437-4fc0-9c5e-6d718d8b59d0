<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELEVATE Pitch Deck 2024 - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Space Grotesk', sans-serif;
        background: #f8f9fa;
        width: 1920px;
        height: 1080px;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
        padding: 80px 108px 140px 108px;
    }
    
    .header-section {
        margin-bottom: 80px;
    }
    
    .subtitle {
        font-size: 20px;
        font-weight: 400;
        color: #888;
        margin-bottom: 15px;
        letter-spacing: 0.5px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 500;
        color: #1a1a1a;
        line-height: 1.2;
        max-width: 1200px;
        letter-spacing: -1px;
    }
    
    .features-container {
        display: flex;
        flex-direction: column;
        gap: 60px;
        flex: 1;
    }
    
    .feature-item {
        display: flex;
        gap: 30px;
        align-items: flex-start;
    }
    
    .feature-number {
        font-size: 24px;
        font-weight: 600;
        color: #2d5f3f;
        min-width: 40px;
    }
    
    .feature-line {
        width: 3px;
        height: 100%;
        background: #2d5f3f;
        min-height: 80px;
    }
    
    .feature-content {
        flex: 1;
    }
    
    .feature-title {
        font-size: 32px;
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 12px;
        letter-spacing: -0.5px;
    }
    
    .feature-description {
        font-size: 18px;
        font-weight: 400;
        color: #666;
        line-height: 1.6;
        letter-spacing: 0.2px;
        max-width: 800px;
    }
    
    .footer-divider {
        position: absolute;
        bottom: 120px;
        left: 0;
        right: 0;
        height: 1px;
        background: #d0d0d0;
    }
    
    .footer {
        position: absolute;
        bottom: 50px;
        left: 108px;
        right: 108px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #666;
        font-weight: 400;
    }
    
    .footer-left {
        letter-spacing: 0.5px;
    }
    
    .footer-right {
        letter-spacing: 0.5px;
    }
</style>

<div class="header-section">
    <div class="subtitle">Your Special Text Here</div>
    <h1 class="main-heading">Unleashing Business Growth Through Scalable and Agile SaaS Solutions</h1>
</div>

<div class="features-container">
    <div class="feature-item">
        <div class="feature-number">01</div>
        <div class="feature-line"></div>
        <div class="feature-content">
            <h3 class="feature-title">Cloud-Native Architecture</h3>
            <p class="feature-description">Built on modern cloud infrastructure to ensure seamless scalability, high availability, and optimal performance for businesses of all sizes.</p>
        </div>
    </div>
    
    <div class="feature-item">
        <div class="feature-number">02</div>
        <div class="feature-line"></div>
        <div class="feature-content">
            <h3 class="feature-title">Agile Integration Framework</h3>
            <p class="feature-description">Flexible integration capabilities that adapt to your existing systems, enabling rapid deployment and continuous innovation without disruption.</p>
        </div>
    </div>
    
    <div class="feature-item">
        <div class="feature-number">03</div>
        <div class="feature-line"></div>
        <div class="feature-content">
            <h3 class="feature-title">Data-Driven Intelligence</h3>
            <p class="feature-description">Advanced analytics and AI-powered insights that transform raw data into actionable strategies, driving informed decision-making and business growth.</p>
        </div>
    </div>
</div>

<div class="footer-divider"></div>

<div class="footer">
    <div class="footer-left">2024 | Elevate</div>
    <div class="footer-right">Slide 4</div>
</div>
</body>
</html>