<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px 80px;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(3, 1fr);
    gap: 80px 200px;
    height: 100%;
  }
  
  .stat-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .stat-number {
    font-size: 200px;
    font-weight: 400;
    color: #000000;
    line-height: 1;
    letter-spacing: -6px;
    margin-bottom: 15px;
  }
  
  .stat-label {
    font-size: 38px;
    font-weight: 400;
    color: #000000;
    letter-spacing: -0.3px;
  }
</style>

<div class="slide-container">
  <div class="stats-grid">
    <div class="stat-box">
      <div class="stat-number">129</div>
      <div class="stat-label">Departure</div>
    </div>
    
    <div class="stat-box">
      <div class="stat-number">218</div>
      <div class="stat-label">Expected Booking</div>
    </div>
    
    <div class="stat-box">
      <div class="stat-number">352</div>
      <div class="stat-label">Total Occupancy</div>
    </div>
    
    <div class="stat-box">
      <div class="stat-number">$31,000</div>
      <div class="stat-label">Total Revenue</div>
    </div>
    
    <div class="stat-box">
      <div class="stat-number">49-66</div>
      <div class="stat-label">Leads Growth</div>
    </div>
    
    <div class="stat-box">
      <div class="stat-number">3.324</div>
      <div class="stat-label">Days Spent</div>
    </div>
  </div>
</div>
</body>
</html>