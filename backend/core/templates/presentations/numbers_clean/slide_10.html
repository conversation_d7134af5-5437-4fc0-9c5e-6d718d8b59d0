<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px;
  }
  
  .slide-title {
    font-size: 52px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 80px;
    letter-spacing: -0.5px;
    line-height: 1.2;
  }
  
  .content-wrapper {
    margin-top: -100px;
  }
  
  .content-row {
    display: flex;
    align-items: center;
    margin-bottom: 120px;
  }
  
  .divider-line {
    width: 370px;
    height: 8px;
    margin-right: 80px;
  }
  
  .divider-red {
    background: #E63946;
  }
  
  .divider-black {
    background: #000000;
  }
  
  .text-number-group {
    display: flex;
    align-items: center;
    flex: 1;
    margin-top: -50px;
  }
  
  .row-text {
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
    max-width: 480px;
    margin-right: 80px;
  }
  
  .row-number {
    font-size: 320px;
    font-weight: 400;
    line-height: 1;
    letter-spacing: -12px;
    margin-left: auto;
  }
  
  .number-red {
    color: #E63946;
  }
  
  .number-black {
    color: #000000;
  }
  
  .footer {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
</style>

<div class="slide-container">
  <h1 class="slide-title">Employee<br>Onboarding<br>Total</h1>
  
  <div class="content-wrapper">
    <div class="content-row">
      <div class="divider-line divider-red"></div>
      <div class="text-number-group">
        <div class="row-text">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
        </div>
        <div class="row-number number-red">40%</div>
      </div>
    </div>
    
    <div class="content-row">
      <div class="divider-line divider-black"></div>
      <div class="text-number-group">
        <div class="row-text">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
        </div>
        <div class="row-number number-black">109%</div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <span class="footer-title">Number Data</span>
    Presentation Template
  </div>
</div>
</body>
</html>