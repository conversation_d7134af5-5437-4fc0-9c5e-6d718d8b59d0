<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number Data Visualization - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
  }
  
  .content-wrapper {
    position: absolute;
    left: 65px;
    top: 145px;
    z-index: 2;
  }
  
  .main-title {
    font-size: 140px;
    font-weight: 900;
    color: #000000;
    line-height: 1.1;
    letter-spacing: -3px;
  }
  
  .title-number {
    display: block;
  }
  
  .title-data {
    display: block;
    position: relative;
    display: inline-block;
  }
  
  .red-accent {
    position: absolute;
    bottom: 15px;
    left: 0;
    width: 340px;
    height: 12px;
    background: #E63946;
    border-radius: 2px;
  }
  
  .subtitle {
    font-size: 56px;
    font-weight: 400;
    color: #2B2B2B;
    margin-top: 15px;
    letter-spacing: -1px;
  }
  
  .template-label {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 38px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.5px;
  }
  
  .percent-graphic {
    position: absolute;
    right: -50px;
    top: calc(50% + 200px);
    transform: translateY(-50%);
    width: 720px;
    height: auto;
    opacity: 1;
    z-index: 1;
  }
  
  .percent-graphic img {
    width: 100%;
    height: auto;
    display: block;
  }
  
  /* Subtle decorative elements */
  .deco-circle {
    position: absolute;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(230, 57, 70, 0.03) 0%, transparent 70%);
    top: -200px;
    right: 200px;
    z-index: 0;
  }
</style>

<div class="slide-container">
  <div class="deco-circle"></div>
  
  <div class="content-wrapper">
    <h1 class="main-title">
      <span class="title-number">Number</span>
      <span class="title-data">
        Data
        <span class="red-accent"></span>
      </span>
    </h1>
    <p class="subtitle">Visualization</p>
  </div>
  
  <div class="template-label">Presentation Template</div>
  
  <div class="percent-graphic">
    <img src="percent.png" alt="Percent Symbol">
  </div>
</div>
</body>
</html>