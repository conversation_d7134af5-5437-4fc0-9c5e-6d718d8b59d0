<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px;
  }
  
  .slide-title {
    font-size: 52px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 60px;
    letter-spacing: -0.5px;
  }
  
  .top-text {
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
    max-width: 520px;
    margin-bottom: 120px;
  }
  
  .right-section {
    position: absolute;
    right: 65px;
    top: 280px;
    text-align: right;
  }
  
  .right-text {
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
    max-width: 480px;
    margin-bottom: 60px;
    text-align: left;
    margin-left: auto;
  }
  
  .large-amount {
    font-size: 380px;
    font-weight: 400;
    color: #000000;
    line-height: 1;
    letter-spacing: -12px;
  }
  
  .footer {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
</style>

<div class="slide-container">
  <h1 class="slide-title">Total Budget Spend</h1>
  
  <div class="top-text">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
  </div>
  
  <div class="right-section">
    <div class="right-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
    </div>
    
    <div class="large-amount">$128</div>
  </div>
  
  <div class="footer">
    <span class="footer-title">Number Data</span>
    Presentation Template
  </div>
</div>
</body>
</html>