<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px;
    display: flex;
  }
  
  .left-section {
    width: 420px;
    margin-right: 100px;
  }
  
  .slide-title {
    font-size: 52px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 30px;
    letter-spacing: -0.5px;
    line-height: 1.2;
  }
  
  .intro-text {
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
  }
  
  .right-section {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 80px 120px;
    align-content: center;
  }
  
  .stat-item {
    display: flex;
    flex-direction: column;
  }
  
  .stat-number {
    font-size: 240px;
    font-weight: 400;
    line-height: 1;
    letter-spacing: -8px;
    margin-bottom: 20px;
  }
  
  .stat-number.black {
    color: #000000;
  }
  
  .stat-number.red {
    color: #E63946;
  }
  
  .stat-description {
    font-size: 26px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
    max-width: 420px;
  }
  
  .footer {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <h1 class="slide-title">Departmental<br>Headcount</h1>
    
    <div class="intro-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
    </div>
  </div>
  
  <div class="right-section">
    <div class="stat-item">
      <div class="stat-number black">23</div>
      <div class="stat-description">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
    
    <div class="stat-item">
      <div class="stat-number black">462</div>
      <div class="stat-description">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
    
    <div class="stat-item">
      <div class="stat-number black">5</div>
      <div class="stat-description">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
    
    <div class="stat-item">
      <div class="stat-number red">8021</div>
      <div class="stat-description">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
  </div>
  
  <div class="footer">
    <span class="footer-title">Number Data</span>
    Presentation Template
  </div>
</div>
</body>
</html>