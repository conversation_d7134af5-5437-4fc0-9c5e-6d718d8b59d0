<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px;
  }
  
  .chart-row {
    display: flex;
    align-items: center;
    margin-bottom: 0;
  }
  
  .row-text {
    width: 380px;
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.5;
    letter-spacing: 0.2px;
    margin-right: 60px;
  }
  
  .bar-container {
    flex: 1;
    display: flex;
    align-items: center;
  }
  
  .bar {
    height: 276px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 60px;
    font-size: 140px;
    font-weight: 400;
    color: #000000;
    letter-spacing: -4px;
  }
  
  .bar-1 {
    background: #EF7C7C;
    width: 560px;
  }
  
  .bar-2 {
    background: #E63946;
    width: 910px;
  }
  
  .bar-3 {
    background: #F5A5A5;
    width: 440px;
  }
  
  .footer {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
</style>

<div class="slide-container">
  <div class="chart-row">
    <div class="row-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
    </div>
    <div class="bar-container">
      <div class="bar bar-1">213</div>
    </div>
  </div>
  
  <div class="chart-row">
    <div class="row-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
    </div>
    <div class="bar-container">
      <div class="bar bar-2">1290</div>
    </div>
  </div>
  
  <div class="chart-row">
    <div class="row-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
    </div>
    <div class="bar-container">
      <div class="bar bar-3">49</div>
    </div>
  </div>
  
  <div class="footer">
    <span class="footer-title">Number Data</span>
    Presentation Template
  </div>
</div>
</body>
</html>