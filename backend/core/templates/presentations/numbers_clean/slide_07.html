<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    padding: 65px;
  }
  
  .top-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 100px;
  }
  
  .left-text {
    font-size: 38px;
    font-weight: 400;
    color: #000000;
    line-height: 1.4;
    letter-spacing: -0.3px;
    max-width: 380px;
  }
  
  .right-text {
    font-size: 28px;
    font-weight: 400;
    color: #2B2B2B;
    line-height: 1.6;
    letter-spacing: 0.2px;
    max-width: 480px;
  }
  
  .hero-number {
    font-size: 550px;
    font-weight: 400;
    color: #E63946;
    line-height: 1;
    letter-spacing: -20px;
    text-align: right;
    margin-top: 50px;
  }
  
  .footer {
    position: absolute;
    left: 65px;
    bottom: 65px;
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
</style>

<div class="slide-container">
  <div class="top-section">
    <div class="left-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
    </div>
    
    <div class="right-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
    </div>
  </div>
  
  <div class="hero-number">468</div>
  
  <div class="footer">
    <span class="footer-title">Number Data</span>
    Presentation Template
  </div>
</div>
</body>
</html>