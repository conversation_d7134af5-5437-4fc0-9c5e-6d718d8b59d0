<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #E8E8E8;
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    display: flex;
  }
  
  .left-section {
    width: 33%;
    background: linear-gradient(135deg, #E8E8E8 0%, #F0F0F0 100%);
    padding: 65px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .left-text {
    font-size: 38px;
    font-weight: 400;
    color: #000000;
    line-height: 1.4;
    letter-spacing: -0.3px;
  }
  
  .footer {
    font-size: 24px;
    font-weight: 400;
    color: #4A4A4A;
    letter-spacing: 0.3px;
  }
  
  .footer-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
  }
  
  .right-section {
    width: 67%;
    display: flex;
    flex-direction: column;
  }
  
  .top-red-box {
    height: 40%;
    background: #EF6C6C;
    padding: 50px 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .bottom-red-box {
    height: 60%;
    background: #E63946;
    padding: 60px 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .large-number-top {
    font-size: 200px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1;
    letter-spacing: -6px;
  }
  
  .small-text {
    font-size: 24px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1.5;
    max-width: 450px;
  }
  
  .middle-text {
    font-size: 28px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1.6;
    letter-spacing: 0.2px;
  }
  
  .large-number-bottom {
    font-size: 280px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1;
    letter-spacing: -8px;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <div class="left-text">
      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
    </div>
    <div class="footer">
      <span class="footer-title">Number Data</span>
      Presentation Template
    </div>
  </div>
  
  <div class="right-section">
    <div class="top-red-box">
      <div class="large-number-top">30%</div>
      <div class="small-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
    
    <div class="bottom-red-box">
      <div class="middle-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
      </div>
      
      <div class="large-number-bottom">70%</div>
    </div>
  </div>
</div>
</body>
</html>