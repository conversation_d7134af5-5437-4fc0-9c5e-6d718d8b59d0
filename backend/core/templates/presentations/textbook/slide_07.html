<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Stardos+Stencil:wght@400;700&family=Inter:wght@400;500;600&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: #0A0A0A;
}

/* Main Container */
.main-container {
    width: 100%;
    height: 100%;
    padding: 80px;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 100px;
}

.slide-title {
    font-family: 'Stardos Stencil', cursive;
    font-size: 72px;
    color: #B8A888;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.header-info {
    display: flex;
    gap: 100px;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    color: #FFFFFF;
    padding-top: 20px;
}

/* List Container */
.list-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* List Item */
.list-item {
    display: flex;
    align-items: center;
    gap: 60px;
    padding: 50px 0;
    border-top: 3px dashed rgba(255, 255, 255, 0.3);
    border-image: repeating-linear-gradient(
        to right,
        rgba(255, 255, 255, 0.3) 0,
        rgba(255, 255, 255, 0.3) 20px,
        transparent 20px,
        transparent 40px
    ) 1;
}

.list-item:last-child {
    border-bottom: 3px dashed rgba(255, 255, 255, 0.3);
    border-image: repeating-linear-gradient(
        to right,
        rgba(255, 255, 255, 0.3) 0,
        rgba(255, 255, 255, 0.3) 20px,
        transparent 20px,
        transparent 40px
    ) 1;
}

/* Shape Icon */
.shape-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.shape-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Text */
.item-text {
    font-family: 'Inter', sans-serif;
    font-size: 48px;
    line-height: 1.4;
    color: #FFFFFF;
    font-weight: 400;
}
</style>

<div class="main-container">
    <div class="header">
        <h1 class="slide-title">WHAT WE DO</h1>
        <div class="header-info">
            <span>Agency Name x Partner Name</span>
            <span>Phase X</span>
        </div>
    </div>
    
    <div class="list-container">
        <div class="list-item">
            <div class="shape-icon">
                <img src="../../uploads/Polygon 1.png" alt="Triangle">
            </div>
            <div class="item-text">Quickly list your team's specialties.</div>
        </div>
        
        <div class="list-item">
            <div class="shape-icon">
                <img src="../../uploads/Polygon 2.png" alt="Pentagon">
            </div>
            <div class="item-text">Keep each of them short and sweet.</div>
        </div>
        
        <div class="list-item">
            <div class="shape-icon">
                <img src="../../uploads/Union.png" alt="X Shape">
            </div>
            <div class="item-text">Make them easy to scan and remember.</div>
        </div>
        
        <div class="list-item">
            <div class="shape-icon">
                <img src="../../uploads/Union2.png" alt="Bow Shape">
            </div>
            <div class="item-text">Only highlight the most important bits.</div>
        </div>
    </div>
</div>
</body>
</html>