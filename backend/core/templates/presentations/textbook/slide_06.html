<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Stardos+Stencil:wght@400;700&family=Courier+Prime:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Courier Prime', monospace;
    background: #0A0A0A;
}

/* Main Container */
.main-container {
    width: 100%;
    height: 100%;
    padding: 80px;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 120px;
}

.slide-title {
    font-family: 'Stardos Stencil', cursive;
    font-size: 72px;
    color: #FF8B6B;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.header-info {
    display: flex;
    gap: 100px;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    color: #FFFFFF;
    padding-top: 20px;
}

/* Content Section */
.content-section {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 0 0 0 0;
}

.main-text {
    font-family: 'Courier Prime', monospace;
    font-size: 64px;
    line-height: 1.4;
    color: #FFFFFF;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
    max-width: 1600px;
}
</style>

<div class="main-container">
    <div class="header">
        <h1 class="slide-title">OUR PURPOSE</h1>
        <div class="header-info">
            <span>Agency Name x Partner Name</span>
            <span>Phase X</span>
        </div>
    </div>
    
    <div class="content-section">
        <p class="main-text">
            TELL THE CLIENT WHAT THE TEAM IS ALL ABOUT—WHO YOU ARE AT YOUR CORE, AND WHAT YOU AIM TO ACHIEVE.
        </p>
    </div>
</div>
</body>
</html>