<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Stardos+Stencil:wght@400;700&family=Zilla+Slab:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Zilla Slab', serif;
    background: #0A0A0A;
}

/* Main Container */
.main-container {
    width: 100%;
    height: 100%;
    padding: 80px;
}

/* Title */
.slide-title {
    font-family: 'Stardos Stencil', cursive;
    font-size: 72px;
    color: #FFFFFF;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 100px;
}

/* Agenda Grid */
.agenda-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 80px 120px;
    height: calc(100% - 200px);
    padding: 0 60px;
}

/* Agenda Item */
.agenda-item {
    display: flex;
    align-items: center;
    gap: 30px;
}

/* Number Styling */
.number {
    font-family: 'Zilla Slab', serif;
    font-size: 280px;
    font-weight: 700;
    line-height: 0.85;
    letter-spacing: -8px;
}

.number-1 {
    color: #FF8B6B;
}

.number-2 {
    color: #5FA8A3;
}

.number-3 {
    color: #B8A888;
}

.number-4 {
    color: #C97BA6;
}

/* Label Styling */
.label {
    font-family: 'Zilla Slab', serif;
    font-size: 32px;
    font-weight: 500;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 3px;
}

/* Positioning adjustments */
.agenda-item:nth-child(1) {
    justify-content: flex-start;
    margin-left: 200px;
}

.agenda-item:nth-child(2) {
    justify-content: flex-end;
    margin-right: 200px;
}

.agenda-item:nth-child(3) {
    justify-content: center;
    margin-left: 200px;
}

.agenda-item:nth-child(4) {
    justify-content: flex-end;
    margin-right: 200px;
}
</style>

<div class="main-container">
    <h1 class="slide-title">AGENDA</h1>
    
    <div class="agenda-grid">
        <div class="agenda-item">
            <div class="number number-1">1</div>
            <div class="label">INTROS</div>
        </div>
        
        <div class="agenda-item">
            <div class="number number-2">2</div>
            <div class="label">BACKGROUND</div>
        </div>
        
        <div class="agenda-item">
            <div class="number number-3">3</div>
            <div class="label">PROPOSAL</div>
        </div>
        
        <div class="agenda-item">
            <div class="number number-4">4</div>
            <div class="label">Q&A</div>
        </div>
    </div>
</div>
</body>
</html>