<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Stardos+Stencil:wght@400;700&family=Inter:wght@400;500;600&family=Zilla+Slab:wght@400;500;600&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: #DED8CB;
    padding: 50px;
}

/* Main Card Container */
.main-card {
    width: 100%;
    height: 100%;
    background-image: url('../../uploads/pres_frame_2.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 32px;
    position: relative;
    overflow: hidden;
}

/* Header Info */
.header-info {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 100px;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    color: #000000;
    padding: 50px 60px;
    z-index: 10;
}

/* Content Container */
.content-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 200px;
    text-align: center;
}

/* Title */
.slide-title {
    font-family: 'Stardos Stencil', serif;
    font-size: 64px;
    font-weight: 400;
    color: #000000;
    letter-spacing: 8px;
    text-transform: uppercase;
    margin-bottom: 80px;
}

/* Description */
.description {
    font-family: 'Inter', sans-serif;
    font-size: 48px;
    line-height: 1.5;
    color: #000000;
    font-weight: 400;
    max-width: 1400px;
}
</style>

<div class="main-card">
    <div class="header-info">
        <span>Agency Name x Partner Name</span>
        <span>Phase X</span>
    </div>
    
    <div class="content-container">
        <h1 class="slide-title">OUR MISSION</h1>
        <p class="description">
            Tell the client what the team is all about—who you are at your core, and what you aim to achieve.
        </p>
    </div>
</div>
</body>
</html>