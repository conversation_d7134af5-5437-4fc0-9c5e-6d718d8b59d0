<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Stardos+Stencil:wght@400;700&family=Inter:wght@400;500;600&family=Courier+Prime:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: #DED8CB;
}

/* Main Container */
.main-container {
    width: 100%;
    height: 100%;
    padding: 80px;
}

/* Header Section */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 80px;
}

.slide-title {
    font-family: 'Stardos Stencil', cursive;
    font-size: 72px;
    color: #000000;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.header-info {
    display: flex;
    gap: 100px;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    color: #000000;
    padding-top: 20px;
}

/* Team Grid */
.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 50px;
}

/* Team Card */
.team-card {
    display: flex;
    flex-direction: column;
}

.card-name {
    font-family: 'Courier Prime', monospace;
    font-size: 24px;
    font-weight: 700;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.card-image {
    width: 100%;
    aspect-ratio: 1.5 / 1;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 24px;
    background: #000000;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-description {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #000000;
    font-weight: 400;
}
</style>

<div class="main-container">
    <div class="header">
        <h1 class="slide-title">OUR TEAM</h1>
        <div class="header-info">
            <span>Agency Name x Partner Name</span>
            <span>Phase X</span>
        </div>
    </div>
    
    <div class="team-grid">
        <div class="team-card">
            <div class="card-name">FULL NAME</div>
            <div class="card-image">
                <img src="../../uploads/image1.png" alt="Team Member 1">
            </div>
            <div class="card-description">
                Highlight bits about each team member that are relevant to this specific pitch and project.
            </div>
        </div>
        
        <div class="team-card">
            <div class="card-name">FULL NAME</div>
            <div class="card-image">
                <img src="../../uploads/image1.png" alt="Team Member 2">
            </div>
            <div class="card-description">
                Role, location, past ventures, and more—anything that will let this client know why the team is so qualified to get the job done.
            </div>
        </div>
        
        <div class="team-card">
            <div class="card-name">FULL NAME</div>
            <div class="card-image">
                <img src="../../uploads/image1.png" alt="Team Member 3">
            </div>
            <div class="card-description">
                If sharing async, you might want to to link to profiles on your team's website, or on social sites like LinkedIn.
            </div>
        </div>
    </div>
</div>
</body>
</html>