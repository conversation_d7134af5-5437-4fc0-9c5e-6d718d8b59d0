<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pitch Deck Template - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Staatliches&family=Inter:wght@400;600;700;800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: #DED8CB;
    padding: 50px;
}

/* Main Card Container */
.main-card {
    width: 100%;
    height: 100%;
    background-image: url('../../uploads/pres_frame_3.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 32px;
    position: relative;
    overflow: hidden;
}

/* Content Overlay */
.content-overlay {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    z-index: 3;
}

/* Top Section */
.top-section {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 70px 80px 0 80px;
}

/* Top Right Date */
.top-date {
    font-family: 'Staatliches', cursive;
    font-size: 28px;
    color: #000000;
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Center Section for Title */
.center-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 0 80px;
    margin-left: 300px;
}

.title-part {
    font-family: 'Staatliches', cursive;
    font-size: 220px;
    line-height: 0.85;
    color: #000000;
    letter-spacing: -2px;
    font-weight: 400;
    text-transform: uppercase;
}

.title-part:first-child {
    align-self: flex-start;
    margin-left: 200px;
}

.title-part:last-child {
    align-self: flex-end;
    margin-right: 520px;
}

/* Bottom Info */
.bottom-section {
    padding: 0 80px 70px 160px;
}

.bottom-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.info-item {
    font-family: 'Staatliches', cursive;
    font-size: 32px;
    color: #000000;
    letter-spacing: 1px;
    text-transform: uppercase;
}
</style>

<div class="main-card">
    <div class="content-overlay">
        <div class="top-section">
            <div class="top-date">TODAY'S DATE</div>
        </div>
        
        <div class="center-section">
            <h1 class="title-part">THANK</h1>
            <h1 class="title-part">YOU</h1>
        </div>
        
        <div class="bottom-section">
            <div class="bottom-info">
                <div class="info-item">— AGENCY NAME</div>
                <div class="info-item">— PARTNER NAME</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>