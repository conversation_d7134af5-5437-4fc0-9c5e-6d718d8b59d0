<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concept Byrn - Beauty & Lifestyle - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:ital,wght@0,400;1,400&family=Epilogue:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
    background-color: #FFFFFF;
}

.slide-container {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 80px;
    display: flex;
    gap: 80px;
}

.left-sidebar {
    width: 340px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.color-tag {
    background-color: #B5FF81;
    border-radius: 25px;
    padding: 8px 24px;
    font-family: 'Epilogue', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
    align-self: flex-start;
}

.sidebar-content {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.sidebar-title {
    font-family: 'Epilogue', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-text {
    font-family: 'Epilogue', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: #000000;
    line-height: 30px;
}

.footer-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 36px;
    font-weight: 400;
    font-style: italic;
    color: #000000;
    margin-top: auto;
}

.color-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 0;
}

.color-block {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.color-block.tennis-ball {
    background-color: #B5FF81;
    grid-row: 1 / 3;
}

.color-block.tennis-whites {
    background-color: #FFFFFF;
}

.color-block.astro-turf {
    background-color: #516B38;
}

.color-block.pitch {
    background-color: #000000;
}

.color-info {
    font-family: 'Epilogue', sans-serif;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.4;
}

.color-info.light {
    color: #000000;
}

.color-info.dark {
    color: #FFFFFF;
}

.color-name {
    text-transform: uppercase;
    margin-bottom: 5px;
}
</style>

<div class="slide-container">
    <div class="left-sidebar">
        <div>
            <div class="color-tag">COLOR</div>
            <div class="sidebar-content">
                <div class="sidebar-title">PRIMARY PALETTE</div>
                <div class="sidebar-text">
                    Use this slide to highlight the details behind your brand's colors.
                    <br><br>
                    The more specific the better, so viewers know exactly what to and what not to do when using this component.
                </div>
            </div>
        </div>
        
        <div class="footer-text">Brand Logo</div>
    </div>

    <div class="color-grid">
        <div class="color-block tennis-ball">
            <div class="color-info light">
                <div class="color-name">TENNIS BALL</div>
                <div>#B5FF81</div>
                <div>C:00 M:00 Y:00 K:00</div>
                <div>PANTONE 0000</div>
            </div>
        </div>

        <div class="color-block tennis-whites">
            <div class="color-info light">
                <div class="color-name">TENNIS WHITES</div>
                <div>#FFFFFF</div>
                <div>C:00 M:00 Y:00 K:00</div>
                <div>PANTONE 0000</div>
            </div>
        </div>

        <div class="color-block astro-turf">
            <div class="color-info dark">
                <div class="color-name">ASTRO TURF</div>
                <div>#516B38</div>
                <div>C:00 M:00 Y:00 K:00</div>
                <div>PANTONE 0000</div>
            </div>
        </div>

        <div class="color-block pitch">
            <div class="color-info dark">
                <div class="color-name">PITCH</div>
                <div>#000000</div>
                <div>C:00 M:00 Y:00 K:00</div>
                <div>PANTONE 0000</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>