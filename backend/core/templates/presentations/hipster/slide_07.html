<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concept Byrn - Beauty & Lifestyle - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:ital,wght@0,400;1,400&family=Epilogue:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
    background-color: #E8E8E8;
}

.slide-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: space-between;
}

.left-sidebar {
    width: 490px;
    padding: 80px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.logos-tag {
    background-color: #B5FF81;
    border-radius: 25px;
    padding: 12px 30px;
    font-family: 'Epilogue', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
    align-self: flex-start;
}

.sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 40px;
}

.sidebar-title {
    font-family: 'Epilogue', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-text {
    font-family: 'Epilogue', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: #000000;
    line-height: 30px;
}

.footer-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 36px;
    font-weight: 400;
    font-style: italic;
    color: #000000;
}

.right-content {
    width: calc(100% - 490px);
    display: flex;
    flex-direction: column;
}

.logo-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-box.green {
    background-color: #B5FF81;
    border-radius: 40px 0 0 0;
}

.logo-box.dark-green {
    background-color: #516B38;
}

.logo-box.black {
    background-color: #000000;
    border-radius: 0 0 0 40px;
}

.logo-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 140px;
    font-weight: 400;
    font-style: italic;
    line-height: 1;
}

.logo-text.black {
    color: #000000;
}

.logo-text.green {
    color: #B5FF81;
}
</style>

<div class="slide-container">
    <div class="left-sidebar">
        <div class="logos-tag">LOGOS</div>
        
        <div class="sidebar-content">
            <div class="sidebar-title">WORDMARK PALETTE</div>
            <div class="sidebar-text">
                Use this slide to highlight the color details behind your wordmark.
                <br><br>
                The more specific the better, so viewers exactly what to and what not to do when using this component.
            </div>
        </div>
        
        <div class="footer-text">Brand Logo</div>
    </div>

    <div class="right-content">
        <div class="logo-box green">
            <div class="logo-text black">Brand Logo</div>
        </div>
        <div class="logo-box dark-green">
            <div class="logo-text green">Brand Logo</div>
        </div>
        <div class="logo-box black">
            <div class="logo-text green">Brand Logo</div>
        </div>
    </div>
</div>
</body>
</html>