<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concept Byrn - Beauty & Lifestyle - Slide 13</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:ital,wght@0,400;1,400&family=Epilogue:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
    background-color: #FFFFFF;
}

.slide-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
}

.left-sidebar {
    width: 490px;
    padding: 80px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.grid-tag {
    background-color: #B5FF81;
    border-radius: 25px;
    padding: 8px 24px;
    font-family: 'Epilogue', sans-serif;
    font-size: 20px;
    font-weight: 400;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
    align-self: flex-start;
}

.sidebar-content {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.sidebar-title {
    font-family: 'Epilogue', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sidebar-text {
    font-family: 'Epilogue', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: #000000;
    line-height: 30px;
}

.footer-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 36px;
    font-weight: 400;
    font-style: italic;
    color: #000000;
    margin-top: auto;
}

.right-content {
    flex: 1;
    padding: 80px 80px 80px 0;
}

.grid-container {
    width: 100%;
    height: 100%;
    background-color: #CECECE;
    border-radius: 40px;
    padding: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.grid-columns {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 20px;
    width: 100%;
    max-width: 900px;
}

.grid-column {
    background-color: #B5FF81;
    opacity: 0.5;
    height: 360px;
}
</style>

<div class="slide-container">
    <div class="left-sidebar">
        <div>
            <div class="grid-tag">GRID</div>
            <div class="sidebar-content">
                <div class="sidebar-title">12-COLUMN GRID</div>
                <div class="sidebar-text">
                    Use this slide to highlight the details behind your brand's layout grids.
                    <br><br>
                    The more specific the better, so viewers know exactly what to and what not to do when using this component.
                </div>
            </div>
        </div>
        
        <div class="footer-text">Brand Logo</div>
    </div>

    <div class="right-content">
        <div class="grid-container">
            <div class="grid-columns">
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
                <div class="grid-column"></div>
            </div>
        </div>
    </div>
</div>
</body>
</html>