<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concept Byrn - Beauty & Lifestyle - Slide 16</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:ital,wght@0,400;1,400&family=Epilogue:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
    background-color: #B5FF81;
}

.slide-container {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 80px 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.thank-you-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.thank-box {
    background-color: #000000;
    border-radius: 40px;
    padding: 60px 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.thank-text {
    font-family: 'Epilogue', sans-serif;
    font-size: 180px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.you-box {
    background-color: #516B38;
    border-radius: 40px;
    padding: 60px 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.you-text {
    font-family: 'Epilogue', sans-serif;
    font-size: 180px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 1;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.contact-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 60px;
    font-weight: 400;
    font-style: italic;
    color: #000000;
}
</style>

<div class="slide-container">
    <div class="thank-you-container">
        <div class="thank-box">
            <div class="thank-text">THANK</div>
        </div>
        <div class="you-box">
            <div class="you-text">YOU</div>
        </div>
    </div>

    <div class="contact-text"><EMAIL></div>
</div>
</body>
</html>