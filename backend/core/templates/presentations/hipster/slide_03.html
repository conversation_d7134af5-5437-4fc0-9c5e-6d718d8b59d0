<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concept Byrn - Beauty & Lifestyle - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:ital,wght@0,400;1,400&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
    background-color: #E8E8E8;
}

.slide-container {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 60px 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
}

.content-item {
    background-color: #000000;
    border-radius: 40px;
    padding: 40px 60px;
    display: flex;
    align-items: center;
    gap: 30px;
}

.content-item.full-width {
    grid-column: 1 / -1;
}

.content-item.medium {
    grid-column: span 1;
}

.item-number {
    font-family: 'Arial', sans-serif;
    font-size: 48px;
    font-weight: 400;
    color: #FFFFFF;
    min-width: 60px;
}

.item-text {
    font-family: 'Arial', sans-serif;
    font-size: 110px;
    font-weight: 400;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.footer-text {
    font-family: 'Averia Serif Libre', serif;
    font-size: 36px;
    font-weight: 400;
    font-style: italic;
    color: #000000;
    margin-top: 20px;
}

/* Specific grid layout */
.row-1 {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 25px;
}

.row-2 {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 25px;
}

.row-3 {
    display: grid;
    grid-template-columns: 0.8fr 1.2fr 1fr;
    gap: 25px;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 25px;
    flex: 1;
}
</style>

<div class="slide-container">
    <div class="content-wrapper">
        <div class="row-1">
            <div class="content-item">
                <div class="item-number">01</div>
                <div class="item-text">LOGOS</div>
            </div>
            <div class="content-item">
                <div class="item-number">02</div>
                <div class="item-text">TYPOGRAPHY</div>
            </div>
        </div>

        <div class="row-2">
            <div class="content-item">
                <div class="item-number">03</div>
                <div class="item-text">COLOR</div>
            </div>
            <div class="content-item">
                <div class="item-number">04</div>
                <div class="item-text">PHOTOGRAPHY</div>
            </div>
        </div>

        <div class="row-3">
            <div class="content-item">
                <div class="item-number">05</div>
                <div class="item-text">GRID</div>
            </div>
            <div class="content-item">
                <div class="item-number">06</div>
                <div class="item-text">DIGITAL</div>
            </div>
            <div class="content-item">
                <div class="item-number">07</div>
                <div class="item-text">PRINT</div>
            </div>
        </div>

        <div class="content-item full-width">
            <div class="item-number">08</div>
            <div class="item-text">APPLICATION</div>
        </div>
    </div>

    <div class="footer-text">Brand Logo</div>
</div>
</body>
</html>