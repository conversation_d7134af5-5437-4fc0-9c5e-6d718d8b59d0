<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syllabus Presentation - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    background: #383838;
    font-family: 'Playfair Display', serif;
    color: #ffffff;
    overflow: hidden;
    position: relative;
}

.slide-container {
    width: 100%;
    height: 100%;
    padding: 80px 120px;
    display: flex;
    gap: 100px;
    align-items: center;
    position: relative;
}

.left-section {
    flex: 0 0 400px;
}

.image-placeholder {
    width: 100%;
    height: 640px;
    background: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0),
                linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0);
    background-size: 40px 40px;
    background-position: 0 0, 20px 20px;
    background-color: #f5f5f5;
    border-radius: 20px;
}

.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.content-title {
    font-size: 96px;
    font-weight: 400;
    line-height: 110px;
    letter-spacing: -0.01em;
}

.content-text {
    font-size: 32px;
    font-weight: 400;
    line-height: 46px;
    letter-spacing: 0.3px;
}

.footer {
    position: absolute;
    bottom: 60px;
    right: 120px;
    font-size: 24px;
    font-weight: 400;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.footer-divider {
    color: #ffffff;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <div class="image-placeholder"></div>
    </div>
    
    <div class="content-area">
        <div class="content-title">Single concept</div>
        <div class="content-text">
            A paragraph is a structural unit in writing that consists of a group of related sentences focused on a central idea or topic. It serves as a cohesive block of text within a larger piece of writing, such as an essay, article, or story.
        </div>
    </div>
    
    <div class="footer">
        <span>Course Name</span>
        <span class="footer-divider">|</span>
        <span>Syllabus</span>
        <span class="footer-divider">|</span>
        <span>Instructor</span>
        <span style="margin-left: 40px;">19</span>
    </div>
</div>
</body>
</html>