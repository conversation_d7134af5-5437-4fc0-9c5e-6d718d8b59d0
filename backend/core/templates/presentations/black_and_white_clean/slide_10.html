<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #f5f5f5;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
  }
  
  .header {
    position: absolute;
    top: 40px;
    left: 40px;
    font-size: 32px;
    font-weight: 400;
    color: #cccccc;
    letter-spacing: 0.5px;
  }
  
  .footer {
    position: absolute;
    bottom: 40px;
    left: 40px;
    right: 40px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 400;
    color: #cccccc;
    letter-spacing: 0.5px;
  }
  
  .content-grid {
    position: absolute;
    top: 280px;
    left: 40px;
    right: 40px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
  }
  
  .column {
    display: flex;
    flex-direction: column;
  }
  
  .column-header {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 80px;
    padding-bottom: 65px;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .column-content {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 1.7;
  }
  
  .audience-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
  }
  
  .description-text {
    margin-bottom: 20px;
  }
  
  .bullet-list {
    list-style: none;
    padding: 0;
  }
  
  .bullet-list li {
    margin-bottom: 15px;
    padding-left: 20px;
    position: relative;
  }
  
  .bullet-list li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #1a1a1a;
    font-weight: bold;
  }
</style>

<div class="slide-container">
  <div class="header">Target Audience 1</div>
  
  <div class="content-grid">
    <div class="column">
      <div class="column-header">Audience</div>
      <div class="column-content">
        <div class="audience-title">Logistics Manager & Procurement Manager</div>
      </div>
    </div>
    
    <div class="column">
      <div class="column-header">Description</div>
      <div class="column-content">
        <div class="description-text">Briefly describe who this audience is (e.g., professionals in law enforcement, infrastructure, healthcare, etc.).</div>
        <ul class="bullet-list">
          <li>Demographics: Include typical age range, income level, job roles, and the types of organizations or sectors they work in.</li>
          <li>Personality Traits: Highlight relevant behavioral traits such as being analytical, risk-averse, detail-focused, or mission-driven.</li>
          <li>Relevant Aspects: Note key responsibilities, professional background, decision-making power, and what they value in a solution or partnership. Focus on what makes them a high-priority or strategic target for your brand.</li>
        </ul>
      </div>
    </div>
    
    <div class="column">
      <div class="column-header">Goals</div>
      <div class="column-content">
        <ul class="bullet-list">
          <li>Proving operational effectiveness to support regulatory approvals.</li>
          <li>Achieving cost efficiency.</li>
          <li>Seamless integration with existing partner systems.</li>
          <li>Ensuring reliable, around-the-clock device detection.</li>
          <li>Demonstrating clear ROI on technology or security investments.</li>
          <li>Streamlining and simplifying daily operations.</li>
          <li>Enabling scalable protection across multiple locations.</li>
          <li>Enhancing responsiveness and effectiveness in critical situations.</li>
        </ul>
      </div>
    </div>
    
    <div class="column">
      <div class="column-header">Obstacles</div>
      <div class="column-content">
        <ul class="bullet-list">
          <li>Cost.</li>
          <li>Regulatory acceptance.</li>
          <li>Installation and maintenance requirements.</li>
          <li>Scalability.</li>
          <li>Overwhelmed by false alarms and manual monitoring.</li>
          <li>Limited visibility in challenging conditions.</li>
          <li>Difficulty justifying security investments.</li>
          <li>Complex integration with existing systems.</li>
          <li>Infrastructure dependencies limiting deployment.</li>
        </ul>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <div>[Company Name]</div>
    <div>[Presentation Title]</div>
  </div>
</div>
</body>
</html>