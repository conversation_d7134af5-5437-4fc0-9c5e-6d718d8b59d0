<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brand Report Presentation - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #161616;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
  }
  
  .content-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 0 0 120px;
    position: relative;
  }
  
  .left-section {
    flex: 1;
    max-width: 800px;
  }
  
  .header-text {
    font-size: 28px;
    font-weight: 300;
    color: #ffffff;
    margin-bottom: 60px;
    letter-spacing: 0.5px;
  }
  
  .main-title {
    font-size: 120px;
    font-weight: 300;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 80px;
    letter-spacing: -2px;
  }
  
  .footer-text {
    font-size: 32px;
    font-weight: 300;
    color: #666666;
    letter-spacing: 1px;
    display: none;
  }
  
  .footer-text .company {
    color: #999999;
    font-weight: 500;
    margin-left: 15px;
  }
  
  .right-section {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: auto;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  
  .card {
    background: #ffffff;
    border-radius: 32px;
    padding: 50px;
    box-shadow: 0 30px 90px rgba(0, 0, 0, 0.4);
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .card-header {
    font-size: 22px;
    font-weight: 500;
    color: #999999;
    margin-bottom: 80px;
    letter-spacing: 0.5px;
  }
  
  .keywords-section {
    margin-bottom: 60px;
  }
  
  .keywords-text {
    font-size: 32px;
    line-height: 1.6;
    color: #333333;
    font-weight: 400;
  }
  
  .keywords-text .highlight {
    color: #000000;
    font-weight: 600;
  }
  
  .keywords-text .muted {
    color: #999999;
    font-weight: 400;
  }
  
  .sliders-section {
    margin-top: auto;
  }
  
  .sliders-header {
    font-size: 20px;
    font-weight: 500;
    color: #999999;
    margin-bottom: 35px;
    letter-spacing: 0.5px;
  }
  
  .slider-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .slider-label {
    width: 180px;
    font-size: 16px;
    font-weight: 500;
    color: #1a1a1a;
    flex-shrink: 0;
  }
  
  .slider-track {
    flex: 1;
    height: 32px;
    background: linear-gradient(90deg, #a8f5d9 0%, #6ee7b7 100%);
    border-radius: 16px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 8px;
  }
  
  .slider-dots {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 8px;
  }
  
  .dot {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
  }
  
  .dot.active {
    width: 18px;
    height: 18px;
    background: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e5e5e5;
  }
  
  .card-footer-text {
    font-size: 14px;
    color: #cccccc;
    font-weight: 400;
  }
  
  .right-image {
    height: 85vh;
    max-height: 918px;
    width: auto;
    display: block;
    object-fit: contain;
    margin: 0;
    padding: 0;
  }
</style>

<div class="slide-container">
  <div class="content-wrapper">
    <div class="left-section">
      <div class="header-text">FREE Brand Report Presentation</div>
      <div class="main-title">Brand<br>Report<br>Template</div>
    </div>
    
    <div class="right-section">
      <img src="uploads/recorte_20251012_061013_1.png" alt="Brand Keywords and Personality Sliders" class="right-image">
    </div>
  </div>
</div>
</body>
</html>