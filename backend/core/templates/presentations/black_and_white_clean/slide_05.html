<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #f5f5f5;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
  }
  
  .footer {
    position: absolute;
    bottom: 40px;
    left: 40px;
    right: 40px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 400;
    color: #cccccc;
    letter-spacing: 0.5px;
  }
  
  .content-area {
    position: absolute;
    top: 40px;
    left: 40px;
    right: 40px;
    width: auto;
    max-width: 1600px;
  }
  
  .main-text {
    font-size: 72px;
    font-weight: 400;
    color: #1a1a1a;
    line-height: 1.3;
    letter-spacing: -0.5px;
    text-align: left;
  }
</style>

<div class="slide-container">
  <div class="content-area">
    <div class="main-text">
      Use this space to define the brand's role and core offering. Describe how the company supports its clients, highlighting its services, areas of expertise, and the value it delivers through tailored, dependable, and forward-thinking solutions.
    </div>
  </div>
  
  <div class="footer">
    <div>[Company Name]</div>
    <div>[Presentation Title]</div>
  </div>
</div>
</body>
</html>