<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #1a1a1a;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
  }
  
  .footer {
    position: absolute;
    bottom: 40px;
    left: 40px;
    right: 40px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 400;
    color: #666666;
    letter-spacing: 0.5px;
  }
  
  .content-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 1400px;
  }
  
  .main-message {
    font-size: 64px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.4;
    letter-spacing: -0.5px;
    margin-bottom: 180px;
  }
  
  .contact-section {
    font-size: 48px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.5;
    letter-spacing: -0.3px;
  }
  
  .email-placeholder {
    color: #999999;
    font-size: 42px;
    margin-top: 20px;
  }
</style>

<div class="slide-container">
  <div class="content-center">
    <div class="main-message">
      We're here whenever you're<br>ready to take the next step.
    </div>
    
    <div class="contact-section">
      If you have any doubts, we are<br>here to help you.<br>
      <span class="email-placeholder">[your email here]</span>
    </div>
  </div>
  
  <div class="footer">
    <div>[Company Name]</div>
    <div>[Presentation Title]</div>
  </div>
</div>
</body>
</html>