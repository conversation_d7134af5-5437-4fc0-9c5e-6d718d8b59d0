<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: #f5f5f5;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
  }
  
  .header {
    position: absolute;
    top: 40px;
    left: 40px;
    font-size: 32px;
    font-weight: 400;
    color: #cccccc;
    letter-spacing: 0.5px;
  }
  
  .footer {
    position: absolute;
    bottom: 40px;
    left: 40px;
    right: 40px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    font-weight: 400;
    color: #cccccc;
    letter-spacing: 0.5px;
  }
  
  .main-content {
    position: absolute;
    top: 40px;
    right: 80px;
    width: 900px;
  }
  
  .main-text {
    font-size: 32px;
    font-weight: 400;
    color: #1a1a1a;
    line-height: 1.6;
    letter-spacing: 0.3px;
  }
  
  .bottom-left-box {
    position: absolute;
    bottom: 180px;
    left: 40px;
    width: 280px;
  }
  
  .box-text {
    font-size: 16px;
    font-weight: 400;
    color: #1a1a1a;
    line-height: 1.6;
    letter-spacing: 0.3px;
    margin-bottom: 40px;
  }
  
  .bottom-right-box {
    position: absolute;
    bottom: 180px;
    left: 580px;
    width: 500px;
  }
  
  .small-text {
    font-size: 14px;
    font-weight: 400;
    color: #cccccc;
    line-height: 1.7;
    letter-spacing: 0.3px;
  }
</style>

<div class="slide-container">
  <div class="header">What</div>
  
  <div class="main-content">
    <div class="main-text">
      Use this section to briefly describe the main products, services, or solutions the company offers. Focus on the core areas of expertise and the benefits delivered—such as operational efficiency, security, or innovation. Mention key markets or regions served, and highlight any traits that make the brand known or trusted (e.g., speed, reliability, specialization).
    </div>
  </div>
  
  <div class="bottom-left-box">
    <div class="box-text">
      "What" defines the products, services, or experiences a brand delivers to its customers.
    </div>
  </div>
  
  <div class="bottom-right-box">
    <div class="small-text">
      Break down the core service areas in more detail. For example, describe how the company delivers technical support, infrastructure management, or consulting services. Highlight unique approaches, methodologies, or team structures that enhance service quality. If relevant, include how the brand embraces innovation or transformation, and how these efforts are aligned with delivering measurable business value.
    </div>
  </div>
  
  <div class="footer">
    <div>[Company Name]</div>
    <div>[Presentation Title]</div>
  </div>
</div>
</body>
</html>