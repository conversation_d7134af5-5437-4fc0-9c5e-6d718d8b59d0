<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Lilita+One&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    background-color: #536CF8;
    overflow: hidden;
    position: relative;
    font-family: Arial, sans-serif;
}

.header {
    position: absolute;
    top: 50px;
    left: 70px;
}

.project-title {
    font-family: '<PERSON>ita One', cursive;
    font-size: 80px;
    color: white;
    text-transform: uppercase;
    -webkit-text-stroke: 5px black;
    text-shadow: 
        -5px -5px 0 black,
        5px -5px 0 black,
        -5px 5px 0 black,
        5px 5px 0 black,
        -5px 0 0 black,
        5px 0 0 black,
        0 -5px 0 black,
        0 5px 0 black;
    paint-order: stroke fill;
    margin-bottom: 10px;
}

.subtitle {
    font-family: 'Courier New', monospace;
    font-size: 36px;
    color: white;
    font-weight: normal;
}

.competitors-container {
    position: absolute;
    top: 210px;
    left: 70px;
    width: 1780px;
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.competitor-column {
    flex: 1;
    position: relative;
}

.competitor-label {
    position: absolute;
    top: -8px;
    left: 20px;
    background-color: black;
    color: white;
    font-family: 'Lilita One', cursive;
    font-size: 40px;
    padding: 10px 30px;
    border-radius: 10px;
    text-transform: uppercase;
    z-index: 10;
}

.competitor-card {
    background-color: white;
    border: 8px solid black;
    border-radius: 30px;
    height: 465px;
    position: relative;
    margin-top: 30px;
    margin-left: 40px;
}

.divider {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 8px;
    background-color: black;
    transform: translateY(-50%);
}

.pros-section, .cons-section {
    position: absolute;
    left: 0;
    right: 0;
    height: 50%;
}

.pros-section {
    top: 0;
}

.cons-section {
    bottom: 0;
}

.emoji {
    position: absolute;
    left: -60px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background-color: #FFD93D;
    border-radius: 50%;
    border: 5px solid black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
}

.takeaways-box {
    position: absolute;
    top: 730px;
    left: 70px;
    width: 1780px;
    height: 210px;
    background-color: white;
    border: 8px solid black;
    border-radius: 30px;
}

.takeaways-label {
    position: absolute;
    top: -8px;
    left: 20px;
    background-color: black;
    color: white;
    font-family: 'Lilita One', cursive;
    font-size: 40px;
    padding: 10px 30px;
    border-radius: 10px;
    text-transform: uppercase;
}

.footer {
    position: absolute;
    bottom: 40px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 70px;
}

.website-url {
    font-family: 'Courier New', monospace;
    font-size: 48px;
    color: white;
    font-weight: bold;
}

.page-number {
    font-family: 'Lilita One', cursive;
    font-size: 60px;
    line-height: 60px;
    color: white;
    -webkit-text-stroke: 3px black;
    text-shadow: 
        -3px -3px 0 black,
        3px -3px 0 black,
        -3px 3px 0 black,
        3px 3px 0 black;
    paint-order: stroke fill;
}
</style>

<div class="header">
    <div class="project-title">PROJECT NAME</div>
    <div class="subtitle">competitive analysis</div>
</div>

<div class="competitors-container">
    <div class="competitor-column">
        <div class="competitor-label">COMPETITOR 1</div>
        <div class="competitor-card">
            <div class="pros-section">
                <div class="emoji">😊</div>
            </div>
            <div class="divider"></div>
            <div class="cons-section">
                <div class="emoji">☹️</div>
            </div>
        </div>
    </div>
    
    <div class="competitor-column">
        <div class="competitor-label">COMPETITOR 2</div>
        <div class="competitor-card">
            <div class="pros-section">
                <div class="emoji">😊</div>
            </div>
            <div class="divider"></div>
            <div class="cons-section">
                <div class="emoji">☹️</div>
            </div>
        </div>
    </div>
    
    <div class="competitor-column">
        <div class="competitor-label">COMPETITOR 3</div>
        <div class="competitor-card">
            <div class="pros-section">
                <div class="emoji">😊</div>
            </div>
            <div class="divider"></div>
            <div class="cons-section">
                <div class="emoji">☹️</div>
            </div>
        </div>
    </div>
</div>

<div class="takeaways-box">
    <div class="takeaways-label">TAKEAWAYS</div>
</div>

<div class="footer">
    <div class="website-url">moserux.com</div>
    <div class="page-number">5</div>
</div>
</body>
</html>