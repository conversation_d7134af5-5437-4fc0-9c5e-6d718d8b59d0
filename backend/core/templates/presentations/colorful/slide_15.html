<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 15</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #FFC400;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    padding: 0 150px;
    gap: 100px;
    z-index: 1;
}

.left-section {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.agenda-title {
    font-family: 'DM Serif Display', serif;
    font-size: 140px;
    line-height: 1.1;
    color: #FBE4CA;
    font-weight: 400;
    opacity: 0;
}

.right-section {
    display: flex;
    flex-direction: column;
    gap: 50px;
    padding-left: 50px;
}

.agenda-item {
    font-family: 'Inter', sans-serif;
    font-size: 52px;
    line-height: 1.3;
    color: #FBE4CA;
    font-weight: 400;
}

.agenda-item.white {
    color: #ffffff;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <h1 class="agenda-title">Agenda</h1>
    </div>
    
    <div class="right-section">
        <div class="agenda-item">Introduction</div>
        <div class="agenda-item">Why we're here</div>
        <div class="agenda-item">The crux of it</div>
        <div class="agenda-item white">Lessons learned</div>
        <div class="agenda-item">Parting thoughts</div>
    </div>
</div>
</body>
</html>