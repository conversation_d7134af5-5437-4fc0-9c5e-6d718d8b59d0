<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #ffffff;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
    flex-direction: column;
    z-index: 1;
}

.image-area {
    flex: 1;
    background: repeating-conic-gradient(#e5e5e5 0% 25%, #f5f5f5 0% 50%) 50% / 80px 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    color: #999;
    font-weight: 500;
}

.text-area {
    background: #ffffff;
    padding: 80px 150px;
    text-align: center;
}

.main-heading {
    font-family: 'DM Serif Display', serif;
    font-size: 90px;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 20px;
    font-weight: 400;
}

.subtext {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    line-height: 1.5;
    color: #1a1a1a;
    font-weight: 400;
}
</style>

<div class="slide-container">
    <div class="image-area">
        <div class="image-placeholder">[Hero Image Placeholder]</div>
    </div>
    
    <div class="text-area">
        <h1 class="main-heading">Maybe a big hero image?</h1>
        <p class="subtext">Could have a sub-line... or not, that's up to you</p>
    </div>
</div>
</body>
</html>