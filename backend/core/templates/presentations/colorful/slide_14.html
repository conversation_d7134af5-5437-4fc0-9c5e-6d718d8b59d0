<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 14</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #FBE4CA;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 200px;
    text-align: center;
    z-index: 1;
}

.quote-mark {
    font-size: 300px;
    color: rgba(0, 0, 0, 0.08);
    font-family: 'DM Serif Display', serif;
    line-height: 1;
    position: absolute;
    top: 100px;
    left: 200px;
}

.main-quote {
    font-family: 'DM Serif Display', serif;
    font-size: 90px;
    line-height: 1.1;
    color: #FF4617;
    margin-bottom: 40px;
    font-weight: 400;
    position: relative;
    z-index: 1;
}

.attribution {
    font-family: 'Inter', sans-serif;
    font-size: 44px;
    line-height: 1.5;
    color: #999;
    font-weight: 400;
}
</style>

<div class="slide-container">
    <div class="quote-mark">"</div>
    <h1 class="main-quote">A really big quote could be kind of fun sometimes … the right placement is up to you, but I like some intrigue</h1>
    <p class="attribution">Who said it?</p>
</div>
</body>
</html>