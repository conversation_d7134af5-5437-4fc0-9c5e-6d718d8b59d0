<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #ffffff;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    z-index: 1;
}

.left-section {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 100px 0 150px;
}

.right-section {
    background: #FBE4CA;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 100px 80px;
    gap: 30px;
}

.main-heading {
    font-family: 'DM Serif Display', serif;
    font-size: 90px;
    line-height: 1.2;
    color: #1a1a1a;
    font-weight: 400;
}

.main-heading .gray {
    color: #cccccc;
}

.main-heading .highlight {
    color: #136F64;
}

.image-placeholder {
    width: 100%;
    height: 320px;
    background: repeating-conic-gradient(#e5e5e5 0% 25%, #f5f5f5 0% 50%) 50% / 40px 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Inter', sans-serif;
    font-size: 18px;
    color: #999;
}

.caption {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    line-height: 1.5;
    color: #FF4617;
    font-weight: 400;
    width: 100%;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <h1 class="main-heading">Another headline<br><span class="gray">Try using</span> <span class="highlight">color</span> <span class="gray">in<br>interesting ways</span></h1>
    </div>
    
    <div class="right-section">
        <div class="image-placeholder">[Image Placeholder]</div>
        <p class="caption">You could fit a lil note here if you wanted</p>
        
        <div class="image-placeholder">[Image Placeholder]</div>
        <p class="caption">Perhaps another one here?</p>
    </div>
</div>
</body>
</html>