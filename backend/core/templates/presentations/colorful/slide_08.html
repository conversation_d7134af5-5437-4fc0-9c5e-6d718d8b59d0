<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 8</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #ffffff;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    z-index: 1;
}

.left-section {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 100px 0 150px;
}

.right-section {
    background: #A9F0F5;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100px;
    position: relative;
}

.main-heading {
    font-family: 'DM Serif Display', serif;
    font-size: 90px;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 40px;
    font-weight: 400;
}

.subtext {
    font-family: 'Inter', sans-serif;
    font-size: 28px;
    line-height: 1.6;
    color: #1a1a1a;
    font-weight: 400;
}

.quote-container {
    position: relative;
}

.quote-mark {
    position: absolute;
    top: -60px;
    left: -40px;
    font-size: 200px;
    color: rgba(1, 55, 93, 0.15);
    font-family: 'DM Serif Display', serif;
    line-height: 1;
}

.quote-text {
    font-family: 'DM Serif Display', serif;
    font-size: 56px;
    line-height: 1.3;
    color: #01375D;
    font-weight: 400;
    position: relative;
    z-index: 1;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <h1 class="main-heading">You can make a simple point here</h1>
        <p class="subtext">With a complementary quote over on the right, for some added flavor</p>
    </div>
    
    <div class="right-section">
        <div class="quote-container">
            <div class="quote-mark">"</div>
            <p class="quote-text">A quote of some kind that you want to emphasize can be nice to include here. Try not to have too much text in it though, people don't like reading, remember?</p>
        </div>
    </div>
</div>
</body>
</html>