<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 13</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #ffffff;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 150px;
    z-index: 1;
}

.list-items {
    display: flex;
    flex-direction: column;
    gap: 60px;
}

.list-item {
    display: flex;
    align-items: center;
    gap: 40px;
}

.number-circle {
    width: 70px;
    height: 70px;
    border: 3px solid #1a1a1a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 600;
    color: #1a1a1a;
    flex-shrink: 0;
}

.item-text {
    font-family: 'DM Serif Display', serif;
    font-size: 90px;
    line-height: 1.2;
    color: #1a1a1a;
    font-weight: 400;
}

.item-text .highlight {
    color: #FF4617;
}
</style>

<div class="slide-container">
    <div class="list-items">
        <div class="list-item">
            <div class="number-circle">1</div>
            <h2 class="item-text">First is the <span class="highlight">worst</span></h2>
        </div>
        
        <div class="list-item">
            <div class="number-circle">2</div>
            <h2 class="item-text">Second is the <span class="highlight">best</span></h2>
        </div>
        
        <div class="list-item">
            <div class="number-circle">3</div>
            <h2 class="item-text">Third is <span class="highlight">just ok I guess</span></h2>
        </div>
    </div>
</div>
</body>
</html>