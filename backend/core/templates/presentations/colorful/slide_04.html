<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Casual Yet Professional Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Inter:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #ffffff;
    font-family: 'Inter', sans-serif;
    position: relative;
    overflow: hidden;
}

.slide-container {
    position: relative;
    width: 1920px;
    height: 1080px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    z-index: 1;
}

.left-section {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 100px 0 150px;
}

.right-section {
    background: #C2F3D9;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.main-heading {
    font-family: 'DM Serif Display', serif;
    font-size: 72px;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 40px;
    font-weight: 400;
}

.subtext {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    line-height: 1.6;
    color: #1a1a1a;
    font-weight: 400;
}

.star-icon {
    width: 400px;
    height: 400px;
    position: relative;
}

.star {
    width: 100%;
    height: 100%;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    background: rgba(255, 255, 255, 0.3);
}
</style>

<div class="slide-container">
    <div class="left-section">
        <h1 class="main-heading">A clear simple statement about a memorable point</h1>
        <p class="subtext">You could have smaller text here, but let's be honest most people won't read much so keep it short</p>
    </div>
    
    <div class="right-section">
        <div class="star-icon">
            <div class="star"></div>
        </div>
    </div>
</div>
</body>
</html>