<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 80px;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Main Content */
.main-content {
    display: flex;
    gap: 80px;
    height: calc(100% - 120px);
    align-items: center;
}

/* Left Side - Text */
.left-side {
    flex: 1;
    display: flex;
    align-items: center;
}

.text-content {
    font-size: 42px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.4;
    letter-spacing: -0.5px;
}

/* Right Side - Portrait Image */
.right-side {
    width: 650px;
    height: 100%;
    display: flex;
    align-items: center;
}

.portrait-container {
    width: 100%;
    height: 90%;
    border-radius: 24px;
    overflow: hidden;
}

.portrait-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="main-content">
        <div class="left-side">
            <p class="text-content">
                The design and development teams will use the Brand Design System as a single source of truth to streamline their workflow, maintain visual consistency, and accelerate production.
            </p>
        </div>
        
        <div class="right-side">
            <div class="portrait-container">
                <img src="portrait-image.png" alt="Portrait" class="portrait-image">
            </div>
        </div>
    </div>
</div>
</body>
</html>