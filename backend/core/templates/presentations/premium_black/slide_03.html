<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 80px;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Main Content */
.main-content {
    position: relative;
    height: calc(100% - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Portrait Image */
.portrait-container {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.portrait-image {
    width: 100%;
    height: auto;
    max-height: 100%;
    border-radius: 24px;
    object-fit: cover;
}

/* Text Overlay */
.text-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 60px 40px 60px;
    z-index: 10;
}

.overlay-text {
    font-size: 42px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.4;
    letter-spacing: -0.5px;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="main-content">
        <div class="portrait-container">
            <img src="portrait-image.png" alt="Portrait" class="portrait-image">
        </div>
        
        <div class="text-overlay">
            <p class="overlay-text">
                In this project, we will design and ship a new brand design system for our upcoming Q2 Product Launch. This new brand system should accurately reflect our brand values and should illustrate our love and passion for great design.
            </p>
        </div>
    </div>
</div>
</body>
</html>