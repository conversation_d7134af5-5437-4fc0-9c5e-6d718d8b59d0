<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 80px;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Main Content */
.main-content {
    display: flex;
    gap: 30px;
    height: calc(100% - 120px);
}

/* Card */
.card {
    flex: 1;
    position: relative;
    border-radius: 24px;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 30px;
}

.card-title {
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    text-align: right;
    letter-spacing: -0.3px;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="main-content">
        <div class="card">
            <img src="gradient-audience.png" alt="Design & Dev" class="card-image">
            <div class="card-overlay">
                <div class="card-title">Design & Dev</div>
            </div>
        </div>
        
        <div class="card">
            <img src="portrait-image.png" alt="Marketing & Product" class="card-image">
            <div class="card-overlay">
                <div class="card-title">Marketing & Product</div>
            </div>
        </div>
        
        <div class="card">
            <img src="gradient-audience.png" alt="External Partners" class="card-image">
            <div class="card-overlay">
                <div class="card-title">External Partners</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>