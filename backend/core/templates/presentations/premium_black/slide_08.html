<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 8</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 80px;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Main Content */
.main-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 120px;
}

.title-section {
    flex: 1;
}

.main-title {
    font-size: 180px;
    font-weight: 400;
    color: #ffffff;
    line-height: 0.9;
    letter-spacing: -5px;
}

.number-section {
    font-size: 280px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    letter-spacing: -8px;
}

/* Description Section */
.description-section {
    position: absolute;
    bottom: 60px;
    left: 60px;
    right: 60px;
}

.description-text {
    font-size: 42px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.4;
    letter-spacing: -0.5px;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="main-content">
        <div class="title-section">
            <div class="main-title">Our<br>Audience</div>
        </div>
        <div class="number-section">02</div>
    </div>
    
    <div class="description-section">
        <p class="description-text">
            Our audience includes the designers, developers, marketers, product teams, and external partners who rely on a consistent, scalable brand system to create, implement, and communicate our visual identity across all touchpoints.
        </p>
    </div>
</div>
</body>
</html>