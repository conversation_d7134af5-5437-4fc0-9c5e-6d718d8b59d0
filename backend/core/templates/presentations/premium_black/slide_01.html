<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
}

/* Header Navigation */
.header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 60px;
    z-index: 10;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Content */
.content {
    position: absolute;
    top: 180px;
    left: 60px;
    right: 60px;
}

.subtitle {
    font-size: 16px;
    font-weight: 300;
    color: #888888;
    line-height: 1.6;
    margin-bottom: 60px;
    max-width: 400px;
}

.main-title {
    font-size: 280px;
    font-weight: 400;
    color: #ffffff;
    line-height: 0.85;
    letter-spacing: -8px;
    margin-bottom: 0;
}

/* Gradient Image */
.gradient-image {
    position: absolute;
    bottom: 40px;
    left: 60px;
    right: 60px;
    height: 400px;
    border-radius: 24px;
    overflow: hidden;
}

.gradient-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="content">
        <div class="subtitle">This document sets out a full creative<br>brief for beginning a new project.</div>
        <div class="main-title">Brief.</div>
    </div>
    
    <div class="gradient-image">
        <img src="gradient-bg.png" alt="Gradient Background">
    </div>
</div>
</body>
</html>