<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    position: absolute;
    top: 40px;
    left: 60px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Gradient Background */
.gradient-background {
    position: absolute;
    top: 120px;
    left: 60px;
    right: 60px;
    bottom: 40px;
    border-radius: 24px;
    overflow: hidden;
}

.gradient-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Content Overlay */
.content-overlay {
    position: absolute;
    top: 120px;
    left: 60px;
    right: 60px;
    bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
}

.section-number {
    font-size: 48px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 40px;
    letter-spacing: 2px;
}

.main-title {
    font-size: 220px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    letter-spacing: -6px;
    margin-bottom: 60px;
}

.section-description {
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    line-height: 1.6;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="gradient-background">
        <img src="gradient-audience.png" alt="Gradient Background">
    </div>
    
    <div class="content-overlay">
        <div class="section-number">(02)</div>
        <div class="main-title">Audience</div>
        <div class="section-description">
            In this section:<br>
            we'll identify our target audience and their needs
        </div>
    </div>
</div>
</body>
</html>