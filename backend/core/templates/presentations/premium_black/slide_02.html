<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SlideKit Presentation System 2025 - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #000000;
    color: #ffffff;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    background: #000000;
    padding: 40px 60px;
}

/* Header Navigation */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 80px;
}

.brand {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
}

.year {
    font-size: 18px;
    font-weight: 400;
    color: #888888;
    letter-spacing: 0.3px;
}

/* Main Content */
.main-content {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 120px);
}

/* Left Side - Gradient Image */
.left-side {
    width: 280px;
    height: 100%;
}

.gradient-image {
    width: 100%;
    height: 100%;
    border-radius: 24px;
    overflow: hidden;
}

.gradient-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Right Side - Index List */
.right-side {
    width: 1000px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.index-list {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.index-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #333333;
    padding-bottom: 30px;
}

.index-title {
    font-size: 90px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: -2px;
    line-height: 1;
}

.index-number {
    font-size: 90px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: -2px;
    line-height: 1;
}
</style>

<div class="slide-container">
    <div class="header">
        <div class="brand">SlideKit™</div>
        <div class="year">Presentation System 2025</div>
    </div>
    
    <div class="main-content">
        <div class="left-side">
            <div class="gradient-image">
                <img src="gradient-bg.png" alt="Gradient Background">
            </div>
        </div>
        
        <div class="right-side">
            <div class="index-list">
                <div class="index-item">
                    <div class="index-title">Goals</div>
                    <div class="index-number">1</div>
                </div>
                <div class="index-item">
                    <div class="index-title">Audience</div>
                    <div class="index-number">2</div>
                </div>
                <div class="index-item">
                    <div class="index-title">Deliverables</div>
                    <div class="index-number">3</div>
                </div>
                <div class="index-item">
                    <div class="index-title">References</div>
                    <div class="index-number">4</div>
                </div>
                <div class="index-item">
                    <div class="index-title">Budget</div>
                    <div class="index-number">5</div>
                </div>
                <div class="index-item">
                    <div class="index-title">Timeline</div>
                    <div class="index-number">6</div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>