<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  background-color: #FFFFFF;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
  padding: 80px 100px;
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 60px;
  align-items: start;
}

.content-section {
  padding-right: 20px;
}

h1 {
  font-size: 54px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 40px;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.subtitle {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 15px;
  line-height: 1.3;
}

.description {
  font-size: 28px;
  font-weight: 400;
  color: #666666;
  line-height: 1.5;
  letter-spacing: -0.01em;
  margin-bottom: 40px;
}

.section-title {
  font-size: 32px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 15px;
  margin-top: 30px;
}

.section-text {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  line-height: 1.6;
  letter-spacing: -0.01em;
  margin-bottom: 25px;
}

.bullet-list {
  list-style: none;
  margin-bottom: 25px;
}

.bullet-list li {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  line-height: 1.6;
  letter-spacing: -0.01em;
  margin-bottom: 12px;
  padding-left: 30px;
  position: relative;
}

.bullet-list li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #000000;
  font-weight: 700;
  font-size: 28px;
}

.image-section {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
}

.case-study-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}
</style>

<div class="slide-container">
  <div class="content-section">
    <h1>Case Study 2 –<br>Product Discovery UX<br>for Shopora</h1>
    
    <div class="subtitle">Helping Users Find What They Love Faster</div>
    <div class="description">Improving product discovery for a Gen Z shopping app</div>
    
    <div class="section-title">Problem:</div>
    <p class="section-text">
      Gen Z users were abandoning search due to overwhelming filters and poor recommendations.
    </p>
    
    <div class="section-title">Research:</div>
    <ul class="bullet-list">
      <li>Ran card sorting + click testing</li>
      <li>Found that Gen Z preferred mood- and style-based browsing</li>
    </ul>
  </div>
  
  <div class="image-section">
    <img src="example2.png" alt="Shopora Case Study Screens" class="case-study-image">
  </div>
</div>
</body>
</html>