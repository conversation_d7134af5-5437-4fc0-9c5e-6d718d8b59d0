<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  background-color: #FFFFFF;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
  padding: 80px 100px;
}

h1 {
  font-size: 120px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 80px;
  letter-spacing: -0.02em;
}

.index-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px 120px;
}

.index-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 30px;
  border-bottom: 1px solid #E0E0E0;
}

.item-content {
  flex: 1;
  padding-right: 40px;
}

.item-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8px;
  line-height: 1.3;
}

.item-description {
  font-size: 18px;
  font-weight: 400;
  color: #666666;
  line-height: 1.5;
}

.item-number {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  flex-shrink: 0;
  min-width: 50px;
  text-align: right;
}
</style>

<div class="slide-container">
  <h1>Index</h1>
  
  <div class="index-grid">
    <!-- Left Column -->
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Design That Connects</div>
        <div class="item-description">A UX Portfolio by Aanya Dave</div>
      </div>
      <div class="item-number">01</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">About Me</div>
        <div class="item-description">A UX designer who blends empathy, strategy, and systems thinking</div>
      </div>
      <div class="item-number">02</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">My Design Philosophy</div>
        <div class="item-description">Design isn't just visual – it's functional, emotional, and inclusive.</div>
      </div>
      <div class="item-number">03</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">My UX Process</div>
        <div class="item-description">My approach to turning insights into experiences</div>
      </div>
      <div class="item-number">04</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Case Study 1 - Onboarding Redesign for FlexBank</div>
        <div class="item-description">Reducing drop-offs by simplifying the onboarding flow</div>
      </div>
      <div class="item-number">05</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Case Study 2 – Product Discovery UX for Shopora</div>
        <div class="item-description">Helping Users Find What They Love Faster</div>
      </div>
      <div class="item-number">06</div>
    </div>
    
    <!-- Right Column -->
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Design System & Visual Language</div>
        <div class="item-description">Design consistency = user trust</div>
      </div>
      <div class="item-number">07</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Collaboration & Delivery</div>
        <div class="item-description">Working cross-functionally for success</div>
      </div>
      <div class="item-number">08</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Feedback & Impact</div>
        <div class="item-description">What others say about working with me</div>
      </div>
      <div class="item-number">09</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">What I'm Looking For</div>
        <div class="item-description">Where I can create, collaborate, and grow</div>
      </div>
      <div class="item-number">10</div>
    </div>
    
    <div class="index-item">
      <div class="item-content">
        <div class="item-title">Thank You</div>
        <div class="item-description">Available for interviews, freelance projects, or full-time roles</div>
      </div>
      <div class="item-number">11</div>
    </div>
  </div>
</div>
</body>
</html>