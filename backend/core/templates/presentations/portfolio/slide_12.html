<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  background-color: #FFFFFF;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
  padding: 80px 100px;
}

h1 {
  font-size: 120px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.subtitle {
  font-size: 42px;
  font-weight: 600;
  color: #333333;
  font-style: italic;
  margin-bottom: 15px;
  line-height: 1.3;
}

.description {
  font-size: 32px;
  font-weight: 400;
  color: #666666;
  line-height: 1.5;
  letter-spacing: -0.01em;
  margin-bottom: 60px;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.testimonial-card {
  display: flex;
  flex-direction: column;
}

.card-image {
  width: 100%;
  height: 280px;
  border-radius: 15px;
  object-fit: cover;
  margin-bottom: 25px;
}

.card-quote {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  line-height: 1.6;
  letter-spacing: -0.01em;
  margin-bottom: 20px;
  flex-grow: 1;
}

.card-author {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  line-height: 1.4;
}
</style>

<div class="slide-container">
  <h1>Feedback & Impact</h1>
  
  <p class="subtitle">Design That Made a Difference</p>
  
  <p class="description">What others say about working with me</p>
  
  <div class="testimonials-grid">
    <div class="testimonial-card">
      <img src="person100.png" alt="Testimonial" class="card-image">
      <p class="card-quote">
        [Designer Name] has a gift for turning complex user pain points into clean, usable solutions.
      </p>
      <p class="card-author">Neha Joshi, Product Manager at FlexBank</p>
    </div>
    
    <div class="testimonial-card">
      <img src="person100.png" alt="Testimonial" class="card-image">
      <p class="card-quote">
        [Designer Name] has a gift for turning complex user pain points into clean, usable solutions.
      </p>
      <p class="card-author">Amit B, Head of Growth at FlexBank</p>
    </div>
    
    <div class="testimonial-card">
      <img src="person100.png" alt="Testimonial" class="card-image">
      <p class="card-quote">
        [Designer Name] has a gift for turning complex user pain points into clean, usable solutions.
      </p>
      <p class="card-author">Neha Joshi, Product Manager at FlexBank</p>
    </div>
  </div>
</div>
</body>
</html>