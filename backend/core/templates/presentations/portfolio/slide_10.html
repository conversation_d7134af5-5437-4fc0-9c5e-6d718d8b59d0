<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  background-color: #FFFFFF;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
  padding: 80px 100px;
}

.header-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
  align-items: start;
}

h1 {
  font-size: 90px;
  font-weight: 800;
  color: #000000;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.right-content {
  padding-top: 10px;
}

.subtitle {
  font-size: 36px;
  font-weight: 600;
  color: #333333;
  font-style: italic;
  margin-bottom: 30px;
  line-height: 1.3;
}

.bullet-list {
  list-style: none;
}

.bullet-list li {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  line-height: 1.6;
  letter-spacing: -0.01em;
  margin-bottom: 12px;
  padding-left: 30px;
  position: relative;
}

.bullet-list li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #000000;
  font-weight: 700;
  font-size: 28px;
}

.image-section {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.design-system-image {
  width: 100%;
  max-width: 1700px;
  height: auto;
  object-fit: contain;
}
</style>

<div class="slide-container">
  <div class="header-section">
    <div class="left-content">
      <h1>Design System<br>& Visual<br>Language</h1>
    </div>
    
    <div class="right-content">
      <p class="subtitle">Building Systems That Scale Design consistency = user trust</p>
      
      <ul class="bullet-list">
        <li>Built FlexBank's internal design system ("FlexKit")</li>
        <li>Components: Inputs, buttons, modals, cards, color styles</li>
        <li>Used variants & auto-layout for efficiency</li>
        <li>Improved design-dev turnaround time by 35%</li>
      </ul>
    </div>
  </div>
  
  <div class="image-section">
    <img src="example3.png" alt="Design System Components" class="design-system-image">
  </div>
</div>
</body>
</html>