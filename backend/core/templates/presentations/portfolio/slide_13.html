<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 13</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Rubik', sans-serif;
  background-color: #FFFFFF;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  position: relative;
}

.slide-container {
  width: 100%;
  height: 100%;
  padding: 80px 100px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: stretch;
}

.content-section {
  padding-right: 40px;
  display: flex;
  flex-direction: column;
}

h1 {
  font-size: 90px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 40px;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.subtitle {
  font-size: 42px;
  font-weight: 600;
  color: #333333;
  font-style: italic;
  margin-bottom: 20px;
  line-height: 1.3;
}

.description {
  font-size: 24px;
  font-weight: 400;
  color: #666666;
  line-height: 1.5;
  letter-spacing: -0.01em;
  margin-bottom: 60px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.icon {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  width: 40px;
  height: 40px;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 28px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 8px;
  line-height: 1.3;
}

.info-text {
  font-size: 24px;
  font-weight: 400;
  color: #333333;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.image-section {
  display: flex;
  align-items: stretch;
  justify-content: center;
}

.selfie-image {
  width: 100%;
  height: 100%;
  border-radius: 30px;
  object-fit: cover;
}
</style>

<div class="slide-container">
  <div class="content-section">
    <h1>What I'm<br>Looking For</h1>
    
    <p class="subtitle">Seeking My Next Design Challenge</p>
    
    <p class="description">Where I can create, collaborate, and grow</p>
    
    <div class="info-list">
      <div class="info-item">
        <div class="icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2"/>
            <path d="M9 11l3 3 5-5"/>
          </svg>
        </div>
        <div class="info-content">
          <div class="info-title">Role</div>
          <div class="info-text">UX/Product Designer</div>
        </div>
      </div>
      
      <div class="info-item">
        <div class="icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
            <circle cx="12" cy="10" r="3"/>
          </svg>
        </div>
        <div class="info-content">
          <div class="info-title">Location</div>
          <div class="info-text">Remove/London</div>
        </div>
      </div>
      
      <div class="info-item">
        <div class="icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="8" r="5"/>
            <path d="M20 21a8 8 0 1 0-16 0"/>
          </svg>
        </div>
        <div class="info-content">
          <div class="info-title">Culture</div>
          <div class="info-text">Mission-driven team, fast iteration, strong design leadership</div>
        </div>
      </div>
      
      <div class="info-item">
        <div class="icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="7" height="7"/>
            <rect x="14" y="3" width="7" height="7"/>
            <rect x="14" y="14" width="7" height="7"/>
            <rect x="3" y="14" width="7" height="7"/>
          </svg>
        </div>
        <div class="info-content">
          <div class="info-title">Interests</div>
          <div class="info-text">Fintech, health tech, edtech, and B2C apps</div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="image-section">
    <img src="selfie.png" alt="Designer Portrait" class="selfie-image">
  </div>
</div>
</body>
</html>