<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketing Strategy Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Gupter:wght@400;500;700&family=Libre+Baskerville:wght@400;700&family=Inter:wght@300;400;500;600&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    .slide-container {
        width: 1920px;
        height: 1080px;
        background: #D0FFCD;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 80px 100px;
        position: relative;
        overflow: hidden;
    }
    
    .content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 20px;
    }
    
    .text-content {
        display: flex;
        align-items: flex-start;
        gap: 30px;
        max-width: 1400px;
    }
    
    .arrow {
        font-family: 'Inter', sans-serif;
        font-size: 48px;
        color: #9B9886;
        line-height: 1;
        margin-top: 8px;
        flex-shrink: 0;
    }
    
    .description {
        font-family: 'Gupter', 'Libre Baskerville', serif;
        font-size: 64px;
        font-weight: 400;
        color: #9B9886;
        line-height: 60px;
        letter-spacing: -0.05em;
    }
    
    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Inter', sans-serif;
        font-size: 28px;
        font-weight: 400;
        color: #9B9886;
        letter-spacing: 1px;
        text-transform: uppercase;
        padding-top: 40px;
    }
</style>

<div class="slide-container">
    <div class="content-area">
        <div class="text-content">
            <div class="arrow">▶</div>
            <div class="description">
                Add a quick description of the purpose of this marketing strategy—like the team, project, and dates. Keep it short and sweet, since you'll dive into the details later in the deck.
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div>QX MARKETING STRATEGY</div>
        <div>COMPANY NAME</div>
    </div>
</div>
</body>
</html>