<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketing Strategy Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Gupter:wght@400;500;700&family=Libre+Baskerville:wght@400;700&family=Inter:wght@300;400;500;600&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    .slide-container {
        width: 1920px;
        height: 1080px;
        background: #F5F3EE;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 80px 100px;
        position: relative;
        overflow: hidden;
    }
    
    .header {
        font-family: 'Inter', sans-serif;
        font-size: 32px;
        font-weight: 400;
        color: #9B9886;
        letter-spacing: 2px;
        text-transform: uppercase;
        margin-bottom: 60px;
    }
    
    .content-area {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 0;
        position: relative;
    }
    
    .column {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0 50px;
        position: relative;
    }
    
    .column:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 100px;
        width: 1px;
        background: #9B9886;
        opacity: 0.3;
    }
    
    .description {
        font-family: 'Gupter', 'Libre Baskerville', serif;
        font-size: 48px;
        font-weight: 400;
        color: #9B9886;
        line-height: 52px;
        letter-spacing: -0.02em;
        margin-bottom: 80px;
    }
    
    .stat {
        font-family: 'Gupter', 'Libre Baskerville', serif;
        font-size: 180px;
        font-weight: 400;
        color: #B8B3A6;
        letter-spacing: -0.03em;
        font-style: italic;
    }
    
    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Inter', sans-serif;
        font-size: 28px;
        font-weight: 400;
        color: #9B9886;
        letter-spacing: 1px;
        text-transform: uppercase;
        padding-top: 40px;
    }
</style>

<div class="slide-container">
    <div class="header">OPPORTUNITY</div>
    
    <div class="content-area">
        <div class="column">
            <div class="description">Add a quick description of the number below.</div>
            <div class="stat">XX%</div>
        </div>
        
        <div class="column">
            <div class="description">Keep it as short and sweet as possible.</div>
            <div class="stat">XX%</div>
        </div>
        
        <div class="column">
            <div class="description">Provide context so viewers "get it."</div>
            <div class="stat">XX%</div>
        </div>
    </div>
    
    <div class="footer">
        <div>QX MARKETING STRATEGY</div>
        <div>COMPANY NAME</div>
    </div>
</div>
</body>
</html>