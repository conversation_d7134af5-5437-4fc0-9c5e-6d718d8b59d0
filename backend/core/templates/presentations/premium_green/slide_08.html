<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketing Strategy Presentation - Slide 8</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Gupter:wght@400;500;700&family=Libre+Baskerville:wght@400;700&family=Inter:wght@300;400;500;600&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    .slide-container {
        width: 1920px;
        height: 1080px;
        background: #F5F3EE;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 80px 100px;
        position: relative;
        overflow: hidden;
    }
    
    .headers {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 100px;
        margin-bottom: 60px;
    }
    
    .header {
        font-family: 'Gupter', 'Libre Baskerville', serif;
        font-size: 32px;
        font-weight: 400;
        color: #9B9886;
        letter-spacing: 2px;
        text-transform: uppercase;
    }
    
    .header.right {
        text-align: right;
    }
    
    .content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        gap: 60px;
    }
    
    .row {
        display: grid;
        grid-template-columns: 1fr 200px 1fr;
        align-items: center;
        gap: 40px;
    }
    
    .pain-section, .benefit-section {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .pain-section {
        text-align: right;
    }
    
    .benefit-section {
        text-align: left;
    }
    
    .item-title {
        font-family: 'Gupter', 'Libre Baskerville', serif;
        font-size: 56px;
        font-weight: 400;
        color: #9B9886;
        line-height: 1.2;
        letter-spacing: -0.02em;
    }
    
    .item-description {
        font-family: 'Inter', sans-serif;
        font-size: 28px;
        font-weight: 400;
        color: #9B9886;
        line-height: 1.4;
    }
    
    .arrow-container {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .arrow {
        width: 100%;
        height: 1px;
        background: #9B9886;
        position: relative;
    }
    
    .arrow::after {
        content: '→';
        position: absolute;
        right: -10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 32px;
        color: #9B9886;
    }
    
    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Inter', sans-serif;
        font-size: 28px;
        font-weight: 400;
        color: #9B9886;
        letter-spacing: 1px;
        text-transform: uppercase;
        padding-top: 40px;
    }
</style>

<div class="slide-container">
    <div class="headers">
        <div class="header">PAIN POINTS</div>
        <div class="header right">BENEFITS</div>
    </div>
    
    <div class="content-area">
        <div class="row">
            <div class="pain-section">
                <div class="item-title">Pain point 1</div>
                <div class="item-description">Describe a customer problem or challenge.</div>
            </div>
            <div class="arrow-container">
                <div class="arrow"></div>
            </div>
            <div class="benefit-section">
                <div class="item-title">Benefit 1</div>
                <div class="item-description">Then describe how your product solves it.</div>
            </div>
        </div>
        
        <div class="row">
            <div class="pain-section">
                <div class="item-title">Pain point 2</div>
                <div class="item-description">Describe a customer problem or challenge.</div>
            </div>
            <div class="arrow-container">
                <div class="arrow"></div>
            </div>
            <div class="benefit-section">
                <div class="item-title">Benefit 2</div>
                <div class="item-description">Then describe how your product solves it.</div>
            </div>
        </div>
        
        <div class="row">
            <div class="pain-section">
                <div class="item-title">Pain point 3</div>
                <div class="item-description">Describe a customer problem or challenge.</div>
            </div>
            <div class="arrow-container">
                <div class="arrow"></div>
            </div>
            <div class="benefit-section">
                <div class="item-title">Benefit 3</div>
                <div class="item-description">Then describe how your product solves it.</div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div>QX MARKETING STRATEGY</div>
        <div>COMPANY NAME</div>
    </div>
</div>
</body>
</html>