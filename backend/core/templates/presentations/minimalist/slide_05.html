<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px;
    font-family: 'Lora', Georgia, serif;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  
  .content-area {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 700px;
    gap: 80px;
    align-items: center;
  }
  
  .left-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 40px;
  }
  
  .main-heading {
    font-size: 100px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 60px;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .description {
    font-size: 24px;
    font-weight: 400;
    color: #bdc3c7;
    line-height: 1.8;
    max-width: 700px;
  }
  
  .right-section {
    width: 500px;
    height: 490px;
    position: relative;
  }
  
  .image-grid {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 250px 250px;
    grid-template-rows: 240px 250px;
    gap: 0;
  }
  
  .image-box {
    position: relative;
    overflow: hidden;
  }
  
  .image-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .placeholder {
    background: #f5f1ed;
  }
  
  .box-top-left {
    grid-column: 1;
    grid-row: 1;
    width: 200px;
    height: 200px;
    justify-self: end;
    align-self: end;
  }
  
  .box-top-right {
    grid-column: 2;
    grid-row: 1;
    width: 250px;
    height: 240px;
    justify-self: start;
    align-self: end;
  }
  
  .box-bottom-left {
    grid-column: 1;
    grid-row: 2;
    width: 250px;
    height: 250px;
    justify-self: end;
    align-self: start;
  }
  
  .box-bottom-right {
    grid-column: 2;
    grid-row: 2;
    width: 200px;
    height: 200px;
    justify-self: start;
    align-self: start;
  }
  
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-text {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .page-number {
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="content-area">
    <div class="left-section">
      <div class="main-heading">minimalism concept</div>
      <div class="description">
        Minimalism emerged in New York in the early 1960s among artists who were self-consciously renouncing recent art they thought had become stale and academic.
      </div>
    </div>
    
    <div class="right-section">
      <div class="image-grid">
        <div class="image-box box-top-left">
          <img src="https://m.media-amazon.com/images/I/71J-X4b2zJL.jpg" alt="Abstract minimalist design">
        </div>
        
        <div class="image-box box-top-right placeholder"></div>
        
        <div class="image-box box-bottom-left placeholder"></div>
        
        <div class="image-box box-bottom-right">
          <img src="https://i.etsystatic.com/26244134/r/il/f59ae6/3299853695/il_570xN.3299853695_5wih.jpg" alt="Minimalist texture">
        </div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <div class="footer-text">Minim Company</div>
    <div class="page-number">6</div>
  </div>
</div>
</body>
</html>