<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
    font-family: 'Lora', Georgia, serif;
    position: relative;
  }
  
  .left-section {
    width: 27%;
    background: #d3d3d3;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 28px;
    color: #999999;
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
  }
  
  .right-section {
    flex: 1;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 80px;
  }
  
  .content-center {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-heading {
    font-size: 180px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    margin-bottom: 30px;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .subtitle-line-wrapper {
    display: flex;
    align-items: center;
    gap: 30px;
  }
  
  .subtitle {
    font-size: 32px;
    font-weight: 400;
    color: #95a5a6;
  }
  
  .divider {
    width: 140px;
    height: 3px;
    background: #ffffff;
  }
  
  .page-number {
    position: absolute;
    bottom: 80px;
    right: 100px;
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <div class="placeholder-text">Image Placeholder</div>
  </div>
  
  <div class="right-section">
    <div class="content-center">
      <div class="main-heading">Team</div>
      <div class="subtitle-line-wrapper">
        <div class="subtitle">Our Amazing Team</div>
        <div class="divider"></div>
      </div>
    </div>
    <div class="page-number">7</div>
  </div>
</div>
</body>
</html>