<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
    font-family: 'Lora', Georgia, serif;
    position: relative;
  }
  
  .left-section {
    width: 50%;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .left-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .subtitle {
    font-size: 28px;
    font-weight: 400;
    color: #95a5a6;
    margin-bottom: 30px;
  }
  
  .main-heading {
    font-size: 110px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 50px;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .divider {
    width: 200px;
    height: 3px;
    background: #ffffff;
    margin-bottom: 50px;
  }
  
  .description {
    font-size: 24px;
    font-weight: 400;
    color: #bdc3c7;
    line-height: 1.8;
    max-width: 600px;
  }
  
  .footer-left {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
    margin-top: 40px;
  }
  
  .right-section {
    width: 50%;
    background: #f5f1ed;
    display: flex;
    flex-direction: column;
  }
  
  .quote-area {
    flex: 1;
    padding: 100px 120px 60px 120px;
    display: flex;
    align-items: center;
  }
  
  .quote-text {
    font-size: 36px;
    font-weight: 400;
    color: #95a5a6;
    line-height: 1.6;
    font-style: italic;
    font-family: 'Lora', Georgia, serif;
  }
  
  .placeholder-area {
    height: 400px;
    background: #d3d3d3;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 32px;
    color: #999999;
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <div class="left-content">
      <div class="subtitle">Subtitle Here</div>
      <div class="main-heading">our mission</div>
      <div class="divider"></div>
      <div class="description">
        Minimalism emerged in New York in the early 1960s among artists who were self-consciously renouncing recent art they thought had become stale and academic.
      </div>
    </div>
    <div class="footer-left">Minim Company</div>
  </div>
  
  <div class="right-section">
    <div class="quote-area">
      <div class="quote-text">
        "Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos quiamet"
      </div>
    </div>
    <div class="placeholder-area">
      <div class="placeholder-text">Image Placeholder</div>
    </div>
  </div>
</div>
</body>
</html>