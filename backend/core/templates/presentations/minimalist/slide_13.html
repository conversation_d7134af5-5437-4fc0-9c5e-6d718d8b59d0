<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 13</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
    font-family: 'Lora', Georgia, serif;
    position: relative;
    background: #f5f1ed;
  }
  
  .left-section {
    width: 50%;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px 40px 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }
  
  .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .small-title {
    font-size: 32px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 30px;
  }
  
  .main-heading {
    font-size: 140px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.1;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .footer-left {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .right-section {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px;
  }
  
  .contact-card {
    background: #ffffff;
    padding: 80px 100px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 50px;
  }
  
  .contact-item {
    display: flex;
    align-items: center;
    gap: 30px;
  }
  
  .icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: #2c3e50;
  }
  
  .contact-text {
    font-size: 26px;
    font-weight: 400;
    color: #2c3e50;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <div class="content-wrapper">
      <div class="small-title">Contact Us</div>
      <div class="main-heading">get in touch</div>
    </div>
    <div class="footer-left">Minim Company</div>
  </div>
  
  <div class="right-section">
    <div class="contact-card">
      <div class="contact-item">
        <div class="icon">👤</div>
        <div class="contact-text">Russel Blue</div>
      </div>
      
      <div class="contact-item">
        <div class="icon">📍</div>
        <div class="contact-text">266 Mutton Town Road, South Dakota, 57551</div>
      </div>
      
      <div class="contact-item">
        <div class="icon">📞</div>
        <div class="contact-text">360-638-1700</div>
      </div>
      
      <div class="contact-item">
        <div class="icon">✉️</div>
        <div class="contact-text"><EMAIL></div>
      </div>
    </div>
  </div>
</div>
</body>
</html>