<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px 40px 100px;
    font-family: 'Lora', Georgia, serif;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  
  .top-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    margin-bottom: 60px;
  }
  
  .top-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .subtitle {
    font-size: 28px;
    font-weight: 400;
    color: #95a5a6;
    margin-bottom: 30px;
  }
  
  .main-heading {
    font-size: 100px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 40px;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .description {
    font-size: 22px;
    font-weight: 400;
    color: #bdc3c7;
    line-height: 1.8;
  }
  
  .top-right {
    width: 100%;
    height: 400px;
    overflow: hidden;
  }
  
  .top-right img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .bottom-section {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding-bottom: 90px;
  }
  
  .bottom-content {
    width: 50%;
    padding-left: 80px;
  }
  
  .bottom-heading {
    font-size: 38px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 25px;
    font-family: 'Lora', Georgia, serif;
  }
  
  .bottom-text {
    font-size: 22px;
    font-weight: 400;
    color: #bdc3c7;
    line-height: 1.8;
  }
  
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-text {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .page-number {
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="top-section">
    <div class="top-left">
      <div class="subtitle">Our Services</div>
      <div class="main-heading">interior design</div>
      <div class="description">
        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta.
      </div>
    </div>
    
    <div class="top-right">
      <img src="https://www.decorilla.com/online-decorating/wp-content/uploads/2024/09/Minimalist-living-room-design-by-Decorilla-2-scaled.jpeg" alt="Modern minimalist interior design">
    </div>
  </div>
  
  <div class="bottom-section">
    <div class="bottom-content">
      <div class="bottom-heading">Lorem ipsum dolor</div>
      <div class="bottom-text">
        Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non.
      </div>
    </div>
  </div>
  
  <div class="footer">
    <div class="footer-text">Minim Company</div>
    <div class="page-number">13</div>
  </div>
</div>
</body>
</html>