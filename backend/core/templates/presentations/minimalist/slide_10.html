<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
    font-family: 'Lora', Georgia, serif;
    position: relative;
  }
  
  .left-section {
    flex: 1;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 80px 100px;
    position: relative;
  }
  
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 40px;
  }
  
  .main-heading {
    font-size: 180px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .subtitle-line-wrapper {
    display: flex;
    align-items: center;
    gap: 30px;
  }
  
  .subtitle {
    font-size: 32px;
    font-weight: 400;
    color: #95a5a6;
  }
  
  .divider {
    width: 280px;
    height: 3px;
    background: #ffffff;
  }
  
  .footer {
    position: absolute;
    bottom: 80px;
    left: 100px;
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .right-section {
    width: 50%;
    background: #d3d3d3;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 28px;
    color: #999999;
    font-weight: 400;
    letter-spacing: 2px;
    text-transform: uppercase;
  }
</style>

<div class="slide-container">
  <div class="left-section">
    <div class="content-wrapper">
      <div class="main-heading">Services</div>
      <div class="subtitle-line-wrapper">
        <div class="subtitle">Our Services</div>
        <div class="divider"></div>
      </div>
    </div>
    <div class="footer">Minim Company</div>
  </div>
  
  <div class="right-section">
    <div class="placeholder-text">Image Placeholder</div>
  </div>
</div>
</body>
</html>