<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
    flex-direction: column;
    font-family: 'Lora', Georgia, serif;
  }
  
  .top-section {
    height: 120px;
    background: #f5f1ed;
  }
  
  .bottom-section {
    flex: 1;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px;
    display: flex;
    gap: 100px;
    position: relative;
  }
  
  .left-section {
    flex: 0 0 500px;
    display: flex;
    flex-direction: column;
    gap: 40px;
  }
  
  .profile-photo {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: #d3d3d3;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 18px;
    color: #999999;
    font-weight: 400;
    letter-spacing: 1px;
    text-transform: uppercase;
  }
  
  .profile-name {
    font-size: 72px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.2;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -1px;
  }
  
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
  }
  
  .contact-item {
    font-size: 22px;
    font-weight: 400;
    color: #95a5a6;
  }
  
  .right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 40px;
  }
  
  .job-title {
    font-size: 48px;
    font-weight: 400;
    color: #ffffff;
    font-family: 'Playfair Display', Georgia, serif;
  }
  
  .bio-text {
    font-size: 22px;
    font-weight: 400;
    color: #bdc3c7;
    line-height: 1.8;
  }
  
  .footer {
    position: absolute;
    bottom: 80px;
    left: 100px;
    right: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-text {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .page-number {
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="top-section"></div>
  
  <div class="bottom-section">
    <div class="left-section">
      <div class="profile-photo">
        <div class="placeholder-text">Photo</div>
      </div>
      
      <div class="profile-name">jason blue</div>
      
      <div class="contact-info">
        <div class="contact-item">@jasonb</div>
        <div class="contact-item">linkedin.com/in/jasonblue</div>
        <div class="contact-item"><EMAIL></div>
      </div>
    </div>
    
    <div class="right-section">
      <div class="job-title">Creative Director</div>
      
      <div class="bio-text">
        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non.
      </div>
      
      <div class="bio-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      </div>
    </div>
    
    <div class="footer">
      <div class="footer-text">Minim Company</div>
      <div class="page-number">11</div>
    </div>
  </div>
</div>
</body>
</html>