<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px;
    font-family: 'Lora', Georgia, serif;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  
  .content-area {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 700px;
    gap: 80px;
    align-items: center;
  }
  
  .left-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .small-title {
    font-size: 28px;
    font-weight: 400;
    color: #95a5a6;
    margin-bottom: 30px;
  }
  
  .main-heading {
    font-size: 100px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.1;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -2px;
  }
  
  .right-section {
    width: 500px;
    height: 490px;
    position: relative;
  }
  
  .team-grid {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 250px 250px;
    grid-template-rows: 240px 250px;
    gap: 0;
  }
  
  .team-box {
    position: relative;
    overflow: hidden;
  }
  
  .team-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .placeholder {
    background: #f5f1ed;
  }
  
  .box-top-left {
    grid-column: 1;
    grid-row: 1;
    width: 200px;
    height: 200px;
    justify-self: end;
    align-self: end;
  }
  
  .box-top-right {
    grid-column: 2;
    grid-row: 1;
    width: 250px;
    height: 240px;
    justify-self: start;
    align-self: end;
  }
  
  .box-bottom-left {
    grid-column: 1;
    grid-row: 2;
    width: 250px;
    height: 250px;
    justify-self: end;
    align-self: start;
  }
  
  .box-bottom-right {
    grid-column: 2;
    grid-row: 2;
    width: 200px;
    height: 200px;
    justify-self: start;
    align-self: start;
  }
  
  .team-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px 25px;
    background: linear-gradient(to top, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.5) 70%, transparent 100%);
    color: #ffffff;
  }
  
  .team-name {
    font-size: 22px;
    font-weight: 400;
    margin-bottom: 3px;
  }
  
  .team-role {
    font-size: 16px;
    font-weight: 400;
    color: #e0e0e0;
  }
  
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-text {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .page-number {
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="content-area">
    <div class="left-section">
      <div class="small-title">The Pitch Founder</div>
      <div class="main-heading">meet the founders</div>
    </div>
    
    <div class="right-section">
      <div class="team-grid">
        <div class="team-box box-top-left placeholder"></div>
        
        <div class="team-box box-top-right">
          <img src="https://heroshotphotography.com/wp-content/uploads/2023/03/male-linkedin-corporate-headshot-on-white-square-1024x1024.jpg" alt="Dave Smith">
          <div class="team-info">
            <div class="team-name">Dave Smith</div>
            <div class="team-role">Co-Founder</div>
          </div>
        </div>
        
        <div class="team-box box-bottom-left">
          <img src="https://portraitpal.ai/wp-content/uploads/2024/08/corporate-headshot.jpg" alt="Jason Blue">
          <div class="team-info">
            <div class="team-name">Jason Blue</div>
            <div class="team-role">Founder</div>
          </div>
        </div>
        
        <div class="team-box box-bottom-right placeholder"></div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <div class="footer-text">Minim Company</div>
    <div class="page-number">8</div>
  </div>
</div>
</body>
</html>