<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minim - Minimalist Presentation Template - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Lora:wght@400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .slide-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 100px 40px 100px;
    font-family: 'Lora', Georgia, serif;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  
  .header {
    margin-bottom: 60px;
  }
  
  .logo {
    font-size: 72px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 20px;
    font-family: 'Playfair Display', Georgia, serif;
    letter-spacing: -1px;
  }
  
  .page-title {
    font-size: 42px;
    font-weight: 400;
    color: #ffffff;
    font-family: 'Lora', Georgia, serif;
  }
  
  .content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 60px;
    flex: 1;
    align-items: center;
  }
  
  .left-content {
    color: #bdc3c7;
    font-size: 24px;
    line-height: 1.8;
    font-weight: 400;
  }
  
  .center-image {
    width: 100%;
    height: 500px;
    position: relative;
    overflow: hidden;
  }
  
  .center-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(230, 126, 34, 0.1) 100%);
    mix-blend-mode: multiply;
  }
  
  .right-content {
    padding-left: 40px;
    border-left: 3px solid rgba(255, 255, 255, 0.2);
  }
  
  .right-title {
    font-size: 38px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 30px;
    font-family: 'Lora', Georgia, serif;
  }
  
  .right-text {
    color: #bdc3c7;
    font-size: 22px;
    line-height: 1.8;
    font-weight: 400;
  }
  
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .footer-text {
    font-size: 20px;
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 3px;
    text-transform: uppercase;
  }
  
  .page-number {
    font-size: 32px;
    font-weight: 400;
    color: #7f8c8d;
  }
</style>

<div class="slide-container">
  <div class="header">
    <div class="logo">minim</div>
    <div class="page-title">About Our Company</div>
  </div>
  
  <div class="content-wrapper">
    <div class="left-content">
      Minimalism is an art movement that began in post–World War II Western art, most strongly with American visual arts in the 1960s and early 1970s.
    </div>
    
    <div class="center-image">
      <img src="https://riworkplace.com/wp-content/uploads/2021/03/The-Modern-Office-Design-Concept-of-Glass-Walls.jpg" alt="Modern office workspace">
      <div class="image-overlay"></div>
    </div>
    
    <div class="right-content">
      <div class="right-title">Lorem ipsum dolor</div>
      <div class="right-text">
        Minimalism is a tool to rid yourself of life's excess in favor of focusing on what's important—so you can find happiness, fulfillment, and freedom.
      </div>
    </div>
  </div>
  
  <div class="footer">
    <div class="footer-text">Minim Company</div>
    <div class="page-number">3</div>
  </div>
</div>
</body>
</html>