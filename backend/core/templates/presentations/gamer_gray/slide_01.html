<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fundamentals of Prompt Engineering - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Jersey+15&family=Instrument+Sans:wght@400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    background: #CFCFCF;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 120px;
    overflow: hidden;
}

.slide-container {
    max-width: 1400px;
}

.brain-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.divider-line {
    width: 400px;
    height: 3px;
    background: #000000;
    margin-bottom: 60px;
}

.main-heading {
    font-family: 'Jersey 15', sans-serif;
    font-size: 170px;
    line-height: 0.95;
    color: #000000;
    font-weight: 400;
    letter-spacing: -2px;
    margin-bottom: 40px;
    text-transform: uppercase;
}

.subtext {
    font-family: 'Instrument Sans', sans-serif;
    font-size: 25px;
    line-height: 1.4;
    color: #000000;
    font-weight: 400;
    max-width: 1200px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}
</style>

<div class="slide-container">
    <svg class="brain-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C10.8954 2 10 2.89543 10 4C10 4.55228 9.55228 5 9 5C7.89543 5 7 5.89543 7 7C7 7.55228 6.55228 8 6 8C4.89543 8 4 8.89543 4 10C4 11.1046 4.89543 12 6 12C6.55228 12 7 12.4477 7 13C7 14.1046 7.89543 15 9 15C9.55228 15 10 15.4477 10 16C10 17.1046 10.8954 18 12 18C13.1046 18 14 17.1046 14 16C14 15.4477 14.4477 15 15 15C16.1046 15 17 14.1046 17 13C17 12.4477 17.4477 12 18 12C19.1046 12 20 11.1046 20 10C20 8.89543 19.1046 8 18 8C17.4477 8 17 7.55228 17 7C17 5.89543 16.1046 5 15 5C14.4477 5 14 4.55228 14 4C14 2.89543 13.1046 2 12 2Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
        <path d="M9.5 10C9.5 10 10 11 12 11C14 11 14.5 10 14.5 10" stroke="#000000" stroke-width="2" stroke-linecap="round"/>
        <path d="M12 11V14" stroke="#000000" stroke-width="2" stroke-linecap="round"/>
    </svg>
    
    <div class="divider-line"></div>
    
    <h1 class="main-heading">FUNDAMENTALS OF<br>PROMPT ENGINEERING</h1>
    
    <p class="subtext">LEARN THESE ESSENTIAL TECHNIQUES TO WRITE EFFECTIVE PROMPTS THAT GET BETTER RESULTS FROM AI MODELS</p>
</div>
</body>
</html>