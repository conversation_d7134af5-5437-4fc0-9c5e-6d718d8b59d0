<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Unbounded:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 1920px;
    height: 1080px;
    background: #CFCFCF;
    padding: 70px 100px;
    overflow: hidden;
}

.main-heading {
    font-family: 'Unbounded', sans-serif;
    font-size: 90px;
    font-weight: 800;
    color: #000000;
    letter-spacing: -0.04em;
    margin-bottom: 15px;
    line-height: 1.1;
}

.subheading {
    font-family: 'Unbounded', sans-serif;
    font-size: 40px;
    font-weight: 700;
    color: #000000;
    letter-spacing: 0.02em;
    margin-bottom: 25px;
    text-transform: uppercase;
}

.description {
    font-family: 'Unbounded', sans-serif;
    font-size: 40px;
    font-weight: 300;
    color: #000000;
    line-height: 1.3;
    margin-bottom: 50px;
    max-width: 1700px;
    text-transform: uppercase;
}

.content-wrapper {
    display: flex;
    gap: 80px;
    align-items: flex-start;
}

.examples-section {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.example-box {
    width: 480px;
    background: #FFFFFF;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.example-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.example-icon {
    font-size: 24px;
}

.example-label {
    font-family: 'Unbounded', sans-serif;
    font-size: 20px;
    font-weight: 700;
    text-transform: uppercase;
}

.vague-label {
    color: #DC2626;
}

.specific-label {
    color: #16A34A;
}

.example-text {
    font-family: 'Unbounded', sans-serif;
    font-size: 20px;
    font-weight: 300;
    color: #4A5568;
    line-height: 1.5;
    font-style: italic;
}

.terminal-section {
    flex: 1;
}

.terminal-window {
    width: 100%;
    background: #2D3748;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.terminal-header {
    background: #1A202C;
    padding: 14px 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.terminal-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot-red { background: #FF5F56; }
.dot-yellow { background: #FFBD2E; }
.dot-green { background: #27C93F; }

.terminal-title {
    font-family: 'Unbounded', sans-serif;
    font-size: 13px;
    color: #A0AEC0;
    margin-left: 12px;
}

.terminal-content {
    padding: 30px;
    font-family: 'Courier New', monospace;
    font-size: 17px;
    line-height: 1.7;
    color: #E2E8F0;
    height: 580px;
}

.prompt-symbol {
    color: #68D391;
}

.prompt-label {
    color: #63B3ED;
}

.prompt-text {
    color: #E2E8F0;
}

.include-label {
    color: #F59E0B;
}

.bullet-item {
    color: #E2E8F0;
    padding-left: 20px;
}

.cursor {
    display: inline-block;
    width: 10px;
    height: 20px;
    background: #68D391;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}
</style>

<div>
    <h1 class="main-heading">Be explicit & detailed</h1>
    <h2 class="subheading">ELIMINATE AMBIGUITY</h2>
    <p class="description">AVOID AMBIGUITY BY BEING SPECIFIC ABOUT EXPECTATIONS. CLEAR INSTRUCTIONS LEAD TO BETTER RESULTS.</p>
    
    <div class="content-wrapper">
        <div class="examples-section">
            <div class="example-box">
                <div class="example-header">
                    <span class="example-icon">❌</span>
                    <div class="example-label vague-label">VAGUE EXAMPLE</div>
                </div>
                <p class="example-text">"Write something good"</p>
            </div>
            
            <div class="example-box">
                <div class="example-header">
                    <span class="example-icon">✅</span>
                    <div class="example-label specific-label">SPECIFIC EXAMPLE</div>
                </div>
                <p class="example-text">"Write a 300-word professional email that persuasively explains why our team needs additional budget for the marketing campaign"</p>
            </div>
        </div>
        
        <div class="terminal-section">
            <div class="terminal-window">
                <div class="terminal-header">
                    <div class="terminal-dot dot-red"></div>
                    <div class="terminal-dot dot-yellow"></div>
                    <div class="terminal-dot dot-green"></div>
                    <span class="terminal-title">AI Chat Interface</span>
                </div>
                <div class="terminal-content">
                    <div><span class="prompt-symbol">$</span> <span class="prompt-label">User Prompt:</span></div>
                    <div style="margin-top: 10px;"></div>
                    <div><span class="prompt-text">Write a 300-word professional email that</span></div>
                    <div><span class="prompt-text">persuasively explains why our team needs</span></div>
                    <div><span class="prompt-text">additional budget for the marketing campaign.</span></div>
                    <div style="margin-top: 15px;"></div>
                    <div><span class="include-label">Include:</span></div>
                    <div><span class="bullet-item">• Specific budget amount needed</span></div>
                    <div><span class="bullet-item">• Clear ROI projections</span></div>
                    <div><span class="bullet-item">• Timeline for implementation</span></div>
                    <div style="margin-top: 15px;"><span class="cursor"></span></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>