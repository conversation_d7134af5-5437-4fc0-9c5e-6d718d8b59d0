<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 5</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #DFDFDF;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    display: flex;
}

.left-section {
    background-color: #FFCD6C;
    width: 50%;
    padding: 70px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.percentage-large {
    font-size: 280px;
    font-weight: 400;
    color: #000;
    line-height: 0.9;
    letter-spacing: -0.02em;
}

.left-text {
    font-size: 32px;
    font-weight: 400;
    color: #000;
    line-height: 1.5;
}

.right-section {
    background-color: #FEF4E1;
    width: 50%;
    padding: 70px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.title {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.description {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
    margin-top: 30px;
}

.large-number {
    font-size: 340px;
    font-weight: 400;
    color: #000;
    line-height: 0.9;
    letter-spacing: -0.02em;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <div class="percentage-large">50%</div>
        <p class="left-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
    </div>
    
    <div class="right-section">
        <div>
            <h1 class="title">Key Market Trends<br>Affecting Our<br>Business</h1>
            <p class="description">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
        </div>
        <div class="large-number">721M</div>
    </div>
</div>
</body>
</html>