<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #DFDFDF;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    background-color: #DFDFDF;
    padding: 70px 70px 0 70px;
    display: flex;
    flex-direction: column;
}

.header {
    margin-bottom: 60px;
}

.header h1 {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1.1;
    letter-spacing: -0.02em;
    margin-bottom: 30px;
}

.header p {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
}

.chart-container {
    flex: 1;
    display: flex;
    gap: 20px;
    align-items: flex-end;
}

.bar-wrapper {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.bar-background {
    background-color: #CDFBE9;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.bar {
    background-color: #13EA94;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 30px 30px 30px;
}

.bar-1 {
    height: 90%;
}

.bar-2 {
    height: 60%;
}

.bar-3 {
    height: 50%;
}

.bar-4 {
    height: 56%;
}

.bar-5 {
    height: 70%;
}

.percentage {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1;
    letter-spacing: -0.02em;
}

.month {
    font-size: 32px;
    font-weight: 400;
    color: #000;
    line-height: 1;
}
</style>

<div class="slide-container">
    <div class="header">
        <h1>Business Growth and<br>Expansion Highlights</h1>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
    </div>
    
    <div class="chart-container">
        <div class="bar-wrapper">
            <div class="bar-background">
                <div class="bar bar-1">
                    <div class="percentage">90%</div>
                    <div class="month">Aug</div>
                </div>
            </div>
        </div>
        
        <div class="bar-wrapper">
            <div class="bar-background">
                <div class="bar bar-2">
                    <div class="percentage">60%</div>
                    <div class="month">Sept</div>
                </div>
            </div>
        </div>
        
        <div class="bar-wrapper">
            <div class="bar-background">
                <div class="bar bar-3">
                    <div class="percentage">50%</div>
                    <div class="month">Oc</div>
                </div>
            </div>
        </div>
        
        <div class="bar-wrapper">
            <div class="bar-background">
                <div class="bar bar-4">
                    <div class="percentage">56%</div>
                    <div class="month">Nov</div>
                </div>
            </div>
        </div>
        
        <div class="bar-wrapper">
            <div class="bar-background">
                <div class="bar bar-5">
                    <div class="percentage">70%</div>
                    <div class="month">Des</div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>