<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #DFDFDF;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    background-color: #DFDFDF;
    padding: 70px 70px 0 70px;
    display: flex;
    flex-direction: column;
}

.header {
    margin-bottom: 60px;
}

.header h1 {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1.1;
    letter-spacing: -0.02em;
    margin-bottom: 30px;
}

.header p {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
}

.content-area {
    flex: 1;
    display: flex;
    gap: 0;
    align-items: flex-end;
}

.text-box {
    background-color: #FFFFFF;
    padding: 50px;
    width: 400px;
    align-self: stretch;
    display: flex;
    align-items: center;
}

.text-box p {
    font-size: 32px;
    font-weight: 400;
    color: #000;
    line-height: 1.5;
}

.bars-container {
    flex: 1;
    display: flex;
    gap: 0;
    height: 100%;
    align-items: flex-end;
}

.bar-column {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.bar {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 30px 30px 30px;
}

.bar-yellow {
    background-color: #FFCD6C;
    height: 55%;
}

.bar-purple {
    background-color: #A391FF;
    height: 48%;
}

.bar-green {
    background-color: #13EA94;
    height: 62%;
}

.percentage {
    font-size: 96px;
    font-weight: 400;
    color: #000;
    line-height: 1;
    letter-spacing: -0.02em;
}

.label {
    font-size: 24px;
    font-weight: 400;
    color: #000;
    line-height: 1.3;
}
</style>

<div class="slide-container">
    <div class="header">
        <h1>Sales Target Achieved<br>Detail Review</h1>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit,<br>sed do eiusmod  tempor incididunt.</p>
    </div>
    
    <div class="content-area">
        <div class="text-box">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
        </div>
        
        <div class="bars-container">
            <div class="bar-column">
                <div class="bar bar-yellow">
                    <div class="percentage">80%</div>
                    <div class="label">Lorem ipsum dolor</div>
                </div>
            </div>
            
            <div class="bar-column">
                <div class="bar bar-purple">
                    <div class="percentage">70%</div>
                    <div class="label">Lorem ipsum dolor</div>
                </div>
            </div>
            
            <div class="bar-column">
                <div class="bar bar-green">
                    <div class="percentage">90%</div>
                    <div class="label">Lorem ipsum dolor</div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>