<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #DFDFDF;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    background-color: #DFDFDF;
    padding: 70px;
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 60px;
}

.header h1 {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1.1;
    letter-spacing: -0.02em;
    margin-bottom: 30px;
}

.header p {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
}

.cards-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.cards-container {
    height: calc(100% - 100px);
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
}

.card {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-purple {
    background-color: #A391FF;
    grid-column: 1;
    grid-row: 1 / 3;
}

.card-green {
    background-color: #13EA94;
    grid-column: 2;
    grid-row: 1 / 3;
}

.card-yellow {
    background-color: #FFCD6C;
    grid-column: 3;
    grid-row: 1;
}

.card-white {
    background-color: #FFFFFF;
    grid-column: 3;
    grid-row: 2;
}

.card-label {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
}

.card-number {
    font-size: 140px;
    font-weight: 400;
    color: #000;
    line-height: 1;
    letter-spacing: -0.02em;
}

.card-purple .card-number {
    font-size: 200px;
}

.card-green .card-number {
    font-size: 120px;
}

.card-yellow .card-number {
    font-size: 120px;
}

.card-white .card-number {
    font-size: 120px;
}

.card-text {
    font-size: 24px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
}
</style>

<div class="slide-container">
    <div class="header">
        <h1>Product Launch Impact</h1>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod<br>tempor incididunt.</p>
    </div>
    
    <div class="cards-wrapper">
        <div class="cards-container">
            <div class="card card-purple">
                <div class="card-label">Lorem ipsum</div>
                <div class="card-number">721M</div>
            </div>
            
            <div class="card card-green">
                <div class="card-number">10,000</div>
                <div class="card-label">Lorem ipsum</div>
            </div>
            
            <div class="card card-yellow">
                <div class="card-number">3112K</div>
                <div class="card-text">Lorem ipsum dolor sit amet</div>
            </div>
            
            <div class="card card-white">
                <div class="card-number">4,876</div>
                <div class="card-text">Lorem ipsum dolor sit amet</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>