<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #DFDFDF;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-container {
    width: 1920px;
    height: 1080px;
    background-color: #DFDFDF;
    display: flex;
    padding: 70px;
    gap: 80px;
}

.left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.green-box {
    background-color: #13EA94;
    padding: 40px;
    height: 340px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.green-box .label {
    font-size: 24px;
    font-weight: 400;
    color: #000;
}

.green-box .number {
    font-size: 180px;
    font-weight: 400;
    color: #000;
    line-height: 0.9;
    letter-spacing: -0.02em;
}

.bottom-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 10px;
    flex: 1;
}

.stat-box {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.gray-box-1 {
    background-color: #D0CECE;
    grid-column: 1;
    grid-row: 1 / 3;
}

.yellow-box {
    background-color: #FFCD6C;
    grid-column: 2;
    grid-row: 1;
}

.white-box {
    background-color: #FFFFFF;
    grid-column: 2;
    grid-row: 2;
}

.stat-box .number {
    font-size: 96px;
    font-weight: 400;
    color: #000;
    line-height: 1;
    letter-spacing: -0.02em;
}

.stat-box .label {
    font-size: 24px;
    font-weight: 400;
    color: #000;
    margin-top: 20px;
}

.gray-box-1 .number {
    font-size: 120px;
}

.right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 0;
}

.title-area {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.title-area h1 {
    font-size: 72px;
    font-weight: 400;
    color: #000;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.title-area p {
    font-size: 28px;
    font-weight: 400;
    color: #000;
    line-height: 1.4;
    max-width: 700px;
}

.large-number {
    font-size: 340px;
    font-weight: 400;
    color: #000;
    line-height: 0.9;
    letter-spacing: -0.02em;
    text-align: left;
}
</style>

<div class="slide-container">
    <div class="left-section">
        <div class="green-box">
            <div class="label">Lorem ipsum</div>
            <div class="number">721M</div>
        </div>
        <div class="bottom-row">
            <div class="stat-box gray-box-1">
                <div class="number">10,000</div>
                <div class="label">Lorem ipsum</div>
            </div>
            <div class="stat-box yellow-box">
                <div class="number">3112K</div>
                <div class="label">Lorem ipsum dolor sit amet</div>
            </div>
            <div class="stat-box white-box">
                <div class="number">4,876</div>
                <div class="label">Lorem ipsum dolor sit amet</div>
            </div>
        </div>
    </div>
    
    <div class="right-section">
        <div class="title-area">
            <h1>Overview of Key<br>Yearly Achievements</h1>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt.</p>
        </div>
        <div class="large-number">721M</div>
    </div>
</div>
</body>
</html>