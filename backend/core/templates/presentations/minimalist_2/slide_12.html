<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calmness Presentation - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
  }
  
  .slide-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .top-section {
    flex: 0 0 auto;
    display: flex;
    padding: 110px 120px 80px 0;
  }
  
  .left-image {
    flex: 0 0 34%;
    height: 350px;
    background: 
      radial-gradient(circle, #a8b8d8 1px, transparent 1px),
      linear-gradient(135deg, #a8b8d8 0%, #8fa5c7 100%);
    background-size: 4px 4px, 100% 100%;
    background-position: 0 0, 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .right-content {
    flex: 1;
    padding-left: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .main-heading {
    font-size: 80px;
    font-weight: 500;
    color: #000000;
    line-height: 1.15;
    letter-spacing: -1.5px;
    margin-bottom: 50px;
  }
  
  .body-text {
    font-size: 17px;
    font-weight: 300;
    color: #666666;
    line-height: 1.8;
    letter-spacing: 0.3px;
    max-width: 700px;
  }
  
  .timeline-section {
    flex: 1;
    padding: 80px 120px 100px 120px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .timeline-item {
    flex: 0 0 auto;
    max-width: 400px;
  }
  
  .year-container {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .year {
    font-size: 42px;
    font-weight: 500;
    color: #000000;
    letter-spacing: -0.5px;
  }
  
  .year-line {
    width: 280px;
    height: 3px;
    background: #000000;
  }
  
  .timeline-text {
    font-size: 16px;
    font-weight: 300;
    color: #666666;
    line-height: 1.8;
    letter-spacing: 0.3px;
  }
</style>

<div class="slide-container">
  <div class="top-section">
    <div class="left-image">
      <div class="placeholder-text">placeholder image</div>
    </div>
    
    <div class="right-content">
      <h1 class="main-heading">The Evolution</h1>
      
      <p class="body-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus tempor commodo odio nec velit quis ismod semper ivamus odio tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semp erivamus tempor commodo odio nec velit quis ismod
      </p>
    </div>
  </div>
  
  <div class="timeline-section">
    <div class="timeline-item">
      <div class="year-container">
        <div class="year">1997</div>
        <div class="year-line"></div>
      </div>
      <p class="timeline-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus
      </p>
    </div>
    
    <div class="timeline-item">
      <div class="year-container">
        <div class="year">2010</div>
        <div class="year-line"></div>
      </div>
      <p class="timeline-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus
      </p>
    </div>
    
    <div class="timeline-item">
      <div class="year-container">
        <div class="year">2023</div>
        <div class="year-line"></div>
      </div>
      <p class="timeline-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus
      </p>
    </div>
  </div>
</div>
</body>
</html>