<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calmness Presentation - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #f5f5f5;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
  }
  
  .slide-container {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    padding: 60px 80px;
  }
  
  .header {
    position: absolute;
    top: 60px;
    left: 80px;
    right: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
  }
  
  .header-left {
    font-size: 18px;
    font-weight: 400;
    color: #1a1a1a;
    letter-spacing: 0.5px;
  }
  
  .header-right {
    font-size: 18px;
    font-weight: 400;
    color: #1a1a1a;
    letter-spacing: 0.5px;
  }
  
  .content-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 0;
    margin-top: 40px;
    position: relative;
  }
  
  .left-content {
    flex: 0 0 35%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-right: 40px;
    z-index: 5;
    position: relative;
  }
  
  .main-title {
    font-size: 140px;
    font-weight: 500;
    color: #000000;
    line-height: 1.1;
    margin-bottom: 180px;
    letter-spacing: -2px;
  }
  
  .subtitle-section {
    margin-top: auto;
  }
  
  .subtitle {
    font-size: 24px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 16px;
    letter-spacing: 0.3px;
  }
  
  .description {
    font-size: 16px;
    font-weight: 300;
    color: #666666;
    line-height: 1.6;
    max-width: 400px;
    letter-spacing: 0.2px;
  }
  
  .right-content {
    position: absolute;
    right: 0;
    top: 0;
    width: 65%;
    height: 100%;
    z-index: 1;
  }
  
  .image-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 0;
    background: 
      radial-gradient(circle, #a8b8d8 1px, transparent 1px),
      linear-gradient(135deg, #a8b8d8 0%, #8fa5c7 100%);
    background-size: 4px 4px, 100% 100%;
    background-position: 0 0, 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
</style>

<div class="slide-container">
  <div class="header">
    <div class="header-left">Minimalist Style</div>
    <div class="header-right">2023</div>
  </div>
  
  <div class="content-wrapper">
    <div class="right-content">
      <div class="image-container">
        <div class="placeholder-text">placeholder image</div>
      </div>
    </div>
    
    <div class="left-content">
      <div class="main-title">Calmness</div>
      <div class="subtitle-section">
        <div class="subtitle">Hallo Calmness !!!</div>
        <div class="description">
          Duis tincidunt mollis nisl nonng<br>
          ue lorem congue non In plaet<br>
          elit quis is ue lore congue.
        </div>
      </div>
    </div>
  </div>
</div>
</body>
</html>