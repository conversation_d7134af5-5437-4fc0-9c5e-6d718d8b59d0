<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calmness Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
  }
  
  .slide-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .top-section {
    width: 100%;
    height: 440px;
    overflow: hidden;
    position: relative;
    background: 
      radial-gradient(circle, #a8b8d8 1px, transparent 1px),
      linear-gradient(135deg, #a8b8d8 0%, #8fa5c7 100%);
    background-size: 4px 4px, 100% 100%;
    background-position: 0 0, 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .bottom-section {
    flex: 1;
    background: #f5f5f5;
    padding: 120px 120px 80px 120px;
    display: flex;
    align-items: flex-start;
    gap: 100px;
  }
  
  .left-column {
    flex: 0 0 auto;
    max-width: 600px;
  }
  
  .main-heading {
    font-size: 72px;
    font-weight: 500;
    color: #000000;
    line-height: 1.2;
    letter-spacing: -1px;
  }
  
  .right-column {
    flex: 1;
    max-width: 900px;
  }
  
  .body-text {
    font-size: 18px;
    font-weight: 300;
    color: #666666;
    line-height: 1.8;
    letter-spacing: 0.3px;
  }
</style>

<div class="slide-container">
  <div class="top-section">
    <div class="placeholder-text">placeholder image</div>
  </div>
  
  <div class="bottom-section">
    <div class="left-column">
      <h1 class="main-heading">Welcome to<br>Calmness</h1>
    </div>
    
    <div class="right-column">
      <p class="body-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus tempor commodo odio nec velit quis ismod semper ivamus odio tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semp erivamus tempor commodo odio nec velit quis ismod semper ivamus odio mollis nisl non ismod semper Vi
      </p>
    </div>
  </div>
</div>
</body>
</html>