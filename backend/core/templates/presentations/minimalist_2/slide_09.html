<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calmness Presentation - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #ffffff;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    position: relative;
  }
  
  .background-box {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background: #f5f5f5;
    z-index: 1;
  }
  
  .slide-container {
    width: 100%;
    height: 100%;
    padding: 80px 120px;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 5;
  }
  
  .top-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 60px;
  }
  
  .left-top {
    flex: 0 0 auto;
  }
  
  .main-heading {
    font-size: 72px;
    font-weight: 500;
    color: #000000;
    line-height: 1.15;
    letter-spacing: -1.5px;
  }
  
  .right-top {
    flex: 1;
    max-width: 800px;
    margin-left: 80px;
  }
  
  .body-text {
    font-size: 16px;
    font-weight: 300;
    color: #666666;
    line-height: 1.8;
    letter-spacing: 0.3px;
  }
  
  .label-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 50px;
  }
  
  .label-line {
    width: 80px;
    height: 2px;
    background: #000000;
  }
  
  .label-text {
    font-size: 18px;
    font-weight: 400;
    color: #000000;
    letter-spacing: 0.5px;
  }
  
  .team-cards {
    display: flex;
    gap: 60px;
    justify-content: flex-end;
    margin-left: auto;
  }
  
  .team-card {
    flex: 0 0 auto;
    width: 360px;
  }
  
  .card-image {
    width: 360px;
    height: 350px;
    background: 
      radial-gradient(circle, #a8b8d8 1px, transparent 1px),
      linear-gradient(135deg, #a8b8d8 0%, #8fa5c7 100%);
    background-size: 4px 4px, 100% 100%;
    background-position: 0 0, 0 0;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .placeholder-text {
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .card-name {
    font-size: 26px;
    font-weight: 500;
    color: #000000;
    letter-spacing: -0.5px;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .card-position {
    font-size: 15px;
    font-weight: 300;
    color: #999999;
    letter-spacing: 0.3px;
    text-align: center;
  }
</style>

<div class="background-box"></div>

<div class="slide-container">
  <div class="top-section">
    <div class="left-top">
      <h1 class="main-heading">Our Team<br>Calmness</h1>
    </div>
    <div class="right-top">
      <p class="body-text">
        Duis tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semper ivamus tempor commodo odio nec velit quis ismod semper ivamus odio tincidunt mollis nisl non congue lorem congue non In placerat velit quis ismod semp erivamus tempor commodo odio nec velit quis ismod semper ivamus odio mollis nisl non ismod semper Vivamus tempor
      </p>
    </div>
  </div>
  
  <div class="label-section">
    <div class="label-line"></div>
    <div class="label-text">Text Title</div>
  </div>
  
  <div class="team-cards">
    <div class="team-card">
      <div class="card-image">
        <div class="placeholder-text">placeholder image</div>
      </div>
      <div class="card-name">William Albern</div>
      <div class="card-position">Your Position</div>
    </div>
    
    <div class="team-card">
      <div class="card-image">
        <div class="placeholder-text">placeholder image</div>
      </div>
      <div class="card-name">Jhon Weak</div>
      <div class="card-position">Your Position</div>
    </div>
    
    <div class="team-card">
      <div class="card-image">
        <div class="placeholder-text">placeholder image</div>
      </div>
      <div class="card-name">Lord Gena</div>
      <div class="card-position">Your Position</div>
    </div>
  </div>
</div>
</body>
</html>