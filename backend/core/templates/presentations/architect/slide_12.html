<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
    }
    
    /* Left Image */
    .left-image {
        flex: 0 0 700px;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        position: relative;
        overflow: hidden;
    }
    
    .left-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80') center/cover;
        opacity: 0.2;
    }
    
    /* Right Content */
    .right-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 100px 120px 100px 100px;
    }
    
    .main-heading {
        font-size: 80px;
        font-weight: 400;
        color: #000;
        line-height: 1.2;
        letter-spacing: -2px;
        margin-bottom: 50px;
    }
    
    .description {
        font-size: 24px;
        font-weight: 400;
        color: #000;
        line-height: 1.7;
        letter-spacing: -0.3px;
        opacity: 0.85;
        margin-bottom: 80px;
    }
    
    .quality-badge {
        font-size: 56px;
        font-weight: 500;
        color: #FF6B35;
        letter-spacing: -1px;
        line-height: 1.3;
    }
</style>

<div class="container">
    <!-- Left Image -->
    <div class="left-image"></div>
    
    <!-- Right Content -->
    <div class="right-content">
        <div class="main-heading">
            Selected High<br>Quality Materials
        </div>
        <div class="description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus its netus quam fermenta ornare dapibus ornare conva egestas blandit adipiscing here.
        </div>
        <div class="quality-badge">
            100% Best Quality<br>Materials
        </div>
    </div>
</div>
</body>
</html>