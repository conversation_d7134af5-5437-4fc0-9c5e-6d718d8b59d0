<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Logo */
    .logo {
        position: absolute;
        top: 50px;
        left: 60px;
        font-size: 32px;
        font-weight: 400;
        color: #000;
        letter-spacing: -0.5px;
    }
    
    /* Content Layout */
    .content-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
    }
    
    /* Left Content Area */
    .left-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 120px;
        padding-right: 80px;
    }
    
    .intro-text {
        font-size: 48px;
        font-weight: 400;
        color: #FF6B35;
        margin-bottom: 30px;
        line-height: 1.3;
        letter-spacing: -0.5px;
    }
    
    .main-text {
        font-size: 64px;
        font-weight: 400;
        color: #000;
        line-height: 1.25;
        letter-spacing: -1.5px;
        max-width: 750px;
    }
    
    /* Right Image Area */
    .right-image {
        width: 600px;
        height: 100%;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        position: relative;
        overflow: hidden;
    }
    
    .right-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80') center/cover;
        opacity: 0.3;
    }
</style>

<div class="logo">Architec.</div>

<div class="content-container">
    <div class="left-content">
        <div class="intro-text">
            We don't just build buildings,
        </div>
        <div class="main-text">
            We build identity, function, and aesthetics in perfect harmony.
        </div>
    </div>
    
    <div class="right-image"></div>
</div>
</body>
</html>