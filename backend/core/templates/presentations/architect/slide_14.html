<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 14</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 60px 60px;
        display: flex;
        flex-direction: column;
    }
    
    /* Image Grid */
    .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        flex: 1;
        margin-bottom: 60px;
    }
    
    .portfolio-image {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        border-radius: 0;
        position: relative;
        overflow: hidden;
    }
    
    .portfolio-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80') center/cover;
        opacity: 0.2;
    }
    
    /* Bottom Content */
    .bottom-content {
        display: flex;
        gap: 80px;
        align-items: flex-start;
    }
    
    .portfolio-heading {
        font-size: 100px;
        font-weight: 400;
        color: #000;
        letter-spacing: -3px;
        line-height: 1;
        flex: 0 0 500px;
    }
    
    .portfolio-description {
        font-size: 28px;
        font-weight: 400;
        color: #000;
        line-height: 1.6;
        letter-spacing: -0.5px;
        opacity: 0.85;
        flex: 1;
        padding-top: 20px;
    }
</style>

<div class="container">
    <div class="image-grid">
        <div class="portfolio-image"></div>
        <div class="portfolio-image"></div>
        <div class="portfolio-image"></div>
    </div>
    
    <div class="bottom-content">
        <div class="portfolio-heading">
            Our Portfolio
        </div>
        <div class="portfolio-description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus.
        </div>
    </div>
</div>
</body>
</html>