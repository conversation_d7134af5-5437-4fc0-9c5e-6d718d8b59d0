<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 9</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Logo Bottom */
    .logo-bottom {
        position: absolute;
        bottom: 80px;
        left: 100px;
        font-size: 32px;
        font-weight: 400;
        color: #000;
        letter-spacing: -0.5px;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
        display: flex;
        gap: 100px;
    }
    
    /* Left Section */
    .left-section {
        flex: 0 0 500px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }
    
    .main-heading {
        font-size: 100px;
        font-weight: 400;
        color: #000;
        letter-spacing: -3px;
        line-height: 1.1;
        margin-bottom: 50px;
    }
    
    .subheading {
        font-size: 32px;
        font-weight: 400;
        color: #000;
        line-height: 1.5;
        letter-spacing: -0.5px;
        max-width: 450px;
    }
    
    /* Right Section - Items */
    .right-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 40px;
        padding-top: 20px;
    }
    
    .reason-item {
        display: flex;
        gap: 40px;
        align-items: flex-start;
        padding: 40px 50px;
        border-radius: 30px;
    }
    
    .reason-item.highlighted {
        background: #FF6B35;
    }
    
    .reason-number {
        font-size: 80px;
        font-weight: 700;
        letter-spacing: -2px;
        line-height: 1;
        flex-shrink: 0;
    }
    
    .reason-item:not(.highlighted) .reason-number {
        color: #FF6B35;
    }
    
    .reason-item.highlighted .reason-number {
        color: #FFF;
    }
    
    .reason-text {
        font-size: 24px;
        font-weight: 400;
        line-height: 1.6;
        letter-spacing: -0.3px;
        padding-top: 10px;
    }
    
    .reason-item:not(.highlighted) .reason-text {
        color: #000;
        opacity: 0.85;
    }
    
    .reason-item.highlighted .reason-text {
        color: #FFF;
        opacity: 0.95;
    }
</style>

<div class="logo-bottom">Architec.</div>

<div class="container">
    <!-- Left Section -->
    <div class="left-section">
        <div class="main-heading">
            Why<br>Choose Us
        </div>
        <div class="subheading">
            Because every building has a story. And we make sure your story comes to life perfectly.
        </div>
    </div>
    
    <!-- Right Section -->
    <div class="right-section">
        <!-- Item 01 -->
        <div class="reason-item">
            <div class="reason-number">01</div>
            <div class="reason-text">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus litora auctor adipiscing vestibu.
            </div>
        </div>
        
        <!-- Item 02 -->
        <div class="reason-item">
            <div class="reason-number">02</div>
            <div class="reason-text">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus litora auctor adipiscing vestibu.
            </div>
        </div>
        
        <!-- Item 03 - Highlighted -->
        <div class="reason-item highlighted">
            <div class="reason-number">03</div>
            <div class="reason-text">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus litora auctor adipiscing vestibu.
            </div>
        </div>
        
        <!-- Item 04 -->
        <div class="reason-item">
            <div class="reason-number">04</div>
            <div class="reason-text">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus litora auctor adipiscing vestibu.
            </div>
        </div>
    </div>
</div>
</body>
</html>