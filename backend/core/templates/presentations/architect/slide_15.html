<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 15</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #2B2B2B;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
    }
    
    /* Top Section */
    .top-section {
        margin-bottom: 120px;
    }
    
    .main-heading {
        font-size: 100px;
        font-weight: 400;
        color: #FFF;
        letter-spacing: -3px;
        margin-bottom: 40px;
    }
    
    .description {
        font-size: 24px;
        font-weight: 400;
        color: #CCC;
        line-height: 1.7;
        letter-spacing: -0.3px;
        max-width: 1400px;
    }
    
    /* Workflow Diagram */
    .workflow-diagram {
        position: relative;
        margin-top: 80px;
        padding: 0 50px;
    }
    
    /* Horizontal Line */
    .workflow-line {
        position: absolute;
        top: 15px;
        left: 50px;
        right: 50px;
        height: 3px;
        background: #FF6B35;
    }
    
    /* Workflow Steps */
    .workflow-steps {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .workflow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
    }
    
    .step-dot {
        width: 30px;
        height: 30px;
        background: #FF6B35;
        border-radius: 50%;
        margin-bottom: 40px;
        position: relative;
        z-index: 2;
    }
    
    .step-label {
        font-size: 28px;
        font-weight: 400;
        color: #FFF;
        text-align: center;
        line-height: 1.4;
        letter-spacing: -0.5px;
        max-width: 250px;
    }
</style>

<div class="container">
    <div class="top-section">
        <div class="main-heading">
            Workflow Process
        </div>
        <div class="description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus its netus quam fermenta.
        </div>
    </div>
    
    <div class="workflow-diagram">
        <!-- Horizontal Line -->
        <div class="workflow-line"></div>
        
        <!-- Workflow Steps -->
        <div class="workflow-steps">
            <div class="workflow-step">
                <div class="step-dot"></div>
                <div class="step-label">Discussion and<br>Consultation</div>
            </div>
            
            <div class="workflow-step">
                <div class="step-dot"></div>
                <div class="step-label">Conceptual<br>Design</div>
            </div>
            
            <div class="workflow-step">
                <div class="step-dot"></div>
                <div class="step-label">Technical Plan<br>and Permit</div>
            </div>
            
            <div class="workflow-step">
                <div class="step-dot"></div>
                <div class="step-label">Field<br>Construction</div>
            </div>
            
            <div class="workflow-step">
                <div class="step-dot"></div>
                <div class="step-label">Handover</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>