<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 16</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #2B2B2B;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
        display: flex;
        gap: 120px;
    }
    
    /* Left Section */
    .left-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    .main-heading {
        font-size: 80px;
        font-weight: 400;
        color: #FFF;
        line-height: 1.2;
        letter-spacing: -2px;
        max-width: 700px;
    }
    
    .abstract-graphic {
        width: 400px;
        height: 400px;
        margin-top: 80px;
    }
    
    .abstract-graphic svg {
        width: 100%;
        height: 100%;
    }
    
    /* Right Section */
    .right-section {
        flex: 0 0 600px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 120px;
    }
    
    .stat-block {
        display: flex;
        flex-direction: column;
    }
    
    .stat-number {
        font-size: 140px;
        font-weight: 700;
        color: #FF6B35;
        letter-spacing: -4px;
        line-height: 1;
        margin-bottom: 40px;
    }
    
    .stat-description {
        font-size: 22px;
        font-weight: 400;
        color: #CCC;
        line-height: 1.7;
        letter-spacing: -0.3px;
    }
</style>

<div class="container">
    <!-- Left Section -->
    <div class="left-section">
        <div class="main-heading">
            We present attractive designs that also support productivity
        </div>
        
        <div class="abstract-graphic">
            <svg viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
                <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(-15 200 200)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(15 200 200)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(45 200 200)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(75 200 200)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(105 200 200)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
            </svg>
        </div>
    </div>
    
    <!-- Right Section -->
    <div class="right-section">
        <!-- Stat 1 -->
        <div class="stat-block">
            <div class="stat-number">450+</div>
            <div class="stat-description">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus its netus quam fermenta ornare dapibus ornare conva egestas blandit adipiscing here.
            </div>
        </div>
        
        <!-- Stat 2 -->
        <div class="stat-block">
            <div class="stat-number">87%</div>
            <div class="stat-description">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum.
            </div>
        </div>
    </div>
</div>
</body>
</html>