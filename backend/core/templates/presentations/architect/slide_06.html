<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 6</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 80px 80px 80px 80px;
        display: flex;
        flex-direction: column;
    }
    
    /* Top Section */
    .top-section {
        margin-bottom: 60px;
    }
    
    .section-label {
        font-size: 48px;
        font-weight: 500;
        color: #FF6B35;
        letter-spacing: -0.5px;
        margin-bottom: 40px;
    }
    
    .main-heading {
        font-size: 56px;
        font-weight: 400;
        color: #000;
        line-height: 1.3;
        letter-spacing: -1px;
        max-width: 1600px;
    }
    
    /* Image Grid */
    .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        flex: 1;
    }
    
    .image-box {
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .image-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80') center/cover;
        opacity: 0.2;
    }
</style>

<div class="container">
    <div class="top-section">
        <div class="section-label">
            What We Do
        </div>
        <div class="main-heading">
            We are here from concept to realization. Well-planned design, robust construction, and stunning results.
        </div>
    </div>
    
    <div class="image-grid">
        <div class="image-box"></div>
        <div class="image-box"></div>
        <div class="image-box"></div>
    </div>
</div>
</body>
</html>