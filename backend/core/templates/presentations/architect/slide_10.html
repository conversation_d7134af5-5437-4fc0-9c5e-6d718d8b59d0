<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 10</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 80px 80px;
        display: flex;
        flex-direction: column;
        gap: 60px;
    }
    
    /* Row */
    .row {
        flex: 1;
        display: flex;
        gap: 80px;
        align-items: center;
    }
    
    /* Left Content */
    .left-content {
        flex: 1;
        display: flex;
        gap: 40px;
        align-items: flex-start;
    }
    
    .icon-circle {
        width: 80px;
        height: 80px;
        background: #FF6B35;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        font-size: 48px;
        color: #FFF;
        font-weight: 300;
    }
    
    .text-content {
        flex: 1;
        padding-top: 5px;
    }
    
    .heading {
        font-size: 64px;
        font-weight: 400;
        color: #000;
        line-height: 1.2;
        letter-spacing: -1.5px;
        margin-bottom: 30px;
    }
    
    .description {
        font-size: 24px;
        font-weight: 400;
        color: #000;
        line-height: 1.7;
        letter-spacing: -0.3px;
        opacity: 0.85;
        max-width: 650px;
    }
    
    /* Right Image */
    .right-image {
        flex: 0 0 600px;
        height: 100%;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .right-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&q=80') center/cover;
        opacity: 0.2;
    }
</style>

<div class="container">
    <!-- Row 1 -->
    <div class="row">
        <div class="left-content">
            <div class="icon-circle">+</div>
            <div class="text-content">
                <div class="heading">
                    Exclusive &<br>Functional Design
                </div>
                <div class="description">
                    Duis diam nullam dis tincidu ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing. Laoreta lectus semper, pharetra eges odio curabitur ultricies Ultrices fames faucibus conubia.
                </div>
            </div>
        </div>
        <div class="right-image"></div>
    </div>
    
    <!-- Row 2 -->
    <div class="row">
        <div class="left-content">
            <div class="icon-circle">+</div>
            <div class="text-content">
                <div class="heading">
                    Timely Execution
                </div>
                <div class="description">
                    Duis diam nullam dis tincidu ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing.
                </div>
            </div>
        </div>
        <div class="right-image"></div>
    </div>
</div>
</body>
</html>