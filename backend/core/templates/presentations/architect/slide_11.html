<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 11</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    /* Heading */
    .main-heading {
        font-size: 80px;
        font-weight: 400;
        color: #000;
        letter-spacing: -2px;
        margin-bottom: 100px;
        text-align: center;
    }
    
    /* Team Grid */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 60px;
        width: 100%;
        max-width: 1600px;
    }
    
    /* Team Member */
    .team-member {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .member-photo {
        width: 100%;
        aspect-ratio: 1;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        border-radius: 30px;
        margin-bottom: 40px;
        position: relative;
        overflow: hidden;
    }
    
    .member-photo::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1560250097-0b93528c311a?w=800&q=80') center/cover;
        opacity: 0.15;
    }
    
    .member-name {
        font-size: 36px;
        font-weight: 400;
        color: #000;
        letter-spacing: -0.5px;
        text-align: center;
    }
</style>

<div class="container">
    <div class="main-heading">
        Team Introduction
    </div>
    
    <div class="team-grid">
        <!-- Team Member 1 -->
        <div class="team-member">
            <div class="member-photo"></div>
            <div class="member-name">Write Anything Name</div>
        </div>
        
        <!-- Team Member 2 -->
        <div class="team-member">
            <div class="member-photo"></div>
            <div class="member-name">Write Anything Name</div>
        </div>
        
        <!-- Team Member 3 -->
        <div class="team-member">
            <div class="member-photo"></div>
            <div class="member-name">Write Anything Name</div>
        </div>
    </div>
</div>
</body>
</html>