<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 17</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    /* Top Section */
    .top-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .contact-info {
        max-width: 800px;
    }
    
    .contact-text {
        font-size: 48px;
        font-weight: 400;
        color: #000;
        line-height: 1.3;
        letter-spacing: -0.5px;
        margin-bottom: 40px;
    }
    
    .email {
        font-size: 48px;
        font-weight: 400;
        color: #000;
        letter-spacing: -0.5px;
    }
    
    /* Abstract Graphic */
    .abstract-graphic {
        width: 500px;
        height: 500px;
        margin-top: -50px;
    }
    
    .abstract-graphic svg {
        width: 100%;
        height: 100%;
    }
    
    /* Bottom Section */
    .bottom-section {
        margin-top: auto;
    }
    
    .main-heading {
        font-size: 180px;
        font-weight: 700;
        color: #000;
        line-height: 0.9;
        letter-spacing: -5px;
    }
    
    .main-heading .dot {
        color: #FF6B35;
    }
</style>

<div class="container">
    <!-- Top Section -->
    <div class="top-section">
        <div class="contact-info">
            <div class="contact-text">
                Build your dream project with us.<br>
                Contact us today for a free consultation.
            </div>
            <div class="email">
                <EMAIL>
            </div>
        </div>
        
        <div class="abstract-graphic">
            <svg viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
                <ellipse cx="250" cy="250" rx="180" ry="120" transform="rotate(-15 250 250)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="250" cy="250" rx="180" ry="120" transform="rotate(15 250 250)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="250" cy="250" rx="180" ry="120" transform="rotate(45 250 250)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="250" cy="250" rx="180" ry="120" transform="rotate(75 250 250)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
                <ellipse cx="250" cy="250" rx="180" ry="120" transform="rotate(105 250 250)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
            </svg>
        </div>
    </div>
    
    <!-- Bottom Section -->
    <div class="bottom-section">
        <div class="main-heading">
            Let's Work<br>Together<span class="dot">.</span>
        </div>
    </div>
</div>
</body>
</html>