<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #2B2B2B;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Logo */
    .logo {
        position: absolute;
        top: 50px;
        left: 60px;
        font-size: 32px;
        font-weight: 400;
        color: #FFF;
        letter-spacing: -0.5px;
    }
    
    /* Abstract Graphic Top Center */
    .abstract-graphic-top {
        position: absolute;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        width: 350px;
        height: 250px;
    }
    
    .abstract-graphic-top svg {
        width: 100%;
        height: 100%;
    }
    
    /* Content Layout */
    .content-container {
        position: absolute;
        top: 320px;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        padding: 0 120px;
    }
    
    /* Left Content */
    .left-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-right: 100px;
    }
    
    .main-statement {
        font-size: 56px;
        font-weight: 400;
        color: #FFF;
        line-height: 1.3;
        letter-spacing: -1px;
        max-width: 700px;
    }
    
    .vision-label {
        font-size: 48px;
        font-weight: 500;
        color: #FF6B35;
        letter-spacing: -0.5px;
        margin-bottom: 80px;
    }
    
    /* Right Content */
    .right-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-left: 50px;
    }
    
    .right-heading {
        font-size: 36px;
        font-weight: 400;
        color: #FFF;
        line-height: 1.4;
        letter-spacing: -0.5px;
        margin-bottom: 40px;
    }
    
    .right-description {
        font-size: 20px;
        font-weight: 400;
        color: #CCC;
        line-height: 1.7;
        letter-spacing: -0.2px;
        margin-bottom: 80px;
    }
    
    .stat-large {
        font-size: 120px;
        font-weight: 700;
        color: #FF6B35;
        letter-spacing: -3px;
        line-height: 1;
        margin-bottom: 10px;
    }
    
    .stat-label {
        font-size: 28px;
        font-weight: 400;
        color: #FF6B35;
        letter-spacing: -0.3px;
    }
</style>

<div class="logo">Architec.</div>

<div class="abstract-graphic-top">
    <svg viewBox="0 0 350 250" fill="none" xmlns="http://www.w3.org/2000/svg">
        <ellipse cx="175" cy="125" rx="140" ry="90" transform="rotate(-15 175 125)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="125" rx="140" ry="90" transform="rotate(15 175 125)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="125" rx="140" ry="90" transform="rotate(45 175 125)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="125" rx="140" ry="90" transform="rotate(75 175 125)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="125" rx="140" ry="90" transform="rotate(105 175 125)" stroke="#FFF" stroke-width="1.5" opacity="0.5"/>
    </svg>
</div>

<div class="content-container">
    <div class="left-content">
        <div class="main-statement">
            To be a trusted architectural partner in creating innovative, functional and highly aesthetic spaces.
        </div>
        <div class="vision-label">
            Our Vision
        </div>
    </div>
    
    <div class="right-content">
        <div class="right-heading">
            Creating innovative, functional and highly aesthetic spaces.
        </div>
        <div class="right-description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus. Quam litora auctor adipiscing vestibulum proin honcus netus quam fermenta ornare dapibus ornare conva egestas blandit adipiscing sollicitu laoret egesta.
        </div>
        <div>
            <div class="stat-large">85%</div>
            <div class="stat-label">Write Project Done</div>
        </div>
    </div>
</div>
</body>
</html>