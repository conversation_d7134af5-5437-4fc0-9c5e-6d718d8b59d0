<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Content */
    .content-wrapper {
        position: absolute;
        top: 180px;
        left: 100px;
        right: 100px;
        bottom: 200px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .headline {
        font-size: 64px;
        font-weight: 400;
        line-height: 1.2;
        color: #000;
        letter-spacing: -1px;
        max-width: 700px;
    }
    
    .headline .highlight {
        color: #FF6B35;
        font-weight: 500;
    }
    
    /* Abstract Graphic */
    .abstract-graphic {
        position: absolute;
        right: 150px;
        top: 50%;
        transform: translateY(-50%);
        width: 400px;
        height: 400px;
    }
    
    .abstract-graphic svg {
        width: 100%;
        height: 100%;
    }
    
    /* Large Brand Text */
    .brand-large {
        position: absolute;
        bottom: 80px;
        left: 100px;
        font-size: 200px;
        font-weight: 800;
        color: #000;
        letter-spacing: -5px;
        line-height: 1;
    }
    
    .brand-large .dot {
        color: #FF6B35;
    }
</style>

<div class="content-wrapper">
    <div class="headline">
        Transforming Ideas into<br>
        Iconic <span class="highlight">Structures</span>
    </div>
    
    <div class="abstract-graphic">
        <svg viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(-15 200 200)" stroke="#000" stroke-width="1.5" opacity="0.6"/>
            <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(15 200 200)" stroke="#000" stroke-width="1.5" opacity="0.6"/>
            <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(45 200 200)" stroke="#000" stroke-width="1.5" opacity="0.6"/>
            <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(75 200 200)" stroke="#000" stroke-width="1.5" opacity="0.6"/>
            <ellipse cx="200" cy="200" rx="150" ry="100" transform="rotate(105 200 200)" stroke="#000" stroke-width="1.5" opacity="0.6"/>
        </svg>
    </div>
</div>

<div class="brand-large">
    Architec<span class="dot">.</span>
</div>
</body>
</html>