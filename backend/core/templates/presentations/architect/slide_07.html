<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Orange Background Box - Full Height */
    .orange-background {
        position: absolute;
        top: 0;
        right: 0;
        width: 500px;
        height: 100%;
        background: #FF6B35;
        z-index: 1;
    }
    
    /* Main Container */
    .container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 100px 100px;
        display: flex;
        gap: 80px;
        z-index: 2;
    }
    
    /* Left Content */
    .left-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    .top-content {
        flex: 1;
    }
    
    .service-label {
        font-size: 48px;
        font-weight: 500;
        color: #FF6B35;
        letter-spacing: -0.5px;
        margin-bottom: 50px;
    }
    
    .main-heading {
        font-size: 64px;
        font-weight: 400;
        color: #000;
        line-height: 1.25;
        letter-spacing: -1.5px;
        max-width: 700px;
    }
    
    .bottom-content {
        margin-top: 80px;
    }
    
    .stat-number {
        font-size: 140px;
        font-weight: 700;
        color: #FF6B35;
        letter-spacing: -4px;
        line-height: 1;
        margin-bottom: 30px;
    }
    
    .stat-description {
        font-size: 22px;
        font-weight: 400;
        color: #000;
        line-height: 1.7;
        letter-spacing: -0.2px;
        max-width: 600px;
        opacity: 0.8;
    }
    
    /* Right Images */
    .right-images {
        flex: 0 0 600px;
        display: flex;
        flex-direction: column;
        gap: 30px;
        position: relative;
        z-index: 3;
    }
    
    .image-box {
        flex: 1;
        background: linear-gradient(135deg, #9BA5B7 0%, #B8C1D0 100%);
        border-radius: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .image-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=800&q=80') center/cover;
        opacity: 0.2;
    }
</style>

<!-- Orange Background Box -->
<div class="orange-background"></div>

<div class="container">
    <!-- Left Content -->
    <div class="left-content">
        <div class="top-content">
            <div class="service-label">
                Construction Service
            </div>
            <div class="main-heading">
                We build every detail with accuracy and precision, making every project stand strong and classy.
            </div>
        </div>
        
        <div class="bottom-content">
            <div class="stat-number">35+</div>
            <div class="stat-description">
                Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing.
            </div>
        </div>
    </div>
    
    <!-- Right Images -->
    <div class="right-images">
        <div class="image-box"></div>
        <div class="image-box"></div>
    </div>
</div>
</body>
</html>