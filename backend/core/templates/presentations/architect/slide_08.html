<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architec Presentation - Slide 8</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #F5F3EE;
        width: 1920px;
        height: 1080px;
        overflow: hidden;
        position: relative;
    }
    
    /* Main Heading */
    .main-heading {
        position: absolute;
        top: 100px;
        left: 100px;
        font-size: 80px;
        font-weight: 400;
        color: #000;
        letter-spacing: -2px;
    }
    
    /* Abstract Graphic Top Right */
    .abstract-graphic {
        position: absolute;
        top: 80px;
        right: 150px;
        width: 350px;
        height: 300px;
    }
    
    .abstract-graphic svg {
        width: 100%;
        height: 100%;
    }
    
    /* Content Grid */
    .content-grid {
        position: absolute;
        top: 350px;
        left: 100px;
        right: 100px;
        bottom: 100px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 50px;
    }
    
    /* Column Items */
    .column-item {
        display: flex;
        flex-direction: column;
        padding: 60px 50px;
        border-radius: 30px;
    }
    
    .column-item.highlighted {
        background: #FF6B35;
    }
    
    .number {
        font-size: 100px;
        font-weight: 700;
        letter-spacing: -3px;
        line-height: 1;
        margin-bottom: 40px;
    }
    
    .column-item:not(.highlighted) .number {
        color: #FF6B35;
    }
    
    .column-item.highlighted .number {
        color: #FFF;
    }
    
    .column-title {
        font-size: 32px;
        font-weight: 500;
        letter-spacing: -0.5px;
        margin-bottom: 30px;
        line-height: 1.3;
    }
    
    .column-item:not(.highlighted) .column-title {
        color: #000;
    }
    
    .column-item.highlighted .column-title {
        color: #FFF;
    }
    
    .column-description {
        font-size: 20px;
        font-weight: 400;
        line-height: 1.7;
        letter-spacing: -0.2px;
    }
    
    .column-item:not(.highlighted) .column-description {
        color: #000;
        opacity: 0.8;
    }
    
    .column-item.highlighted .column-description {
        color: #FFF;
        opacity: 0.95;
    }
</style>

<div class="main-heading">
    Project Planning
</div>

<div class="abstract-graphic">
    <svg viewBox="0 0 350 300" fill="none" xmlns="http://www.w3.org/2000/svg">
        <ellipse cx="175" cy="150" rx="140" ry="90" transform="rotate(-15 175 150)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="150" rx="140" ry="90" transform="rotate(15 175 150)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="150" rx="140" ry="90" transform="rotate(45 175 150)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
        <ellipse cx="175" cy="150" rx="140" ry="90" transform="rotate(75 175 150)" stroke="#000" stroke-width="1.5" opacity="0.5"/>
    </svg>
</div>

<div class="content-grid">
    <!-- Column 01 -->
    <div class="column-item">
        <div class="number">01</div>
        <div class="column-title">Timeline of each project</div>
        <div class="column-description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus netus quam fermenta ornare dapibus ornare
        </div>
    </div>
    
    <!-- Column 02 - Highlighted -->
    <div class="column-item highlighted">
        <div class="number">02</div>
        <div class="column-title">Progress of each project</div>
        <div class="column-description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus netus quam fermenta ornare dapibus ornare
        </div>
    </div>
    
    <!-- Column 03 -->
    <div class="column-item">
        <div class="number">03</div>
        <div class="column-title">Budget of each project</div>
        <div class="column-description">
            Duis diam nullam dis tincidunt ornare tincidunt Sapien tristique mattis taciti placerat curabitur ipsum metus dolor ridiculus suam litora auctor adipiscing vestibulum proin honcus netus quam fermenta ornare dapibus ornare
        </div>
    </div>
</div>
</body>
</html>