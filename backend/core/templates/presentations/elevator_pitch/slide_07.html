<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elevator Pitch Presentation - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #FFFFFF;
        overflow: hidden;
        width: 1920px;
        height: 1080px;
        position: relative;
    }
    
    .slide-container {
        width: 1920px;
        height: 1080px;
        position: relative;
        background: #FFFFFF;
        padding: 0;
    }
    
    /* Main Title - Bold Weight */
    .main-title {
        position: absolute;
        top: 100px;
        left: 120px;
        font-size: 90px;
        font-weight: 700;
        line-height: 1.1;
        color: #000000;
        letter-spacing: -2px;
        z-index: 10;
    }
    
    .title-purple {
        color: #7C3AED;
        font-weight: 700;
    }
    
    /* Large Light Gray Box Behind Cards - 50% width, touching right and bottom */
    .gray-background {
        position: absolute;
        top: 40px;
        right: 0;
        bottom: 0;
        width: 50%;
        background: #E8E8E8;
        border-radius: 260px 0 0 0;
        z-index: 1;
    }
    
    /* Top Right Text - No background box */
    .top-text {
        position: absolute;
        top: 100px;
        right: 120px;
        width: 480px;
        font-size: 20px;
        font-weight: 400;
        color: #000000;
        line-height: 1.6;
        z-index: 10;
    }
    
    /* Cards Container */
    .cards-container {
        position: absolute;
        top: 280px;
        left: 120px;
        right: 120px;
        display: flex;
        gap: 40px;
        z-index: 5;
    }
    
    .card {
        flex: 1;
        padding: 70px 50px;
        border-radius: 50px;
        min-height: 450px;
    }
    
    .card-purple {
        background: #7C3AED;
    }
    
    .card-green {
        background: #6EE7B7;
    }
    
    /* Large Percentage */
    .card-percentage {
        font-size: 120px;
        font-weight: 700;
        margin-bottom: 30px;
        line-height: 1;
    }
    
    .percentage-white {
        color: #FFFFFF;
    }
    
    .percentage-black {
        color: #000000;
    }
    
    /* Card Plan Name */
    .card-plan {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 40px;
        line-height: 1.2;
    }
    
    .plan-white {
        color: #FFFFFF;
    }
    
    .plan-black {
        color: #000000;
    }
    
    /* Bullet List */
    .card-list {
        list-style: none;
        padding: 0;
    }
    
    .card-list li {
        font-size: 18px;
        font-weight: 400;
        line-height: 1.7;
        margin-bottom: 15px;
        padding-left: 20px;
        position: relative;
    }
    
    .card-list li:before {
        content: "•";
        position: absolute;
        left: 0;
        font-size: 24px;
    }
    
    .list-white {
        color: #FFFFFF;
    }
    
    .list-white li:before {
        color: #FFFFFF;
    }
    
    .list-black {
        color: #000000;
    }
    
    .list-black li:before {
        color: #000000;
    }
    
    /* Website URL */
    .website {
        position: absolute;
        bottom: 80px;
        left: 120px;
        font-size: 24px;
        font-weight: 400;
        color: #000000;
        z-index: 10;
    }
    
    /* Slide Number */
    .slide-number {
        position: absolute;
        bottom: 60px;
        right: 100px;
        width: 90px;
        height: 90px;
        background: #7C3AED;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 44px;
        font-weight: 700;
        color: #FFFFFF;
        z-index: 30;
    }
</style>

<div class="slide-container">
    <!-- Large Light Gray Box Behind Cards -->
    <div class="gray-background"></div>
    
    <!-- Main Title - Bold Weight -->
    <div class="main-title">
        Our <span class="title-purple">Pricing</span>
    </div>
    
    <!-- Top Right Text - No background -->
    <div class="top-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor sit amet.
    </div>
    
    <!-- Cards Container -->
    <div class="cards-container">
        <!-- Card 1 - Purple - Basic Plan -->
        <div class="card card-purple">
            <div class="card-percentage percentage-white">80%</div>
            <div class="card-plan plan-white">Basic Plan</div>
            <ul class="card-list list-white">
                <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit</li>
                <li>sed do eiusmod tempor incididunt ut</li>
                <li>Lorem ipsum dolor sit amet</li>
            </ul>
        </div>
        
        <!-- Card 2 - Green - Standard Plan -->
        <div class="card card-green">
            <div class="card-percentage percentage-black">70%</div>
            <div class="card-plan plan-black">Standard Plan</div>
            <ul class="card-list list-black">
                <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit</li>
                <li>sed do eiusmod tempor incididunt ut</li>
                <li>Lorem ipsum dolor sit amet</li>
            </ul>
        </div>
        
        <!-- Card 3 - Purple - Premium Plan -->
        <div class="card card-purple">
            <div class="card-percentage percentage-white">60%</div>
            <div class="card-plan plan-white">Premium Plan</div>
            <ul class="card-list list-white">
                <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit</li>
                <li>sed do eiusmod tempor incididunt ut</li>
                <li>Lorem ipsum dolor sit amet</li>
            </ul>
        </div>
    </div>
    
    <!-- Website -->
    <div class="website">
        www.yourwebsite.org
    </div>
    
    <!-- Slide Number -->
    <div class="slide-number">
        7
    </div>
</div>
</body>
</html>