<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elevator Pitch Presentation - Slide 1</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap');
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #FAFAFA;
        overflow: hidden;
        width: 1920px;
        height: 1080px;
        position: relative;
    }
    
    .slide-container {
        width: 1920px;
        height: 1080px;
        position: relative;
        background: #FAFAFA;
        padding: 0;
    }
    
    /* Logo */
    .logo {
        position: absolute;
        top: 80px;
        left: 120px;
        width: 80px;
        height: 80px;
        z-index: 10;
    }
    
    .logo-bar {
        background: #7C3AED;
        border-radius: 25px;
        position: absolute;
    }
    
    .logo-bar-1 {
        width: 35px;
        height: 120px;
        left: 0;
        top: 0;
        transform: rotate(-25deg);
        transform-origin: bottom left;
    }
    
    .logo-bar-2 {
        width: 35px;
        height: 80px;
        left: 45px;
        top: 0;
        transform: rotate(-25deg);
        transform-origin: bottom left;
    }
    
    /* Main Title */
    .main-title {
        position: absolute;
        top: 220px;
        left: 120px;
        font-size: 140px;
        font-weight: 900;
        line-height: 1.1;
        color: #000000;
        letter-spacing: -3px;
        z-index: 10;
    }
    
    /* Subtitle */
    .subtitle {
        position: absolute;
        top: 540px;
        left: 120px;
        font-size: 32px;
        font-weight: 400;
        color: #000000;
        z-index: 10;
    }
    
    .subtitle-bold {
        font-weight: 700;
    }
    
    /* Website URL */
    .website {
        position: absolute;
        bottom: 420px;
        left: 120px;
        font-size: 24px;
        font-weight: 400;
        color: #000000;
        z-index: 10;
    }
    
    /* Bottom 3 Columns spanning full width */
    .bottom-columns {
        position: absolute;
        bottom: 0;
        left: 120px;
        right: 120px;
        height: 750px;
        display: flex;
        gap: 30px;
        align-items: flex-end;
    }
    
    .column {
        flex: 1;
        border-radius: 40px 40px 0 0;
    }
    
    /* Column 1 - Small Green (left) */
    .column-1 {
        background: #6EE7B7;
        height: 180px;
    }
    
    /* Column 2 - Medium Purple (middle) */
    .column-2 {
        background: #7C3AED;
        height: 280px;
    }
    
    /* Column 3 - Tall Green (right) */
    .column-3 {
        background: #6EE7B7;
        height: 750px;
        border-radius: 40px 0 0 0;
    }
    
    /* Purple Box - Top Right, same width as column 3 */
    .shape-purple-top {
        position: absolute;
        width: calc((100% - 240px - 60px) / 3);
        height: 300px;
        background: #7C3AED;
        top: 0;
        right: 120px;
        border-radius: 0 0 0 40px;
    }
</style>

<div class="slide-container">
    <!-- Logo -->
    <div class="logo">
        <div class="logo-bar logo-bar-1"></div>
        <div class="logo-bar logo-bar-2"></div>
    </div>
    
    <!-- Main Title -->
    <div class="main-title">
        Elevator<br>Pitch
    </div>
    
    <!-- Subtitle -->
    <div class="subtitle">
        <span class="subtitle-bold">Elevator Pitch</span> Presentation Template
    </div>
    
    <!-- Website -->
    <div class="website">
        www.yourwebsite.org
    </div>
    
    <!-- Purple Box at Top Right - Same width as column 3 -->
    <div class="shape-purple-top"></div>
    
    <!-- Bottom 3 Columns -->
    <div class="bottom-columns">
        <div class="column column-1"></div>
        <div class="column column-2"></div>
        <div class="column column-3"></div>
    </div>
</div>
</body>
</html>