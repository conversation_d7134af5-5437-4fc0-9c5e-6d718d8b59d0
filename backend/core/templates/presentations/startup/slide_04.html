<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 4</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
  }
  
  /* Split layout */
  .left-side {
    width: 50%;
    background: #e5e5e5;
    position: relative;
  }
  
  .right-side {
    width: 50%;
    background: #0a0a0a;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 100px 120px;
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 60px;
    left: 80px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Right side content */
  .main-heading {
    font-size: 64px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 80px;
    line-height: 1.2;
    letter-spacing: -1px;
  }
  
  .main-heading .bold {
    font-weight: 700;
  }
  
  /* List items */
  .list-container {
    display: flex;
    flex-direction: column;
    gap: 50px;
  }
  
  .list-item {
    display: flex;
    gap: 25px;
    align-items: flex-start;
  }
  
  .icon-circle {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: transparent;
    border: 3px solid #0066ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  
  .arrow-icon {
    width: 24px;
    height: 24px;
    position: relative;
  }
  
  .arrow-icon::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 3px;
    background: #0066ff;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  
  .arrow-icon::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border-right: 3px solid #0066ff;
    border-top: 3px solid #0066ff;
    top: 50%;
    right: 2px;
    transform: translateY(-50%) rotate(45deg);
  }
  
  .item-content {
    flex: 1;
  }
  
  .item-title {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
  }
  
  .item-description {
    font-size: 17px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.7;
    letter-spacing: 0.3px;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .left-side {
    animation: fadeIn 0.8s ease-out;
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .main-heading {
    animation: slideInRight 0.8s ease-out 0.2s both;
  }
  
  .list-item:nth-child(1) {
    animation: slideInRight 0.8s ease-out 0.4s both;
  }
  
  .list-item:nth-child(2) {
    animation: slideInRight 0.8s ease-out 0.6s both;
  }
  
  .list-item:nth-child(3) {
    animation: slideInRight 0.8s ease-out 0.8s both;
  }
</style>

<div class="left-side">
  <div class="logo-section">
    <div class="logo-circle"></div>
    <div class="logo-text">Business Enterprise</div>
  </div>
</div>

<div class="right-side">
  <h1 class="main-heading"><span class="bold">Types Of</span> Business Enterprises</h1>
  
  <div class="list-container">
    <div class="list-item">
      <div class="icon-circle">
        <div class="arrow-icon"></div>
      </div>
      <div class="item-content">
        <h2 class="item-title">Sole Proprietorship</h2>
        <p class="item-description">
          A business structure owned and operated by a single individual who assumes all risks and rewards. This model offers complete control and simplified operations while maintaining direct responsibility for all business decisions and outcomes.
        </p>
      </div>
    </div>
    
    <div class="list-item">
      <div class="icon-circle">
        <div class="arrow-icon"></div>
      </div>
      <div class="item-content">
        <h2 class="item-title">Partnership</h2>
        <p class="item-description">
          A collaborative business arrangement where two or more individuals share ownership, responsibilities, and profits. Partners combine their expertise, resources, and capital to achieve common business objectives through mutual agreement.
        </p>
      </div>
    </div>
    
    <div class="list-item">
      <div class="icon-circle">
        <div class="arrow-icon"></div>
      </div>
      <div class="item-content">
        <h2 class="item-title">Corporation</h2>
        <p class="item-description">
          A legal entity separate from its owners, offering limited liability protection and the ability to raise capital through stock issuance. Corporations provide structured governance and perpetual existence independent of ownership changes.
        </p>
      </div>
    </div>
  </div>
</div>
</body>
</html>