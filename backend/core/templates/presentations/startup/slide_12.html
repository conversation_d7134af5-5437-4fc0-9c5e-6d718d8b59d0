<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 12</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
  }
  
  /* Main heading */
  .main-heading {
    text-align: center;
    font-size: 82px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 100px;
    margin-bottom: 80px;
    letter-spacing: -1px;
  }
  
  .main-heading .bold {
    font-weight: 700;
  }
  
  /* Main container - 50/50 split */
  .main-container {
    display: flex;
    gap: 100px;
    padding: 0 80px;
    height: calc(100% - 350px);
  }
  
  /* Left side - 2x2 grid */
  .left-side {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 40px;
  }
  
  /* Blue card with triangle corner */
  .info-card {
    background: linear-gradient(135deg, #1a5fff 0%, #4d94ff 50%, #7db8ff 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 50px;
  }
  
  .info-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 70px 70px 0;
    border-color: transparent #0a0a0a transparent transparent;
  }
  
  .card-number {
    font-size: 110px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
    margin-bottom: 15px;
    letter-spacing: -4px;
  }
  
  .card-text {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    letter-spacing: 0.3px;
    line-height: 1.3;
  }
  
  /* Text boxes in grid */
  .text-box {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }
  
  .text-box p {
    font-size: 17px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.8;
    text-align: center;
    letter-spacing: 0.3px;
  }
  
  /* Right side - Content */
  .right-side {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px 0;
  }
  
  .section-title {
    font-size: 56px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 50px;
    letter-spacing: -0.5px;
  }
  
  .section-paragraph {
    font-size: 18px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.8;
    margin-bottom: 35px;
    text-align: justify;
    letter-spacing: 0.3px;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .main-heading {
    animation: slideInUp 0.8s ease-out 0.2s both;
  }
  
  .left-side > *:nth-child(1) {
    animation: scaleIn 0.6s ease-out 0.4s both;
  }
  
  .left-side > *:nth-child(2) {
    animation: scaleIn 0.6s ease-out 0.5s both;
  }
  
  .left-side > *:nth-child(3) {
    animation: scaleIn 0.6s ease-out 0.6s both;
  }
  
  .left-side > *:nth-child(4) {
    animation: scaleIn 0.6s ease-out 0.7s both;
  }
  
  .right-side {
    animation: slideInRight 0.8s ease-out 0.8s both;
  }
</style>

<h1 class="main-heading">Infographic <span class="bold">Section</span></h1>

<div class="main-container">
  <div class="left-side">
    <div class="info-card">
      <div class="card-number">01</div>
      <div class="card-text">Description In<br>Here</div>
    </div>
    
    <div class="text-box">
      <p>Lorem dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incidi</p>
    </div>
    
    <div class="text-box">
      <p>Lorem dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incidi</p>
    </div>
    
    <div class="info-card">
      <div class="card-number">02</div>
      <div class="card-text">Description In<br>Here</div>
    </div>
  </div>
  
  <div class="right-side">
    <h2 class="section-title">Graphic Designs</h2>
    <p class="section-paragraph">
      Lorem dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ipsum dolor sit amet consectetur adipiscing elit sed
    </p>
    <p class="section-paragraph">
      Lorem dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ipsum dolor sit amet consectetur adipiscing elit sed
    </p>
  </div>
</div>
</body>
</html>