<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 15</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
  }
  
  /* Split layout */
  .left-side {
    width: 58%;
    background: #0a0a0a;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 100px 120px;
    position: relative;
  }
  
  .right-side {
    width: 42%;
    background: linear-gradient(180deg, #6db3ff 0%, #5da3ff 30%, #4d94ff 60%, #3d85ff 100%);
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 40px;
    left: 40px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Main content */
  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }
  
  .main-heading {
    font-size: 140px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 40px;
    letter-spacing: -3px;
  }
  
  .subtitle {
    font-size: 22px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.6;
    margin-bottom: 80px;
    letter-spacing: 0.3px;
    max-width: 800px;
  }
  
  .cta-button {
    display: inline-block;
    padding: 20px 60px;
    background: linear-gradient(135deg, #3d85ff 0%, #5da3ff 100%);
    color: #ffffff;
    font-size: 22px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    letter-spacing: 0.5px;
    align-self: flex-start;
  }
  
  .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(61, 133, 255, 0.4);
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .left-side {
    animation: fadeIn 0.8s ease-out;
  }
  
  .right-side {
    animation: slideInRight 0.8s ease-out 0.2s both;
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .main-heading {
    animation: slideInLeft 0.8s ease-out 0.4s both;
  }
  
  .subtitle {
    animation: slideInLeft 0.8s ease-out 0.6s both;
  }
  
  .cta-button {
    animation: slideInLeft 0.8s ease-out 0.8s both;
  }
</style>

<div class="left-side">
  <div class="logo-section">
    <div class="logo-circle"></div>
    <div class="logo-text">Business Enterprise</div>
  </div>
  
  <div class="content-wrapper">
    <h1 class="main-heading">Thank You</h1>
    
    <p class="subtitle">
      Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Maecenas portico cangue masa.
    </p>
    
    <button class="cta-button">End Presentation</button>
  </div>
</div>

<div class="right-side"></div>
</body>
</html>