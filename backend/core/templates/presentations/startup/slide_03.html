<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 3</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 60px;
    left: 80px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Main content */
  .main-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 100px;
    padding-top: 60px;
  }
  
  .header-section {
    text-align: center;
    margin-bottom: 80px;
  }
  
  .main-heading {
    font-size: 72px;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 30px;
    letter-spacing: -1px;
  }
  
  .main-heading .bold {
    font-weight: 700;
  }
  
  .description {
    font-size: 18px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.7;
    max-width: 1200px;
    margin: 0 auto;
    letter-spacing: 0.3px;
  }
  
  /* Cards section */
  .cards-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    width: 100%;
    max-width: 1760px;
  }
  
  .card {
    height: 380px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: 40px;
    position: relative;
    overflow: hidden;
  }
  
  .card-gray {
    background: #e5e5e5;
  }
  
  .card-blue {
    background: linear-gradient(180deg, #6b9fff 0%, #4d94ff 50%, #3d85ff 100%);
  }
  
  .card-label {
    padding: 16px 40px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
  }
  
  .card-label:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 102, 255, 0.4);
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .main-heading {
    animation: fadeInUp 0.8s ease-out 0.2s both;
  }
  
  .description {
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }
  
  .card:nth-child(1) {
    animation: scaleIn 0.6s ease-out 0.6s both;
  }
  
  .card:nth-child(2) {
    animation: scaleIn 0.6s ease-out 0.7s both;
  }
  
  .card:nth-child(3) {
    animation: scaleIn 0.6s ease-out 0.8s both;
  }
  
  .card:nth-child(4) {
    animation: scaleIn 0.6s ease-out 0.9s both;
  }
</style>

<div class="logo-section">
  <div class="logo-circle"></div>
  <div class="logo-text">Business Enterprise</div>
</div>

<div class="main-content">
  <div class="header-section">
    <h1 class="main-heading"><span class="bold">Importance</span> of Business Enterprises</h1>
    <p class="description">
      Business enterprises serve as the backbone of modern economies, driving innovation, creating employment opportunities, and fostering sustainable development. They transform ideas into tangible solutions that address market needs while contributing to societal progress and economic prosperity.
    </p>
  </div>
  
  <div class="cards-container">
    <div class="card card-gray">
      <div class="card-label">Economic Growth</div>
    </div>
    
    <div class="card card-gray">
      <div class="card-label">Innovation</div>
    </div>
    
    <div class="card card-blue">
      <div class="card-label">Social Impact</div>
    </div>
    
    <div class="card card-gray">
      <div class="card-label">Advancements</div>
    </div>
  </div>
</div>
</body>
</html>