<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 2</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 60px;
    left: 80px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Main container */
  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 0 0 80px;
    gap: 60px;
  }
  
  /* Left content */
  .left-content {
    flex: 1;
    max-width: 700px;
    padding-top: 80px;
  }
  
  .main-heading {
    font-size: 64px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 30px;
    letter-spacing: -1px;
  }
  
  .main-heading .highlight {
    font-weight: 700;
  }
  
  .description {
    font-size: 18px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.7;
    margin-bottom: 50px;
    letter-spacing: 0.3px;
  }
  
  .subheading {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
  }
  
  .sub-description {
    font-size: 18px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.7;
    margin-bottom: 40px;
    letter-spacing: 0.3px;
  }
  
  .cta-button {
    display: inline-block;
    padding: 18px 50px;
    background: #0066ff;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    border: none;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    letter-spacing: 0.5px;
  }
  
  .cta-button:hover {
    background: #0052cc;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 102, 255, 0.3);
  }
  
  /* Right content - Image placeholder with gradient accent */
  .right-content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 30px;
    padding-top: 80px;
    padding-right: 0;
  }
  
  .image-placeholder {
    width: 580px;
    height: 650px;
    background: #e5e5e5;
    position: relative;
  }
  
  .blue-accent {
    width: 190px;
    height: 650px;
    background: linear-gradient(180deg, #4d94ff 0%, #0066ff 50%, #0052cc 100%);
    margin-right: 0;
  }
  
  /* Animations */
  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .main-heading {
    animation: fadeInLeft 0.8s ease-out 0.2s both;
  }
  
  .description {
    animation: fadeInLeft 0.8s ease-out 0.4s both;
  }
  
  .subheading {
    animation: fadeInLeft 0.8s ease-out 0.6s both;
  }
  
  .sub-description {
    animation: fadeInLeft 0.8s ease-out 0.8s both;
  }
  
  .cta-button {
    animation: fadeInLeft 0.8s ease-out 1s both;
  }
  
  .image-placeholder {
    animation: fadeInRight 0.8s ease-out 0.4s both;
  }
  
  .blue-accent {
    animation: fadeInRight 0.8s ease-out 0.6s both;
  }
</style>

<div class="logo-section">
  <div class="logo-circle"></div>
  <div class="logo-text">Business Enterprise</div>
</div>

<div class="container">
  <div class="left-content">
    <h1 class="main-heading">Definition of <span class="highlight">Business Enterprise</span></h1>
    
    <p class="description">
      A business enterprise represents an organized commercial entity engaged in providing goods or services to meet market demands while generating sustainable revenue and creating value for stakeholders.
    </p>
    
    <h2 class="subheading">Organization Engaged In Commercial</h2>
    
    <p class="sub-description">
      Business enterprises operate within structured frameworks, combining resources, expertise, and strategic planning to achieve commercial objectives.
    </p>
    
    <button class="cta-button">Learn More</button>
  </div>
  
  <div class="right-content">
    <div class="image-placeholder"></div>
    <div class="blue-accent"></div>
  </div>
</div>
</body>
</html>