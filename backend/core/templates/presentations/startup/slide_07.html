<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 7</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 60px;
    left: 80px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Grid layout */
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 30px;
    padding: 180px 80px 80px 80px;
    height: 100%;
  }
  
  .grid-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px;
  }
  
  .box-gray {
    background: #e5e5e5;
  }
  
  .box-blue {
    background: linear-gradient(135deg, #5da3ff 0%, #4d94ff 50%, #3d85ff 100%);
    justify-content: center;
    align-items: flex-start;
    text-align: left;
  }
  
  .box-dark {
    background: #2a2a2a;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
  }
  
  /* Blue box content */
  .main-heading {
    font-size: 72px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 30px;
    letter-spacing: -1px;
  }
  
  .description {
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.7;
    letter-spacing: 0.3px;
  }
  
  /* Dark box content */
  .statistic {
    font-size: 96px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
    margin-bottom: 25px;
    letter-spacing: -3px;
  }
  
  .stat-label {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
  }
  
  .stat-description {
    font-size: 16px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.7;
    letter-spacing: 0.3px;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .grid-box:nth-child(1) {
    animation: scaleIn 0.6s ease-out 0.2s both;
  }
  
  .grid-box:nth-child(2) {
    animation: scaleIn 0.6s ease-out 0.3s both;
  }
  
  .grid-box:nth-child(3) {
    animation: scaleIn 0.6s ease-out 0.4s both;
  }
  
  .grid-box:nth-child(4) {
    animation: scaleIn 0.6s ease-out 0.5s both;
  }
  
  .grid-box:nth-child(5) {
    animation: scaleIn 0.6s ease-out 0.6s both;
  }
  
  .grid-box:nth-child(6) {
    animation: scaleIn 0.6s ease-out 0.7s both;
  }
</style>

<div class="logo-section">
  <div class="logo-circle"></div>
  <div class="logo-text">Business Enterprise</div>
</div>

<div class="grid-container">
  <div class="grid-box box-gray"></div>
  
  <div class="grid-box box-blue">
    <h1 class="main-heading">Our Success Story</h1>
    <p class="description">
      Our journey reflects dedication to excellence, innovation, and sustainable growth. Through strategic partnerships and customer-centric approaches, we've achieved remarkable milestones.
    </p>
  </div>
  
  <div class="grid-box box-gray"></div>
  
  <div class="grid-box box-gray"></div>
  
  <div class="grid-box box-gray"></div>
  
  <div class="grid-box box-dark">
    <div class="statistic">6.18%</div>
    <h2 class="stat-label">Market Research</h2>
    <p class="stat-description">
      Comprehensive market analysis driving informed strategic decisions and competitive positioning in dynamic business environments.
    </p>
  </div>
</div>
</body>
</html>