<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Enterprise Presentation - Slide 14</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
        body {
            height: 1080px;
            width: 1920px;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
    width: 1920px;
    height: 1080px;
    display: flex;
  }
  
  /* Split layout */
  .left-side {
    width: 50%;
    background: #e5e5e5;
    position: relative;
  }
  
  .right-side {
    width: 50%;
    background: #0a0a0a;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 100px 120px;
  }
  
  /* Logo section */
  .logo-section {
    position: absolute;
    top: 40px;
    left: 40px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 10;
  }
  
  .logo-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .logo-circle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(255, 255, 255, 0.1) 3px,
      rgba(255, 255, 255, 0.1) 6px
    );
  }
  
  .logo-text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 500;
    letter-spacing: -0.5px;
  }
  
  /* Right side content */
  .main-heading {
    font-size: 82px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 100px;
    letter-spacing: -1px;
  }
  
  /* Contact sections */
  .contact-section {
    margin-bottom: 80px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 30px;
  }
  
  .icon-circle {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: transparent;
    border: 3px solid #3d85ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    order: 2;
  }
  
  .arrow-icon {
    width: 24px;
    height: 24px;
    position: relative;
  }
  
  .arrow-icon::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 3px;
    background: #3d85ff;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  
  .arrow-icon::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border-right: 3px solid #3d85ff;
    border-top: 3px solid #3d85ff;
    top: 50%;
    right: 2px;
    transform: translateY(-50%) rotate(45deg);
  }
  
  .contact-content {
    flex: 1;
    border-bottom: 1px solid #333333;
    padding-bottom: 30px;
  }
  
  .contact-section:last-child .contact-content {
    border-bottom: none;
  }
  
  .section-title {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
  }
  
  .contact-info {
    font-size: 20px;
    font-weight: 400;
    color: #a3a3a3;
    line-height: 1.8;
    letter-spacing: 0.3px;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  .left-side {
    animation: fadeIn 0.8s ease-out;
  }
  
  .logo-section {
    animation: fadeIn 0.8s ease-out;
  }
  
  .main-heading {
    animation: slideInRight 0.8s ease-out 0.2s both;
  }
  
  .contact-section:nth-child(1) {
    animation: slideInRight 0.8s ease-out 0.4s both;
  }
  
  .contact-section:nth-child(2) {
    animation: slideInRight 0.8s ease-out 0.6s both;
  }
  
  .contact-section:nth-child(3) {
    animation: slideInRight 0.8s ease-out 0.8s both;
  }
</style>

<div class="left-side">
  <div class="logo-section">
    <div class="logo-circle"></div>
    <div class="logo-text">Business Enterprise</div>
  </div>
</div>

<div class="right-side">
  <h1 class="main-heading">Contact<br>Information's</h1>
  
  <div class="contact-section">
    <div class="icon-circle">
      <div class="arrow-icon"></div>
    </div>
    <div class="contact-content">
      <h2 class="section-title">Office Hours</h2>
      <p class="contact-info">Monday – Saturday<br>08.00 AM – 08.00 PM</p>
    </div>
  </div>
  
  <div class="contact-section">
    <div class="icon-circle">
      <div class="arrow-icon"></div>
    </div>
    <div class="contact-content">
      <h2 class="section-title">Address</h2>
      <p class="contact-info">123 Street Name, City Name</p>
    </div>
  </div>
  
  <div class="contact-section">
    <div class="icon-circle">
      <div class="arrow-icon"></div>
    </div>
    <div class="contact-content">
      <h2 class="section-title">Get In Touch</h2>
      <p class="contact-info">(+62) 000 0000 0000<br>(0725) 00000</p>
    </div>
  </div>
</div>
</body>
</html>