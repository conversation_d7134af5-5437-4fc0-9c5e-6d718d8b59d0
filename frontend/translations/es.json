{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "saving": "Guardando...", "success": "Éxito", "done": "Listo", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "Sí", "no": "No", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "search": "Buscar", "loading": "Cargando...", "error": "Error", "retry": "Reintentar", "send": "Enviar", "back": "Atrás", "new": "Nuevo", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "remove": "Eliminar", "confirm": "Confirmar", "processing": "Procesando...", "keep": "<PERSON><PERSON><PERSON>", "active": "Activo", "inactive": "Inactivo", "next": "Siguient<PERSON>", "upgrade": "<PERSON><PERSON><PERSON>", "basic": "Básico", "tryFree": "Comenzar"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"general": "General", "plan": "Plan", "billing": "Facturación", "usage": "<PERSON><PERSON>", "knowledgeBase": "Base de Conocimientos", "integrations": "Integraciones", "envManager": "Administrador de Entorno"}, "general": {"title": "Configuración del Perfil", "description": "Gestiona la información de tu cuenta", "name": "Nombre", "email": "Email", "namePlaceholder": "Ingresa tu nombre", "emailCannotChange": "El email no se puede cambiar desde aquí", "saveChanges": "Guardar Cambios", "profileUpdated": "Perfil actualizado correctamente", "profileUpdateFailed": "Error al actualizar el perfil", "language": {"title": "Idioma", "description": "Elige tu idioma preferido", "current": "Idioma Actual", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deleteAccount": {"title": "Eliminar Cuenta", "description": "Elimina permanentemente tu cuenta y todos los datos asociados", "button": "Eliminar Cuenta", "scheduled": "Eliminación Programada", "scheduledDescription": "Tu cuenta será eliminada permanentemente el {date}.", "canCancel": "<PERSON>uedes cancelar esta solicitud en cualquier momento antes de la fecha de eliminación.", "cancelButton": "Cancelar Solicitud de Eliminación", "dialogTitle": "Eliminar Cuenta", "warning": "Esta acción no se puede deshacer después de 30 días", "whenDelete": "<PERSON>uando eliminas tu cuenta:", "agentsDeleted": "Todos tus agentes y versiones de agentes serán eliminados", "threadsDeleted": "Todos tus hilos y conversaciones serán eliminados", "credentialsRemoved": "Todas tus credenciales e integraciones serán eliminadas", "subscriptionCancelled": "Tu suscripción será cancelada", "billingRemoved": "Todos los datos de facturación serán eliminados", "scheduled30Days": "Tu cuenta será programada para eliminación en 30 días", "gracePeriod": "Puedes cancelar esta solicitud en cualquier momento durante el período de gracia de 30 días. Después de 30 días, todos tus datos serán eliminados permanentemente y no se podrán recuperar.", "confirmText": "Escribe eliminar para confirmar", "confirmPlaceholder": "delete", "keepAccount": "<PERSON><PERSON><PERSON>", "cancelDeletionTitle": "Cancelar Eliminación de Cuenta", "cancelDeletionDescription": "¿Estás seguro de que quieres cancelar la eliminación de tu cuenta? Tu cuenta y todos los datos serán preservados.", "cancelDeletion": "Cancelar Eliminación"}}, "billing": {"title": "Facturación y Suscripción", "description": "Gestiona tus créditos y suscripción", "insufficientCredits": "Créditos insuficientes", "insufficientCreditsMessage": "Verificación de facturación fallida: Créditos insuficientes. Tu saldo es {balance}. Por favor, añade créditos para continuar.", "creditsExhausted": "Te has quedado sin créditos. Actualiza ahora.", "billingCheckFailed": "Verificación de facturación fallida. Por favor, actualiza para continuar.", "failedToStartAgent": "Error al iniciar el agente {agentName}: {message}"}, "usage": {"title": "<PERSON><PERSON>"}, "knowledgeBase": {"opening": "Abriendo Base de Conocimientos", "redirecting": "Redirigiendo a la página de Base de Conocimientos..."}, "integrations": "Integraciones"}, "triggers": {"getStarted": "Comienza agregando un trigger", "getStartedDescription": "Programa un trigger para automatizar acciones y recibir recordatorios cuando se completen.", "search": "Buscar", "scheduledTrigger": "<PERSON><PERSON>", "eventBasedTrigger": "<PERSON><PERSON> en <PERSON>o", "chooseAgent": "Elegir <PERSON>e", "schedule": "Programar", "agentInstructions": "Instrucciones del Agente", "agentInstructionsDescription": "Prompt personalizado para el agente", "assignedAgent": "<PERSON><PERSON>", "unknownAgent": "Agente Desconocido", "technicalDetails": "Detalles Técnicos", "type": "Tipo", "provider": "<PERSON><PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Última Actualización", "enable": "Activar", "disable": "Desactivar", "deleteTask": "Eliminar <PERSON>", "deleteTaskDescription": "¿Estás seguro de que quieres eliminar \"{name}\"? Esta acción no se puede deshacer y detendrá todas las ejecuciones automáticas de esta tarea.", "taskEnabled": "<PERSON><PERSON> activada", "taskDisabled": "Tarea desactivada", "toggleTaskFailed": "Error al alternar tarea", "taskDeletedSuccess": "Tarea eliminada correctamente", "deleteTaskFailed": "Error al eliminar tarea", "taskUpdatedSuccess": "Tarea actualizada correctamente", "updateTaskFailed": "Error al actualizar tarea", "pleaseSelectAgent": "Por favor selecciona un agente", "defaultScheduledTriggerName": "<PERSON><PERSON>", "defaultScheduledTriggerDescription": "Trigger programado automáticamente", "scheduleTriggerUpdated": "Trigger programado actualizado correctamente", "scheduleTriggerCreated": "Trigger programado creado correctamente", "upgradeForMoreScheduledTriggers": "Mejora para crear más triggers programados", "upgradeForMoreAppTriggers": "Mejora para crear más triggers basados en app", "updateScheduleTriggerFailed": "Error al actualizar trigger programado", "createScheduleTriggerFailed": "Error al crear trigger programado", "editScheduledTask": "<PERSON>ar <PERSON>", "createScheduledTask": "<PERSON><PERSON><PERSON>", "editAppBasedTask": "Editar <PERSON> en App", "createAppBasedTask": "<PERSON><PERSON><PERSON> en <PERSON>pp", "updateAgentForTask": "Actualizar el agente para esta tarea", "selectAgentForTask": "Primero, selecciona qué agente debe manejar esta tarea", "createNew": "<PERSON><PERSON><PERSON> nuevo", "notConfigured": "No configurado", "every15Minutes": "Cada 15 minutos", "every30Minutes": "Cada 30 minutos", "everyHour": "<PERSON><PERSON> hora", "dailyAt9AM": "Diariamente a las 9 AM", "weekdaysAt9AM": "Días laborables a las 9 AM", "weeklyOnMonday": "Semanalmente el lunes", "monthlyOn1st": "Mensualmente el día 1"}, "sidebar": {"chats": "Chats", "agents": "<PERSON><PERSON>", "workers": "Workers", "triggers": "Triggers", "newChat": "Nuevo Chat", "newAgent": "Nuevo Agente", "settings": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "openMenu": "<PERSON><PERSON><PERSON>", "expandSidebar": "Expandir barra lateral (CMD+B)", "searchChats": "Buscar chats", "searchChatsPlaceholder": "Buscar chats...", "allTriggers": "Todos los Triggers", "addWorkers": "Agregar Workers", "addTrigger": "<PERSON><PERSON><PERSON><PERSON>", "triggerConfig": "Configuración de Trigger", "scheduledTrigger": "<PERSON><PERSON>", "scheduledTriggerDescription": "Programar un trigger para ejecutar en un momento específico", "eventBasedTrigger": "<PERSON><PERSON> en <PERSON>o", "eventBasedTriggerDescription": "<PERSON>rear un trigger para ejecutar cuando ocurre un evento", "noChatsFound": "No se encontraron chats", "noConversations": "Aún no hay chats", "search": "Buscar", "searchPlaceholder": "Buscar...", "searchResults": "Resultados de Búsqueda", "recent": "Reciente", "personalAccount": "Cuenta Personal", "workspaces": "Espacios de Trabajo", "theme": "<PERSON><PERSON>", "integrations": "Integraciones", "billing": "Facturación", "usage": "<PERSON><PERSON>", "adminPanel": "Panel Admin", "apiKeys": "Claves API", "envManager": "Administrador de Entorno", "advanced": "<PERSON><PERSON><PERSON>", "plan": "Plan", "knowledgeBase": "Base de Conocimientos", "conversation": "Conversación", "conversations": "Conversaciones", "startNewChat": "Iniciar un nuevo chat", "openInNewTab": "Abrir en nueva pestaña", "delete": "Eliminar", "deleting": "Eliminando", "allThreadsLoaded": "Todos los hilos cargados", "myWorkforce": "Mi Fuerza Laboral", "addTeam": "Agregar <PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "deselectAll": "<PERSON>elecci<PERSON><PERSON> todo", "selectAllConversations": "Seleccionar todas las conversaciones", "deselectAllConversations": "Deseleccionar todas las conversaciones", "loadMore": "<PERSON>gar más", "remaining": "restantes"}, "dashboard": {"title": "Panel", "welcome": "Bienvenido", "whatWouldYouLike": "¿Qué quieres hacer?", "describeWhatYouNeed": "Describe con qué necesitas ayuda...", "usageLimits": "Límites de Uso", "threads": "<PERSON><PERSON>", "customWorkers": "Workers Personalizados", "scheduledTriggers": "Triggers Programados", "appTriggers": "<PERSON><PERSON><PERSON> <PERSON>", "limitsExceeded": "Has alcanzado tu límite de chats ({current}/{limit}). Mejora tu plan para crear más chats.", "agentInitiatedSuccessfully": "Agente iniciado correctamente", "aiAssistantReady": "Tu asistente de IA está listo para ayudar"}, "thread": {"integrations": "Integraciones", "connect": "Conectar", "seeAllIntegrations": "+ Ver todas las integraciones", "connectIntegrations": "Conectar integraciones", "adaptive": "Adaptativo", "adaptiveDescription": "Respuestas rápidas con cambio inteligente de contexto", "autonomous": "Autónomo", "autonomousDescription": "Modo de trabajo profundo para resolución de problemas en varios pasos", "chat": "Cha<PERSON>", "chatDescription": "Conversación simple de ida y vuelta", "sampleAnswers": "Respuestas de ejemplo", "samplePrompts": "Prompts de ejemplo", "waitingForUserResponse": "Kortix continuará trabajando de forma autónoma después de tu respuesta.", "taskCompleted": "<PERSON><PERSON> completada", "uploadingFiles": "Subiendo {count} archivo{plural}...", "uploadingFilesOne": "Subiendo {count} archivo...", "uploadingFilesMany": "Subiendo {count} archivos...", "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "tools": "Herramientas", "instructions": "Instrucciones", "knowledge": "Conocimiento", "triggers": "Triggers", "agents": "<PERSON><PERSON>", "superWorker": "Super Worker", "searchWorkers": "Buscar workers...", "myWorkers": "Mis Workers", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minimizeToFloatingPreview": "Minimizar a vista previa flotante", "close": "<PERSON><PERSON><PERSON>", "noActionsYet": "Aún no hay acciones", "workerActionsDescription": "Las acciones y resultados del Worker aparecerán aquí mientras se ejecutan", "toolIsRunning": "La herramienta está ejecutando", "toolCurrentlyExecuting": "{toolName} está ejecutando actualmente. Los resultados aparecerán aquí cuando se complete.", "rateThisResult": "Califica este resultado", "feedbackHelpsImprove": "Tu comentario nos ayuda a mejorar", "additionalFeedbackOptional": "Comentario adicional (opcional)", "helpKortixImprove": "Ayuda a Kortix a mejorar con tu comentario", "submit": "Enviar", "submitting": "Enviando...", "feedbackSubmittedSuccess": "Comentario enviado con éxito", "feedbackSubmitFailed": "Error al enviar el comentario"}, "agents": {"title": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "createNewWorker": "Crear Nuevo Worker", "edit": "<PERSON><PERSON>", "delete": "Eliminar Agente", "name": "Nombre", "description": "Descripción", "model": "<PERSON><PERSON>", "systemPrompt": "Prompt del Sistema", "systemPromptPlaceholder": "Define cómo debe comportarse tu agente...", "customizeIcon": "Haz clic para personalizar el icono del agente"}, "home": {"heroPlaceholder": "Describe la tarea que quieres que tu Worker complete..."}, "showcase": {"title": "Kortix: Tu Worker de IA Autónomo", "subtitle": "Construido para tareas complejas, diseñado para todo. El asistente de IA definitivo que maneja todo—desde solicitudes simples hasta proyectos mega-complejos.", "tryItOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kortixComputer": "Kortix Computer", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "workers": {"images": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Crea imágenes bajo demanda. Desde fotos de productos hasta gráficos para redes sociales y ilustraciones completas. Ajusta estilo, iluminación, colores y diseño, o refina visuales existentes con ediciones rápidas y retoques.", "capabilities": {"0": "Generar fotos de productos", "1": "<PERSON><PERSON>r gráficos para redes sociales", "2": "<PERSON><PERSON> ilust<PERSON>", "3": "Variaciones de estilo e iluminación", "4": "Creación de logos / recursos", "5": "+ <PERSON><PERSON> más"}, "imageAlt": "Ejemplo gráfico El crecimiento no es lineal", "fileType": "image"}, "slides": {"title": "Presentaciones", "description": "Crea presentaciones impresionantes al instante. Desde pitch decks hasta informes y materiales de capacitación. Ajusta temas, diseños, estructura de contenido o refina mazos existentes con ediciones y actualizaciones rápidas.", "capabilities": {"0": "Pitch decks", "1": "Material de capacitación", "2": "Presentaciones de informes", "3": "Variaciones de tema y diseño", "4": "Reestructuración de contenido", "5": "+ <PERSON><PERSON> más"}, "imageAlt": "Ejemplo de diapositiva Nexus Enterprise Automation Platform", "fileType": "PPTX"}, "data": {"title": "Datos", "description": "Transforma datos sin procesar en insights. Desde hojas de cálculo hasta dashboards y visualizaciones. Limpia conjuntos de datos, crea gráficos, genera informes o refina análisis existentes con actualizaciones rápidas.", "capabilities": {"0": "Dashboards", "1": "Visualizaciones", "2": "Informes de datos", "3": "Limpiar y organizar datos", "4": "Generar insights", "5": "+ <PERSON><PERSON> más"}, "imageAlt": "Ejemplo de Dashboard de Modelo Financiero", "fileType": "Vista previa"}, "docs": {"title": "Documentos", "description": "Escribe y edita documentos sin esfuerzo. Desde propuestas hasta guías y piezas de contenido. Ajusta tono, estructura, formato o refina documentos existentes con reescrituras rápidas y pulido.", "capabilities": {"0": "Propuestas", "1": "Guías y manuales", "2": "Piezas de contenido", "3": "Variaciones de tono y estilo", "4": "Formatear y reestructurar", "5": "+ <PERSON><PERSON> más"}, "imageAlt": "Ejemplo de Informe Ejecutivo Resumen Q3 2025", "fileType": "PDF"}, "research": {"title": "Investigación", "description": "Investiga temas de manera integral. Desde tendencias de mercado hasta análisis competitivo y estudios en profundidad. Recopila fuentes, sintetiza hallazgos o refina investigaciones existentes con actualizaciones rápidas.", "capabilities": {"0": "<PERSON><PERSON><PERSON> tenden<PERSON> de mercado", "1": "Investigación competitiva", "2": "Estudios en profundidad de temas", "3": "Recopilar fuentes", "4": "<PERSON><PERSON><PERSON><PERSON>", "5": "+ <PERSON><PERSON> más"}, "imageAlt": "Ejemplo de investigación Perfiles Detallados de Competidores", "fileType": "PDF"}}}, "threads": {"title": "<PERSON><PERSON>", "newThread": "Nuevo Hilo", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "placeholder": "Escribe tu mensaje..."}, "billing": {"title": "Facturación & Suscripción", "subscription": "Suscripción", "credits": "C<PERSON>dit<PERSON>", "popular": "Popular", "creditsExplained": "Créditos explicados", "creditsExhaustedRefreshIn": "Has agotado tus créditos. Se renovarán en {time}", "trialActive": "Prueba Activa", "trialBadge": "Prueba de 7 Días", "currentPlan": "Plan Actual", "currentBadge": "Actual", "scheduled": "Programado", "scheduledBadge": "Programado", "loading": "Cargando información de facturación...", "subscriptionChangeScheduled": "Cambio de suscripción programado", "planWillChangeOn": "Tu plan cambiará el {date}.", "failedToInitiateSubscription": "Error al iniciar suscripción. Intenta de nuevo.", "subscriptionUpgraded": "Suscripción mejorada de {currentPrice} a {newPrice}", "subscriptionUpdated": "Suscripción actualizada correctamente", "cannotDowngradeDuringCommitment": "No puedes degradar durante el período de compromiso", "alreadyOnThisPlan": "Ya estás en este plan.", "localModeMessage": "Ejecutando en modo de desarrollo local - funciones de facturación deshabilitadas", "creditsExplainedPage": {"title": "Créditos Explicados", "subtitle": "Todo lo que necesitas saber sobre cómo funcionan los créditos en Kortix", "understandingCredits": {"title": "Entendiendo los Créditos", "description": "Los créditos sirven como moneda universal de Kortix para las operaciones de la plataforma. Cada acción que tus agentes de IA realizan—desde analizar datos hasta generar código—consume créditos según la complejidad de la tarea y los recursos necesarios."}, "howCreditsWork": {"title": "Cómo Funcionan los Créditos", "description": "Los créditos se consumen según los recursos que tus agentes de IA usan:", "aiModelUsage": {"title": "Uso del Modelo de IA", "description": "El principal factor de consumo de créditos", "content": "Diferentes modelos de IA tienen costos diferentes según sus capacidades y uso de tokens. Los créditos se consumen para tokens de entrada (tus prompts y contexto), tokens de salida (respuestas del agente) y varían según el nivel del modelo (Claude, GPT, etc.)."}, "pricingModel": {"title": "<PERSON><PERSON>", "description": "Margen del 20% sobre los costos del modelo de IA", "content": "Aplicamos un margen del 20% en todos los costos de API y modelo para cubrir la infraestructura de la plataforma, seguridad y desarrollo continuo. Este precio transparente asegura que sepas exactamente por qué estás pagando."}}, "gettingMoreCredits": {"title": "Obtener Más <PERSON>", "description": "Hay varias formas de obtener créditos en Kortix:", "monthlySubscription": {"title": "Créditos de Suscripción Mensual", "description": "Incluidos en tu plan de pago y renovados automáticamente cada mes. Estos son créditos con expiración."}, "topUpCredits": {"title": "Créditos de Recarga", "description": "Compra créditos adicionales cuando los necesites. Estos no expiran y están disponibles para miembros premium."}, "promotionalGrants": {"title": "Concesiones Promocionales y de Eventos", "description": "Créditos bonus de eventos especiales, promociones o referidos. Estos no expiran."}, "refunds": {"title": "Reembolsos", "description": "Créditos devueltos debido a problemas técnicos o tareas fallidas. Estos no expiran."}}, "typesOfCredits": {"title": "Tipos de Créditos", "description": "Kortix usa dos tipos de créditos para darte flexibilidad en cómo gestionas tu uso:", "expiringCredits": {"title": "Créditos con Expiración", "description": "Créditos de suscripción mensual", "content": "Estos créditos están incluidos en tu suscripción de pago y se renuevan automáticamente cada mes en la fecha de tu suscripción. Expiran al final de cada ciclo de facturación y siempre se consumen primero antes de cualquier crédito sin expiración."}, "nonExpiringCredits": {"title": "Créditos sin Expiración", "description": "Créditos permanentes que nunca expiran", "content": "Estos créditos nunca expiran y se transfieren de mes a mes. Incluyen compras de recarga, reembolsos y concesiones promocionales. Los créditos sin expiración solo se usan después de que tus créditos con expiración se hayan agotado."}, "creditPriority": {"title": "Prioridad de Créditos:", "description": "Cuando usas Kortix, los créditos con expiración se consumen primero. Solo después de que tus créditos con expiración se agoten se usarán los créditos sin expiración."}}}, "close": "<PERSON><PERSON><PERSON>", "cancelScheduledChange": "Cancelar Cambio Programado", "threadUsage": "<PERSON><PERSON>", "error": "Error", "totalUsage": "Uso Total", "usage": "<PERSON><PERSON>", "thread": "<PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "creditsUsed": "Créditos <PERSON>", "creditTransactions": "Transacciones de Crédito", "loadingTransactionHistory": "Cargando tu historial de transacciones...", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "discount": "15% de descuento", "perMonth": "/mes", "billedYearly": "facturado anualmente", "downgradePending": "Degradación Pendiente", "downgrade": "Degradar", "switchToLegacyYearly": "Cambiar a Anual Legacy", "pickPlan": "Elige el plan que funciona para ti.", "reachedLimit": "Has alcanzado tus límites.", "projectLimit": "Límite de Proyecto ({current}/{limit})", "threadLimit": "<PERSON><PERSON><PERSON> de Hilo ({current}/{limit})", "workerLimit": "<PERSON><PERSON><PERSON> de Worker ({current}/{limit})", "triggerLimit": "<PERSON><PERSON><PERSON> ({current}/{limit})", "getAdditionalCredits": "Obtener Créditos Adicionales", "selectPlan": "Seleccionar Plan", "changeScheduled": "Cambio Programado", "notAvailable": "No Disponible", "features": {"creditsPerMonth": "{count} créditos/mes", "customWorker": "{count} Worker personalizado", "customWorkers": "{count} Workers personalizados", "privateProject": "{count} proyecto privado", "privateProjects": "Proyectos privados", "customTrigger": "{count} trigger personalizado", "integrations": "100+ integraciones", "premiumAIModels": "Modelos de IA Premium", "prioritySupport": "Soporte Prioritario"}}, "auth": {"logIntoAccount": "Inicia sesión en tu cuenta", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "signInOrCreateAccount": "Inicia sesión o crea tu cuenta de Kortix", "continueWithGoogle": "Continuar con <PERSON>", "continueWithGitHub": "Continuar con GitHub", "orEmail": "o email", "emailAddress": "Dirección de email", "password": "Contraseña", "confirmPassword": "Confirma tu contraseña", "signIn": "In<PERSON><PERSON>", "signUp": "Registrarse", "signingIn": "Iniciando se<PERSON>...", "creatingAccount": "<PERSON><PERSON>ndo cuenta...", "forgotPassword": "¿Olvidaste tu contraseña?", "dontHaveAccount": "¿No tienes cuenta?", "alreadyHaveAccount": "¿Ya tienes cuenta?", "acceptPrivacyTerms": "Acepto la <privacyPolicy>Política de Privacidad</privacyPolicy> y los <termsOfService>Términos de Servicio</termsOfService>", "privacyPolicy": "Política de Privacidad", "termsOfService": "Términos de Servicio", "checkYourEmail": "Revisa tu email", "confirmationLinkSent": "Te enviamos un enlace a", "clickLinkToActivate": "Haz clic en el enlace del email para activar tu cuenta. Si no ves el email, revisa tu carpeta de spam.", "returnToHome": "Volver al inicio", "backToSignIn": "<PERSON>ver al login", "resetPassword": "Restablecer contraseña", "resetPasswordDescription": "Ingresa tu email y te enviaremos un link para restablecer tu contraseña", "sendResetLink": "Enviar link", "cancel": "<PERSON><PERSON><PERSON>", "pleaseEnterValidEmail": "Por favor ingresa una dirección de email válida", "signInFailed": "Error al iniciar sesión. Verifica tus datos.", "signUpFailed": "Error al registrarse. Intenta de nuevo.", "sendMagicLink": "Enviar magic link", "sending": "Enviando...", "magicLinkSent": "Enviamos un magic link a", "magicLinkDescription": "Haz clic en el enlace de tu email para iniciar sesión. El enlace expirará en 1 hora.", "magicLinkExplanation": "Te enviaremos un enlace seguro para iniciar sesión. No se necesita contraseña.", "byContinuingYouAgree": "<PERSON> continuar, aceptas nuestra <privacyPolicy>Política de Privacidad</privacyPolicy> y nuestros <termsOfService>Términos de Servicio</termsOfService>"}, "onboarding": {"welcome": "Bienvenido", "next": "Siguient<PERSON>", "skip": "Saltar"}, "tools": {"executeCommand": "<PERSON>je<PERSON><PERSON><PERSON> comando", "checkCommandOutput": "Verificando salida del comando", "terminateCommand": "Terminando comando", "listCommands": "Listando comandos", "createFile": "Creando archivo", "deleteFile": "Eliminando archivo", "fullFileRewrite": "Reescribiendo archivo", "strReplace": "Editando texto", "editFile": "Editando archivo", "uploadFile": "Subiendo archivo", "createDocument": "<PERSON>reando documento", "updateDocument": "Actualizando documento", "readDocument": "Leyendo documento", "listDocuments": "Listando documentos", "deleteDocument": "Eliminando documento", "createTasks": "<PERSON><PERSON><PERSON> tareas", "updateTasks": "Actualizando tareas", "browserNavigateTo": "Navegando a página", "browserAct": "Ejecutando acción", "browserExtractContent": "Extrayendo contenido", "browserScreenshot": "Tomando captura de pantalla", "executeDataProviderCall": "<PERSON><PERSON><PERSON><PERSON><PERSON> de datos", "getDataProviderEndpoints": "Obteniendo endpoints", "ask": "Preguntar", "wait": "<PERSON><PERSON><PERSON>", "complete": "Completando tarea", "crawlWebpage": "Rastreando sitio web", "exposePort": "Exponiendo puerto", "scrapeWebpage": "Raspando página web", "webSearch": "Buscando en web", "loadImage": "Cargando imagen", "createPresentationOutline": "Creando esquema de presentación", "createPresentation": "<PERSON>reando presentac<PERSON>", "presentPresentation": "<PERSON><PERSON><PERSON>", "clearImagesFromContext": "Limpiando imágenes del contexto", "imageSearch": "Buscando imagen", "createSheet": "<PERSON><PERSON><PERSON> hoja", "updateSheet": "Actualizando hoja", "viewSheet": "Viendo hoja", "analyzeSheet": "<PERSON><PERSON><PERSON><PERSON>ja", "visualizeSheet": "Visualizando hoja", "formatSheet": "Formateando hoja", "updateAgent": "Actualizando agente", "getCurrentAgentConfig": "Obteniendo configuración del agente", "searchMcpServers": "Buscando servidores MCP", "getMcpServerTools": "Obteniendo herramientas del servidor MCP", "configureMcpServer": "Configurand<PERSON> servidor MC<PERSON>", "getPopularMcpServers": "Obteniendo servidores MCP populares", "testMcpServerConnection": "Probando conexión del servidor MCP", "listAppEventTriggers": "Encontrando triggers de evento", "createEventTrigger": "<PERSON><PERSON>ndo trigger de evento", "getProjectStructure": "Obteniendo estructura del proyecto", "buildProject": "Construyendo proyecto", "makePhoneCall": "Haciendo llamada", "endCall": "Terminando llamada", "getCallDetails": "Obteniendo de<PERSON><PERSON> de la llamada", "listCalls": "<PERSON><PERSON><PERSON>", "monitorCall": "<PERSON><PERSON><PERSON>", "waitForCallCompletion": "Esperando finalización", "getPaperDetails": "Obteniendo detalles del artículo", "searchAuthors": "Buscando autores", "getAuthorDetails": "Obteniendo detalles del autor", "getAuthorPapers": "Obteniendo artículos del autor", "getPaperCitations": "Obteniendo citas del artículo", "getPaperReferences": "Obteniendo referencias del artículo", "paperSearch": "<PERSON><PERSON><PERSON>", "discoverUserMcpServers": "Descubriendo <PERSON>", "createCredentialProfile": "<PERSON><PERSON><PERSON> perfil", "getCredentialProfiles": "Obteniendo perfiles", "configureProfileForAgent": "Agregando her<PERSON>as al agente", "createNewAgent": "Creando nuevo agente", "searchMcpServersForAgent": "Buscando servidores MCP", "createCredentialProfileForAgent": "<PERSON><PERSON>ndo perfil de credenciales", "discoverMcpToolsForAgent": "Descubriendo herramientas MCP", "configureAgentIntegration": "Configurando integración del agente", "createAgentScheduledTrigger": "Creando trigger programado", "listAgentScheduledTriggers": "Listando triggers programados del agente", "executingTool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toolExecutedSuccessfully": "Herramienta ejecutada correctamente", "toolExecutionFailed": "Error al ejecutar herramienta", "input": "Entrada", "output": "Salida", "copyFileContent": "Copiar contenido del archivo", "fileContentCopied": "Contenido del archivo copiado al portapapeles", "failedToCopyFileContent": "Error al copiar contenido del archivo", "noContentAvailable": "No hay contenido disponible", "noContentDescription": "Esta ejecución de herramienta no produjo ningún contenido de entrada o salida para mostrar.", "tool": "Herramienta"}, "agentRunLimit": {"gotIt": "Entendido", "parallelRunsLimitReached": "Límite de Ejecuciones Paralelas Alcanzado", "parallelRunsLimitDescription": "Has alcanzado el máximo de ejecuciones paralelas de agentes permitidas.", "needMoreParallelRuns": "¿Necesitas más ejecuciones paralelas?", "upgradeDescription": "Mejora tu plan para ejecutar múltiples agentes simultáneamente y aumentar tu productividad.", "upgradePlan": "Mejorar Plan", "currentlyRunningAgents": "Agentes Actualmente Ejecutando", "loadingThreads": "Cargando hilos...", "foundRunningAgents": "Encontrados {count} agentes ejecutando pero no se pudieron cargar los detalles del hilo.", "threadIds": "IDs de Hilo: {ids}", "moreRunning": "+{count} ejecutando", "whatCanYouDo": "¿Qué puedes hacer?", "stopAgentsHint": "Haz clic en el botón detener para detener agentes ejecutando", "waitForCompletion": "Espera a que un agente se complete automáticamente", "upgradeYourPlan": "Mejora tu plan", "forMoreParallelRuns": "para más ejecuciones paralelas", "stopAgent": "Detener agente", "stopThisAgent": "Detener este agente", "openThread": "<PERSON><PERSON><PERSON> hilo", "agentStoppedSuccessfully": "Agente detenido correctamente", "failedToStopAgent": "Error al detener agente", "upgradeToRunMore": "Mejora para ejecutar más agentes en paralelo"}, "errors": {"generic": "Ocurrió un error", "network": "Error de red", "unauthorized": "No autorizado", "notFound": "No encontrado"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano", "zh": "中文", "ja": "日本語", "pt": "Português", "fr": "Français", "es": "Español"}, "suna": {"samplePrompts": "Prompts de ejemplo", "chooseStyle": "Elige un estilo", "chooseTemplate": "Elige una plantilla", "chooseOutputFormat": "Elige formato de salida", "preferredCharts": "Gráficos preferidos", "whatWouldYouLike": "¿Qué quieres hacer?", "describeTask": "Describe la tarea que quieres que tu Worker complete...", "modes": {"image": "Imagen", "slides": "Slides", "data": "Datos", "docs": "Documentos", "people": "Gente", "research": "Investigación"}, "styles": {"photorealistic": "Foto", "watercolor": "<PERSON><PERSON><PERSON><PERSON>", "digital-art": "Digital", "oil-painting": "<PERSON><PERSON>", "minimalist": "Minimalista", "isometric": "Isométrico", "vintage": "Vintage", "comic": "<PERSON><PERSON><PERSON>", "neon": "Neón", "pastel": "Pastel", "geometric": "Geométrico", "abstract": "Abstracto", "anime": "Anime", "impressionist": "Impresionista", "surreal": "Surrealista"}, "templates": {"minimalist": {"name": "Minimalista", "description": "Diseño limpio y simple"}, "minimalist_2": {"name": "Minimalista 2", "description": "Estilo minimal alternativo"}, "black_and_white_clean": {"name": "Blanco y Negro", "description": "Monocromo clásico"}, "colorful": {"name": "Colorido", "description": "Vibrante y energético"}, "startup": {"name": "Startup", "description": "Dinámico e innovador"}, "elevator_pitch": {"name": "Elevator Pitch", "description": "Rápido e impactante"}, "portfolio": {"name": "Portafolio", "description": "Muestra tu trabajo"}, "textbook": {"name": "Libro de Texto", "description": "Educativo y estructurado"}, "architect": {"name": "Arquitecto", "description": "Profesional y preciso"}, "hipster": {"name": "<PERSON><PERSON>", "description": "Moderno y a la moda"}, "green": {"name": "Verde", "description": "Diseño inspirado en la naturaleza"}, "premium_black": {"name": "Negro Premium", "description": "Tema oscuro de lujo"}, "premium_green": {"name": "Verde Premium", "description": "Verde sofisticado"}, "professor_gray": {"name": "<PERSON><PERSON><PERSON>", "description": "Académico y erudito"}, "gamer_gray": {"name": "<PERSON><PERSON>", "description": "Diseño inspirado en juegos"}, "competitor_analysis_blue": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enfocado en análisis de negocios"}, "numbers_clean": {"name": "Números <PERSON>", "description": "Visualización de datos limpia"}, "numbers_colorful": {"name": "Números Coloridos", "description": "Presentación de datos vibrante"}, "prd": {"name": "PRD", "description": "Documento de requisitos del producto"}, "technical": {"name": "Técnico", "description": "Documentación técnica"}, "proposal": {"name": "Propuesta", "description": "Propuesta comercial"}, "report": {"name": "Informe", "description": "Formato de informe detallado"}, "guide": {"name": "Guía", "description": "Guía paso a paso"}, "wiki": {"name": "Wiki", "description": "Artículo de base de conocimientos"}, "policy": {"name": "Política", "description": "Documento de política"}, "meeting-notes": {"name": "Acta de Reunión", "description": "Acta de reunión"}}, "outputFormats": {"spreadsheet": {"name": "Hoja de Cálculo", "description": "Tabla con fórmulas"}, "dashboard": {"name": "Panel", "description": "Gráficos interactivos"}, "report": {"name": "Informe", "description": "An<PERSON><PERSON><PERSON> con visuales"}, "slides": {"name": "Slides", "description": "Formato de presentación"}}, "charts": {"bar": "Barras", "line": "Líneas", "pie": "Circular", "scatter": "Dispersión", "heatmap": "Mapa de calor", "bubble": "<PERSON><PERSON><PERSON><PERSON>", "wordcloud": "Nube de palabras", "stacked": "Barras apiladas", "area": "Á<PERSON>"}, "prompts": {"image": {"0": "Una majestuosa águila dorada volando a través de picos de montañas brumosas al amanecer con iluminación dramática", "1": "Retrato de primer plano de una modelo de moda con maquillaje vanguardista, iluminación de estudio, sombras de alto contraste", "2": "Sala de estar escandinava acogedora con muebles de madera natural, plantas de interior y suave luz matutina", "3": "Mercado callejero futurista cyberpunk por la noche con letreros de neón, pavimento resbaladizo por la lluvia y pantallas holográficas", "4": "Fotografía elegante de producto de frasco de perfume de lujo en superficie de mármol con reflejos suaves", "5": "Islas flotantes caprichosas conectadas por puentes de cuerda en un cielo pastel con nubes oníricas", "6": "Primer plano macro de gotas de rocío matutino en pétalos de flores vibrantes con fondo bokeh", "7": "Configuración moderna de escritorio de espacio de trabajo con laptop, café, cuaderno y plantas suculentas desde arriba", "8": "Sendero místico en el bosque con árboles antiguos, luciérnagas brillantes y haces de luz etéreos a través de la niebla", "9": "Detalle arquitectónico de fachada de edificio de vidrio contemporáneo con patrones geométricos y reflejos", "10": "Puesto vibrante de vendedor de comida callejera con ingredientes coloridos, vapor subiendo e iluminación cálida", "11": "Jardín zen japonés sereno con arena rastrillada, piedras cubiertas de musgo y pétalos de cerezo", "12": "Foto de acción dinámica de atleta a mitad del salto contra cielo de puesta de sol dramático, efecto de silueta", "13": "Cocina rústica de granja con ollas de cobre, hierbas frescas, tablas de cortar de madera y texturas naturales", "14": "Arte fluido abstracto con patrones orgánicos de oro metálico giratorio, azul profundo y verde esmeralda"}, "slides": {"0": "Crear un pitch deck de Serie A con tamaño de mercado, tracción y proyecciones financieras", "1": "Construir una revisión de negocios del Q4 mostrando KPIs, victorias e iniciativas estratégicas", "2": "Diseñar una presentación de lanzamiento de producto con videos de demostración y testimonios de clientes", "3": "Desarrollar un deck de habilitación de ventas explicando nuestra propuesta de valor y ventajas competitivas", "4": "Crear una actualización para inversores destacando métricas clave y próximos hitos", "5": "Construir una presentación de estudio de caso del cliente mostrando ROI y métricas de éxito", "6": "Diseñar una presentación all-hands cubriendo actualizaciones de la empresa y visión", "7": "Desarrollar un deck de entrenamiento para nuevas características y flujos de trabajo del producto", "8": "Crear una charla de conferencia sobre escalar equipos de ingeniería", "9": "Construir una presentación de reunión de junta con recomendaciones estratégicas"}, "data": {"0": "Construir un modelo financiero proyectando crecimiento de ARR con diferentes escenarios de precios", "1": "Crear un panel de ventas interactivo rastreando métricas por región y trimestre", "2": "Analizar 50 mil reseñas de clientes y visualizar tendencias de sentimiento a lo largo del tiempo", "3": "Diseñar un calendario de contenido rastreando campañas con gráficos de ROI y compromiso", "4": "Construir un análisis de cohorte mostrando patrones de retención y abandono de usuarios", "5": "Crear un modelo de atribución de marketing comparando el rendimiento de canales", "6": "Desarrollar un rastreador de contratación con métricas de pipeline y análisis de tiempo para llenar", "7": "Construir una hoja de cálculo de planificación de presupuesto con modelado de escenarios", "8": "<PERSON><PERSON><PERSON> datos de tráfico del sitio web y visualizar embudos de conversión", "9": "Crear un sistema de gestión de inventario con alertas automáticas de reorden"}, "docs": {"0": "Escribir un PRD completo para un motor de recomendación impulsado por IA", "1": "Redactar un documento de arquitectura técnica para una plataforma de microservicios escalable", "2": "Crear un documento de estrategia go-to-market para nuestro lanzamiento de producto Q2", "3": "Desarrollar un playbook de incorporación de 90 días para gerentes de ingeniería", "4": "Escribir una guía de documentación de API con ejemplos y mejores prácticas", "5": "Crear un manual de la empresa cubriendo cultura, políticas y beneficios", "6": "Redactar una política de privacidad de datos compatible con GDPR y CCPA", "7": "Desarrollar un playbook de éxito del cliente para cuentas empresariales SaaS", "8": "Escribir un plan de respuesta a incidentes de seguridad con procedimientos de escalación", "9": "Crear una guía de estilo completa para marca y contenido"}, "people": {"0": "Encontrar candidatos a VP de Ingeniería en startups de IA/ML Serie B+ en el área de la bahía de San Francisco con 10+ años de experiencia y historial comprobado de escalar equipos de ingeniería", "1": "Construir lista de leads de CMOs en empresas B2B SaaS ($10M-$50M ARR) que recientemente levantaron financiamiento Serie A/B - incluir patrones de email y stack tecnológico", "2": "Investigar Ingenieros Blockchain Senior con experiencia en Solidity/Rust en proyectos cripto de primer nivel, abiertos a reubicación a Dubái o Singapur", "3": "Generar lista de prospectos de fundadores técnicos en startups Seed-Serie A en IA Empresarial que levantaron $2M-$15M en los últimos 6 meses", "4": "Identificar Gerentes de Producto Senior en empresas fintech con 5-10 años de experiencia de FAANG o unicornios, hábiles en desarrollo de producto 0-1", "5": "Encontrar CIOs y VP de Ingeniería en empresas de TI de salud de mercado medio (500-5000 empleados) con presupuestos de TI de $500K+ planeando migración a la nube", "6": "Investigar VP de Ventas en empresas B2B SaaS mostrando crecimiento de 100%+ interanual, con 7+ años cerrando tratos de $100K+ y experiencia PLG", "7": "Construir lista de CTOs en empresas empresariales implementando activamente infraestructura de IA con presupuestos multimillonarios en 2024", "8": "Encontrar Diseñadores UX/UI Senior con experiencia en apps de consumidor mobile-first y portafolios de 1M+ usuarios, buscando activamente o abiertos a oportunidades", "9": "Identificar Ingenieros DevOps Senior en startups cloud-native con experiencia en Kubernetes/Terraform y 5-8 años construyendo infraestructura para 10M+ usuarios"}, "research": {"0": "<PERSON><PERSON>zar tendencias emergentes en computación cuántica y aplicaciones comerciales potenciales", "1": "Investigar los 10 principales competidores en el espacio de CRM impulsado por IA con comparación de características", "2": "Investigar requisitos regulatorios para lanzar una app fintech en la UE", "3": "Compilar aná<PERSON>is de mercado sobre tasas de adopción de vehículos eléctricos en mercados principales", "4": "Estudiar el impacto del trabajo remoto en la demanda de bienes raíces comerciales en ciudades principales", "5": "Investigar patrones de adopción Web3 entre empresas Fortune 500", "6": "<PERSON><PERSON><PERSON> sentimiento del consumidor hacia marcas de moda sostenible", "7": "Investigar los últimos desarrollos en terapia génica para enfermedades raras", "8": "Estudiar estrategias de precios de empresas exitosas de cajas de suscripción D2C", "9": "Investigar el panorama competitivo de soluciones de ciberseguridad impulsadas por IA"}}}}