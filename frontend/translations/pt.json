{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvando...", "success": "Sucesso", "done": "Pronto", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "delete": "Excluir", "edit": "<PERSON><PERSON>", "search": "Buscar", "loading": "Carregando...", "error": "Erro", "retry": "Tentar de novo", "send": "Enviar", "back": "Voltar", "new": "Novo", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "confirm": "Confirmar", "processing": "Processando...", "keep": "<PERSON><PERSON>", "active": "Ativo", "inactive": "Inativo", "next": "Próximo", "upgrade": "<PERSON><PERSON><PERSON>", "basic": "Básico", "tryFree": "<PERSON><PERSON><PERSON>"}, "settings": {"title": "Configurações", "tabs": {"general": "G<PERSON>", "plan": "Plano", "billing": "Cobrança", "usage": "<PERSON><PERSON>", "knowledgeBase": "Base de Conhecimento", "integrations": "Integrações", "envManager": "Gerenciador de Ambiente"}, "general": {"title": "Configurações do Perfil", "description": "Gerencia as informações da tua conta", "name": "Nome", "email": "Email", "namePlaceholder": "Digite teu nome", "emailCannotChange": "Email não pode ser alterado daqui", "saveChanges": "<PERSON><PERSON>", "profileUpdated": "Perfil atualizado com sucesso", "profileUpdateFailed": "Falha ao atualizar perfil", "language": {"title": "Idioma", "description": "Escolhe teu idioma preferido", "current": "Idioma Atual", "select": "Selecionar Idioma"}, "deleteAccount": {"title": "Excluir Conta", "description": "Remove permanentemente tua conta e todos os dados associados", "button": "Excluir Conta", "scheduled": "Exclusão Agendada", "scheduledDescription": "Tua conta será permanentemente excluída em {date}.", "canCancel": "Podes cancelar este pedido a qualquer momento antes da data de exclusão.", "cancelButton": "Cancelar Pedido de Exclusão", "dialogTitle": "Excluir Conta", "warning": "Esta ação não pode ser desfeita após 30 dias", "whenDelete": "Quando excluis tua conta:", "agentsDeleted": "Todos os teus agentes e versões de agentes serão excluídos", "threadsDeleted": "<PERSON><PERSON> as tuas threads e conversas serão excluídas", "credentialsRemoved": "<PERSON><PERSON> as tuas credenciais e integrações serão removidas", "subscriptionCancelled": "Tua assinatura será cancelada", "billingRemoved": "Todos os dados de cobrança serão removidos", "scheduled30Days": "Tua conta será agendada para exclusão em 30 dias", "gracePeriod": "Podes cancelar este pedido a qualquer momento durante o período de carência de 30 dias. Após 30 dias, todos os teus dados serão permanentemente excluídos e não poderão ser recuperados.", "confirmText": "Digite excluir para confirmar", "confirmPlaceholder": "delete", "keepAccount": "<PERSON><PERSON>", "cancelDeletionTitle": "Cancelar Exclusão de Conta", "cancelDeletionDescription": "Tens certeza que queres cancelar a exclusão da tua conta? Tua conta e todos os dados serão preservados.", "cancelDeletion": "<PERSON><PERSON><PERSON>"}}, "billing": {"title": "Cobrança e Assinatura", "description": "Gerencie seus créditos e assinatura", "insufficientCredits": "Créditos insuficientes", "insufficientCreditsMessage": "Verificação de cobrança falhou: Créditos insuficientes. <PERSON><PERSON> saldo é {balance}. Por favor, adicione créditos para continuar.", "creditsExhausted": "Você ficou sem créditos. Atualize agora.", "billingCheckFailed": "Verificação de cobrança falhou. Por favor, atualize para continuar.", "failedToStartAgent": "Falha ao iniciar o agente {agentName}: {message}"}, "usage": {"title": "<PERSON><PERSON>"}, "knowledgeBase": {"opening": "Abrindo Base de Conhecimento", "redirecting": "Redirecionando para a página da Base de Conhecimento..."}, "integrations": "Integrações"}, "triggers": {"getStarted": "Começa adicionando um trigger", "getStartedDescription": "Agenda um trigger para automatizar ações e receber lembretes quando completarem.", "search": "Buscar", "scheduledTrigger": "<PERSON><PERSON>", "eventBasedTrigger": "<PERSON><PERSON> em <PERSON>o", "chooseAgent": "Escolher <PERSON>e", "schedule": "Agendar", "agentInstructions": "Instruções do Agente", "agentInstructionsDescription": "Prompt personalizado para o agente", "assignedAgent": "Agente <PERSON>ribuído", "unknownAgent": "Agente Desconhecido", "technicalDetails": "<PERSON><PERSON><PERSON>", "type": "Tipo", "provider": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Última Atualização", "enable": "Ativar", "disable": "Desativar", "deleteTask": "Excluir <PERSON>", "deleteTaskDescription": "Tens certeza que queres excluir \"{name}\"? Esta ação não pode ser desfeita e vai parar todas as execuções automáticas desta tarefa.", "taskEnabled": "<PERSON><PERSON><PERSON>", "taskDisabled": "Tarefa desativada", "toggleTaskFailed": "Falha ao alternar tarefa", "taskDeletedSuccess": "Tarefa excluída com sucesso", "deleteTaskFailed": "Falha ao excluir tarefa", "taskUpdatedSuccess": "Tarefa atualizada com sucesso", "updateTaskFailed": "Falha ao atualizar tarefa", "pleaseSelectAgent": "Por favor seleciona um agente", "defaultScheduledTriggerName": "<PERSON><PERSON>", "defaultScheduledTriggerDescription": "Trigger agendado automaticamente", "scheduleTriggerUpdated": "Trigger agendado atualizado com sucesso", "scheduleTriggerCreated": "Trigger agendado criado com sucesso", "upgradeForMoreScheduledTriggers": "Melhora para criar mais triggers agendados", "upgradeForMoreAppTriggers": "Melhora para criar mais triggers baseados em app", "updateScheduleTriggerFailed": "Falha ao atualizar trigger agendado", "createScheduleTriggerFailed": "<PERSON>alha ao criar trigger agendado", "editScheduledTask": "<PERSON><PERSON>", "createScheduledTask": "<PERSON><PERSON><PERSON>", "editAppBasedTask": "Editar Tarefa Baseada em App", "createAppBasedTask": "Criar Tarefa Baseada em App", "updateAgentForTask": "Atualizar o agente para esta tarefa", "selectAgentForTask": "<PERSON>iro, seleciona qual agente deve lidar com esta tarefa", "createNew": "Criar novo", "notConfigured": "Não configurado", "every15Minutes": "A cada 15 minutos", "every30Minutes": "A cada 30 minutos", "everyHour": "A cada hora", "dailyAt9AM": "Diariamente às 9h", "weekdaysAt9AM": "Dias úteis às 9h", "weeklyOnMonday": "Semanalmente na segunda-feira", "monthlyOn1st": "Mensalmente no dia 1"}, "sidebar": {"chats": "Chats", "agents": "<PERSON><PERSON>", "workers": "Workers", "triggers": "Triggers", "newChat": "Novo Chat", "newAgent": "Novo Agente", "settings": "Configurações", "logout": "<PERSON><PERSON>", "openMenu": "Abrir menu", "expandSidebar": "Expandir barra lateral (CMD+B)", "searchChats": "Buscar chats", "searchChatsPlaceholder": "Buscar chats...", "allTriggers": "Todos os Triggers", "addWorkers": "Adicionar Workers", "addTrigger": "<PERSON><PERSON><PERSON><PERSON>", "triggerConfig": "Configuração de Trigger", "scheduledTrigger": "<PERSON><PERSON>", "scheduledTriggerDescription": "Agendar um trigger para executar em um horário específico", "eventBasedTrigger": "<PERSON><PERSON> em <PERSON>o", "eventBasedTriggerDescription": "Criar um trigger para executar quando um evento ocorrer", "noChatsFound": "<PERSON>enhum chat encontrado", "noConversations": "<PERSON>da sem chats", "search": "Buscar", "searchPlaceholder": "Buscar...", "searchResults": "Resultados da Busca", "recent": "<PERSON><PERSON>", "personalAccount": "Conta Pessoal", "workspaces": "Espaços de Trabalho", "theme": "<PERSON><PERSON>", "integrations": "Integrações", "billing": "Cobrança", "usage": "<PERSON><PERSON>", "adminPanel": "<PERSON><PERSON>", "apiKeys": "Chaves API", "envManager": "Gerenciador de Ambiente", "advanced": "Avançado", "plan": "Plano", "knowledgeBase": "Base de Conhecimento", "conversation": "Conversa", "conversations": "Conversas", "startNewChat": "Começar um novo chat", "openInNewTab": "Abrir em nova aba", "delete": "Excluir", "deleting": "Excluindo", "allThreadsLoaded": "<PERSON><PERSON> as threads carregadas", "myWorkforce": "Minha Força de Trabalho", "addTeam": "Adicionar <PERSON>", "selectAll": "Selecionar todos", "deselectAll": "Desselecionar todos", "selectAllConversations": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as conversas", "deselectAllConversations": "Desselecionar todas as conversas", "loadMore": "<PERSON><PERSON><PERSON> mais", "remaining": "restantes"}, "dashboard": {"title": "<PERSON><PERSON>", "welcome": "<PERSON><PERSON>-vindo", "whatWouldYouLike": "O que tu queres fazer?", "describeWhatYouNeed": "Descreve com o que precisas de ajuda...", "usageLimits": "Limites de Uso", "threads": "Threads", "customWorkers": "Workers Personalizados", "scheduledTriggers": "Tri<PERSON><PERSON>", "appTriggers": "<PERSON><PERSON><PERSON> <PERSON>", "limitsExceeded": "Atingiste teu limite de chats ({current}/{limit}). Melhora teu plano para criar mais chats.", "agentInitiatedSuccessfully": "Agente iniciado com sucesso", "aiAssistantReady": "Teu assistente de IA está pronto para ajudar"}, "thread": {"integrations": "Integrações", "connect": "Conectar", "seeAllIntegrations": "+ <PERSON>er todas as integrações", "connectIntegrations": "Conectar integrações", "adaptive": "Adaptativo", "adaptiveDescription": "Respostas rápidas com mudança inteligente de contexto", "autonomous": "Autônomo", "autonomousDescription": "Modo de trabalho profundo para resolução de problemas em várias etapas", "chat": "Cha<PERSON>", "chatDescription": "Conversa simples de ida e volta", "sampleAnswers": "Respostas de exemplo", "samplePrompts": "Prompts de exemplo", "waitingForUserResponse": "Kortix continuará trabalhando de forma autônoma após tua resposta.", "taskCompleted": "Tarefa concluída", "uploadingFiles": "Enviando {count} arquivo{plural}...", "uploadingFilesOne": "Enviando {count} arquivo...", "uploadingFilesMany": "Enviando {count} arquivos...", "scrollToBottom": "Rolar para baixo", "tools": "Ferramentas", "instructions": "Instruções", "knowledge": "Conhecimento", "triggers": "Triggers", "agents": "<PERSON><PERSON>", "superWorker": "Super Worker", "searchWorkers": "Buscar workers...", "myWorkers": "Meus Workers", "running": "Executando", "minimizeToFloatingPreview": "Minimizar para pré-visualização flutuante", "close": "<PERSON><PERSON><PERSON>", "noActionsYet": "Ainda sem ações", "workerActionsDescription": "Ações e resultados do Worker aparecerão aqui conforme executam", "toolIsRunning": "Ferramenta está executando", "toolCurrentlyExecuting": "{toolName} está executando no momento. Resultados aparecerão aqui quando completo.", "rateThisResult": "Avaliar este resultado", "feedbackHelpsImprove": "Teu feedback nos ajuda a melhorar", "additionalFeedbackOptional": "Feedback adicional (opcional)", "helpKortixImprove": "Ajuda Kortix a melhorar com teu feedback", "submit": "Enviar", "submitting": "Enviando...", "feedbackSubmittedSuccess": "Feedback enviado com sucesso", "feedbackSubmitFailed": "Falha ao enviar feedback"}, "agents": {"title": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "createNewWorker": "Criar Novo Worker", "edit": "<PERSON><PERSON>", "delete": "Excluir Agente", "name": "Nome", "description": "Descrição", "model": "<PERSON><PERSON>", "systemPrompt": "Prompt do Sistema", "systemPromptPlaceholder": "Define como teu agente deve se comportar...", "customizeIcon": "Clica para personalizar ícone do agente"}, "home": {"heroPlaceholder": "Descreve a tarefa que queres que teu Worker complete..."}, "showcase": {"title": "Kortix: <PERSON><PERSON> de IA Autônomo", "subtitle": "Construído para tarefas complexas, projetado para tudo. O assistente de IA definitivo que lida com tudo—de solicitações simples a projetos mega-complexos.", "tryItOut": "Experimentar", "kortixComputer": "Kortix Computer", "running": "Em execução", "workers": {"images": {"title": "Imagens", "description": "Cria imagens sob demanda. De fotos de produtos a gráficos para redes sociais até ilustrações completas. Ajusta estilo, iluminação, cores e layout, ou refina visuais existentes com edições rápidas e retoques.", "capabilities": {"0": "Gerar fotos de produtos", "1": "Criar gráficos para redes sociais", "2": "<PERSON><PERSON>r <PERSON>", "3": "Variações de estilo e iluminação", "4": "Criação de logo / recursos", "5": "+ <PERSON><PERSON> mais"}, "imageAlt": "Exemplo gráfico O crescimento não é linear", "fileType": "image"}, "slides": {"title": "Apresentações", "description": "Cria apresentações impressionantes instantaneamente. De pitch decks a relatórios até materiais de treinamento. Ajusta temas, layouts, estrutura de conteúdo ou refina apresentações existentes com edições e atualizações rápidas.", "capabilities": {"0": "Pitch decks", "1": "Material de treinamento", "2": "Apresentações de relatórios", "3": "Variações de tema e layout", "4": "Reestruturação de conteúdo", "5": "+ <PERSON><PERSON> mais"}, "imageAlt": "Exemplo de slide Nexus Enterprise Automation Platform", "fileType": "PPTX"}, "data": {"title": "<PERSON><PERSON>", "description": "Transforma dados brutos em insights. <PERSON> planilhas a dashboards até visualizações. Limpa conjuntos de dados, cria gráficos, gera relatórios ou refina análises existentes com atualizações rápidas.", "capabilities": {"0": "Dashboards", "1": "Visualizações", "2": "Relatórios de dados", "3": "Limpar e organizar dados", "4": "<PERSON><PERSON><PERSON> <PERSON>", "5": "+ <PERSON><PERSON> mais"}, "imageAlt": "Exemplo de dashboard modelo financeiro", "fileType": "Visualização"}, "docs": {"title": "Documentos", "description": "Escreve e edita documentos sem esforço. De propostas a guias até conteúdos. Ajusta tom, estrutura, formatação ou refina documentos existentes com reescritas rápidas e polimento.", "capabilities": {"0": "Propostas", "1": "Guias e manuais", "2": "Conte<PERSON><PERSON>", "3": "Variações de tom e estilo", "4": "Formatar e reestruturar", "5": "+ <PERSON><PERSON> mais"}, "imageAlt": "Exemplo de relatório executivo resumo Q3 2025", "fileType": "PDF"}, "research": {"title": "Pesquisa", "description": "Pesquisa tópicos de forma abrangente. De tendências de mercado a análises competitivas até estudos aprofundados. Coleta fontes, sintetiza descobertas ou refina pesquisas existentes com atualizações rápidas.", "capabilities": {"0": "<PERSON><PERSON>ar tend<PERSON> de mercado", "1": "Pesquisa competitiva", "2": "Estudos aprofundados de tópicos", "3": "Coletar fontes", "4": "<PERSON><PERSON><PERSON><PERSON>", "5": "+ <PERSON><PERSON> mais"}, "imageAlt": "Exemplo de pesquisa Perfis detalhados de concorrentes", "fileType": "PDF"}}}, "threads": {"title": "Threads", "newThread": "Nova Thread", "sendMessage": "Enviar mensagem", "placeholder": "Digite tua mensagem..."}, "billing": {"title": "Faturação & Assinatura", "subscription": "Assinatura", "credits": "C<PERSON>dit<PERSON>", "popular": "Popular", "creditsExplained": "Créditos explicados", "creditsExhaustedRefreshIn": "Voc<PERSON> esgotou seus créditos. Eles serão renovados em {time}", "trialActive": "Teste Ativo", "trialBadge": "Teste de 7 Dias", "currentPlan": "Plano Atual", "currentBadge": "Atual", "scheduled": "Agendado", "scheduledBadge": "Agendado", "loading": "Carregando informações de cobrança...", "subscriptionChangeScheduled": "Mudança de assinatura agendada", "planWillChangeOn": "<PERSON>u plano mudará em {date}.", "failedToInitiateSubscription": "Falha ao iniciar assinatura. Tenta de novo.", "subscriptionUpgraded": "Assinatura melhorada de {currentPrice} para {newPrice}", "subscriptionUpdated": "Assinatura atualizada com sucesso", "cannotDowngradeDuringCommitment": "Não podes rebaixar durante o período de compromisso", "alreadyOnThisPlan": "Já estás neste plano.", "localModeMessage": "Executando em modo de desenvolvimento local - recursos de cobrança desabilitados", "creditsExplainedPage": {"title": "Créditos Explicados", "subtitle": "Tudo o que precisas saber sobre como os créditos funcionam no Kortix", "understandingCredits": {"title": "Entend<PERSON><PERSON>", "description": "Os créditos servem como moeda universal do Kortix para operações da plataforma. Cada ação que teus agentes de IA executam—de analisar dados a gerar código—consome créditos com base na complexidade da tarefa e nos recursos necessários."}, "howCreditsWork": {"title": "Como os Créditos Funcionam", "description": "Os créditos são consumidos com base nos recursos que teus agentes de IA usam:", "aiModelUsage": {"title": "Uso do Modelo de IA", "description": "O principal fator de consumo de créditos", "content": "Diferentes modelos de IA têm custos diferentes com base em suas capacidades e uso de tokens. Os créditos são consumidos para tokens de entrada (teus prompts e contexto), tokens de saída (respostas do agente) e variam por nível do modelo (Claude, GPT, etc.)."}, "pricingModel": {"title": "Modelo de Preços", "description": "Margem de 20% sobre os custos do modelo de IA", "content": "Aplicamos uma margem de 20% em todos os custos de API e modelo para cobrir infraestrutura da plataforma, segurança e desenvolvimento contínuo. Este preço transparente garante que saibas exatamente pelo que estás pagando."}}, "gettingMoreCredits": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Existem várias formas de obter créditos no Kortix:", "monthlySubscription": {"title": "Créditos de Assinatura Mensal", "description": "Incluídos no teu plano pago e renovados automaticamente a cada mês. Estes são créditos com expiração."}, "topUpCredits": {"title": "Créditos de Recarga", "description": "Compra créditos adicionais quando precisares. Estes não expiram e estão disponíveis para membros premium."}, "promotionalGrants": {"title": "Concessões Promocionais e de Eventos", "description": "Créditos bônus de eventos especiais, promoções ou indicações. Estes não expiram."}, "refunds": {"title": "Reembolsos", "description": "Créditos devolvidos devido a problemas técnicos ou tarefas falhadas. Estes não expiram."}}, "typesOfCredits": {"title": "Tipos de Créditos", "description": "O Kortix usa dois tipos de créditos para te dar flexibilidade em como geres teu uso:", "expiringCredits": {"title": "Créditos com Expiração", "description": "Créditos de assinatura mensal", "content": "Estes créditos estão incluídos na tua assinatura paga e são renovados automaticamente a cada mês na data da tua assinatura. Eles expiram no final de cada ciclo de cobrança e são sempre consumidos primeiro antes de quaisquer créditos sem expiração."}, "nonExpiringCredits": {"title": "Créditos sem Expiração", "description": "Créditos permanentes que nunca expiram", "content": "Estes créditos nunca expiram e são transferidos de mês para mês. Eles incluem compras de recarga, reembolsos e concessões promocionais. Os créditos sem expiração são usados apenas após os teus créditos com expiração serem esgotados."}, "creditPriority": {"title": "Prioridade de Créditos:", "description": "Quando usas o Kortix, os créditos com expiração são consumidos primeiro. Apenas após os teus créditos com expiração se esgotarem, os créditos sem expiração serão usados."}}}, "close": "<PERSON><PERSON><PERSON>", "cancelScheduledChange": "<PERSON>celar <PERSON> Agendada", "threadUsage": "<PERSON><PERSON>ead", "error": "Erro", "totalUsage": "Uso Total", "usage": "<PERSON><PERSON>", "thread": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "creditsUsed": "Créditos <PERSON>", "creditTransactions": "Transações de Crédito", "loadingTransactionHistory": "Carregando teu histórico de transações...", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "discount": "15% de desconto", "perMonth": "/mês", "billedYearly": "faturado an<PERSON>mente", "downgradePending": "Rebaixamento Pendente", "downgrade": "Re<PERSON><PERSON><PERSON>", "switchToLegacyYearly": "<PERSON><PERSON> para <PERSON>", "pickPlan": "Escolhe o plano que funciona para ti.", "reachedLimit": "Atingiste teus limites.", "projectLimit": "Limite de Projeto ({current}/{limit})", "threadLimit": "<PERSON>ite de <PERSON> ({current}/{limit})", "workerLimit": "<PERSON>ite de <PERSON> ({current}/{limit})", "triggerLimit": "<PERSON><PERSON> ({current}/{limit})", "getAdditionalCredits": "Obter Cré<PERSON>", "selectPlan": "Selecionar Plano", "changeScheduled": "Mudança Agendada", "notAvailable": "Não Disponível", "features": {"creditsPerMonth": "{count} créditos/mês", "customWorker": "{count} Worker personalizado", "customWorkers": "{count} Workers personalizados", "privateProject": "{count} projeto privado", "privateProjects": "Projetos privados", "customTrigger": "{count} trigger personalizado", "integrations": "100+ integraç<PERSON>es", "premiumAIModels": "Modelos de IA Premium", "prioritySupport": "Suporte Prioritário"}}, "auth": {"logIntoAccount": "Entra na tua conta", "createAccount": "C<PERSON><PERSON> conta", "signInOrCreateAccount": "Entra ou cria a tua conta Kortix", "continueWithGoogle": "Continuar com Google", "continueWithGitHub": "Continuar com GitHub", "orEmail": "ou email", "emailAddress": "Endereço de email", "password": "<PERSON><PERSON>", "confirmPassword": "Confirma tua senha", "signIn": "Entrar", "signUp": "Cadastrar", "signingIn": "Entrando...", "creatingAccount": "C<PERSON>do conta...", "forgotPassword": "Esque<PERSON>u a senha?", "dontHaveAccount": "Não tem conta?", "alreadyHaveAccount": "Já tem conta?", "acceptPrivacyTerms": "Aceito a <privacyPolicy>Política de Privacidade</privacyPolicy> e os <termsOfService>Termos de Serviço</termsOfService>", "privacyPolicy": "Política de Privacidade", "termsOfService": "Termos de Serviço", "checkYourEmail": "Confere teu email", "confirmationLinkSent": "Enviamos um link para", "clickLinkToActivate": "Clica no link no email para ativar tua conta. Se não vires o email, confere a pasta de spam.", "returnToHome": "Voltar para home", "backToSignIn": "Voltar ao login", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPasswordDescription": "Digite seu email e vamos te enviar um link para redefinir", "sendResetLink": "Enviar link", "cancel": "<PERSON><PERSON><PERSON>", "pleaseEnterValidEmail": "Por favor digite um endereço de email válido", "signInFailed": "Falha ao entrar. Confere teus dados.", "signUpFailed": "Cadastro falhou. Tenta de novo.", "sendMagicLink": "Enviar magic link", "sending": "Enviando...", "magicLinkSent": "Enviamos um magic link para", "magicLinkDescription": "Clica no link no teu email para entrar. O link expirará em 1 hora.", "magicLinkExplanation": "Vamos te enviar um link seguro para entrar. Não precisa de senha.", "byContinuingYouAgree": "Ao continuar, concordas com a nossa <privacyPolicy>Política de Privacidade</privacyPolicy> e os nossos <termsOfService>Termos de Serviço</termsOfService>"}, "onboarding": {"welcome": "<PERSON><PERSON>-vindo", "next": "Próximo", "skip": "<PERSON><PERSON>"}, "tools": {"executeCommand": "Executando comando", "checkCommandOutput": "Verificando saída do comando", "terminateCommand": "Encer<PERSON>o comando", "listCommands": "Listando comandos", "createFile": "<PERSON><PERSON><PERSON>", "deleteFile": "Excluindo arquivo", "fullFileRewrite": "Reescrevendo arquivo", "strReplace": "Editando texto", "editFile": "Editando arquivo", "uploadFile": "Enviando arquivo", "createDocument": "<PERSON><PERSON><PERSON> documento", "updateDocument": "Atualizando documento", "readDocument": "<PERSON>do documento", "listDocuments": "Listando documentos", "deleteDocument": "Excluindo documento", "createTasks": "<PERSON><PERSON><PERSON>", "updateTasks": "Atualizando tare<PERSON>s", "browserNavigateTo": "Navegando para página", "browserAct": "Executando ação", "browserExtractContent": "<PERSON><PERSON><PERSON>", "browserScreenshot": "Tirando captura de tela", "executeDataProviderCall": "<PERSON><PERSON><PERSON> de dados", "getDataProviderEndpoints": "Obtendo endpoints", "ask": "Pergun<PERSON>", "wait": "Aguardar", "complete": "Completando tare<PERSON>", "crawlWebpage": "Rastreando site", "exposePort": "Expondo porta", "scrapeWebpage": "<PERSON><PERSON><PERSON>", "webSearch": "Buscando na web", "loadImage": "Carregando imagem", "createPresentationOutline": "<PERSON><PERSON>do <PERSON> de apresentação", "createPresentation": "<PERSON><PERSON><PERSON>", "presentPresentation": "Apresentando", "clearImagesFromContext": "Limpando imagens do contexto", "imageSearch": "Buscando imagem", "createSheet": "<PERSON><PERSON><PERSON>", "updateSheet": "Atualizando planilha", "viewSheet": "Visualizando planilha", "analyzeSheet": "<PERSON><PERSON><PERSON><PERSON>", "visualizeSheet": "Visualizando planilha", "formatSheet": "Formatando planilha", "updateAgent": "Atualizando agente", "getCurrentAgentConfig": "Obtendo configuração do agente", "searchMcpServers": "Buscando servidores MCP", "getMcpServerTools": "Obtendo ferramentas do servidor MCP", "configureMcpServer": "Configurand<PERSON> servidor MC<PERSON>", "getPopularMcpServers": "Obtendo servidores MCP populares", "testMcpServerConnection": "Testando conexão do servidor MCP", "listAppEventTriggers": "Encontrando triggers de evento", "createEventTrigger": "<PERSON><PERSON><PERSON> trigger de evento", "getProjectStructure": "Obtendo estrutura do projeto", "buildProject": "Construindo projeto", "makePhoneCall": "Fazendo chamada", "endCall": "<PERSON><PERSON><PERSON><PERSON>", "getCallDetails": "Obtend<PERSON> de<PERSON><PERSON> da chamada", "listCalls": "<PERSON><PERSON><PERSON>", "monitorCall": "<PERSON><PERSON><PERSON>", "waitForCallCompletion": "<PERSON><PERSON><PERSON><PERSON> conclus<PERSON>", "getPaperDetails": "Obtendo detalhes do artigo", "searchAuthors": "Buscando autores", "getAuthorDetails": "Obtendo detalhes do autor", "getAuthorPapers": "Obtendo artigos do autor", "getPaperCitations": "Obtendo citações do artigo", "getPaperReferences": "Obtendo referências do artigo", "paperSearch": "<PERSON><PERSON><PERSON> artigo<PERSON>", "discoverUserMcpServers": "Descobrindo ferramentas", "createCredentialProfile": "<PERSON><PERSON><PERSON> per<PERSON>", "getCredentialProfiles": "Obtendo perfis", "configureProfileForAgent": "Adicionando ferramentas ao agente", "createNewAgent": "Criando novo agente", "searchMcpServersForAgent": "Buscando servidores MCP", "createCredentialProfileForAgent": "<PERSON><PERSON><PERSON> per<PERSON> de credencia<PERSON>", "discoverMcpToolsForAgent": "Descobrindo ferramentas MCP", "configureAgentIntegration": "Configurando integração do agente", "createAgentScheduledTrigger": "<PERSON><PERSON><PERSON> trigger agendado", "listAgentScheduledTriggers": "Listando triggers agendados do agente", "executingTool": "Executando ferramenta", "toolExecutedSuccessfully": "Ferramenta executada com sucesso", "toolExecutionFailed": "Falha na execução da ferramenta", "input": "Entrada", "output": "<PERSON><PERSON><PERSON>", "copyFileContent": "Copiar conteúdo do arquivo", "fileContentCopied": "Conteúdo do arquivo copiado para área de transferência", "failedToCopyFileContent": "Falha ao copiar conteúdo do arquivo", "noContentAvailable": "Nenhum conteúdo disponível", "noContentDescription": "Esta execução de ferramenta não produziu nenhum conteúdo de entrada ou saída para exibir.", "tool": "Ferramenta"}, "agentRunLimit": {"gotIt": "<PERSON><PERSON><PERSON>", "parallelRunsLimitReached": "Limite de Execuções Paralelas Atingido", "parallelRunsLimitDescription": "Atingiste o máximo de execuções paralelas de agentes permitidas.", "needMoreParallelRuns": "Precisas de mais execuções paralelas?", "upgradeDescription": "Melhora teu plano para executar múltiplos agentes simultaneamente e aumentar tua produtividade.", "upgradePlan": "<PERSON><PERSON><PERSON>", "currentlyRunningAgents": "Agentes Atualmente Executando", "loadingThreads": "Carregando threads...", "foundRunningAgents": "Encontrados {count} agentes executando mas incapaz de carregar detalhes da thread.", "threadIds": "IDs de Thread: {ids}", "moreRunning": "+{count} executando", "whatCanYouDo": "O que podes fazer?", "stopAgentsHint": "Clica no botão parar para parar agentes executando", "waitForCompletion": "Espera um agente completar automaticamente", "upgradeYourPlan": "Melhora teu plano", "forMoreParallelRuns": "para mais execuções paralelas", "stopAgent": "Parar agente", "stopThisAgent": "Parar este agente", "openThread": "Abrir thread", "agentStoppedSuccessfully": "Agente parado com sucesso", "failedToStopAgent": "Falha ao parar agente", "upgradeToRunMore": "Melhora para executar mais agentes em paralelo"}, "errors": {"generic": "Ocorreu um erro", "network": "Erro de rede", "unauthorized": "Não autorizado", "notFound": "Não encontrado"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano", "zh": "中文", "ja": "日本語", "pt": "Português", "fr": "Français", "es": "Español"}, "suna": {"samplePrompts": "Prompts de exemplo", "chooseStyle": "Escolhe um estilo", "chooseTemplate": "Escolhe um <PERSON>", "chooseOutputFormat": "Escolhe formato de saída", "preferredCharts": "Gráficos preferidos", "whatWouldYouLike": "O que tu queres fazer?", "describeTask": "Descreve a tarefa que queres que teu Worker complete...", "modes": {"image": "Imagem", "slides": "Slides", "data": "<PERSON><PERSON>", "docs": "Documentos", "people": "<PERSON><PERSON><PERSON><PERSON>", "research": "Pesquisa"}, "styles": {"photorealistic": "Fotorealista", "watercolor": "Aquarela", "digital-art": "Arte Digital", "oil-painting": "Pintura a Óleo", "minimalist": "Minimalista", "isometric": "Isométrico", "vintage": "Vintage", "comic": "Q<PERSON>rin<PERSON>", "neon": "Neon", "pastel": "Pastel", "geometric": "Geométrico", "abstract": "Abstrato", "anime": "Anime", "impressionist": "Impressionista", "surreal": "Surrealista"}, "templates": {"minimalist": {"name": "Minimalista", "description": "Design limpo e simples"}, "minimalist_2": {"name": "Minimalista 2", "description": "Estilo minimal alternativo"}, "black_and_white_clean": {"name": "Preto e Branco", "description": "Monocromático clássico"}, "colorful": {"name": "Colorido", "description": "Vibrante e energético"}, "startup": {"name": "Startup", "description": "Dinâmico e inovador"}, "elevator_pitch": {"name": "Elevator Pitch", "description": "Rápido e impactante"}, "portfolio": {"name": "Portfólio", "description": "<PERSON>ra teu trabalho"}, "textbook": {"name": "<PERSON><PERSON>", "description": "Educacional e estruturado"}, "architect": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Profissional e preciso"}, "hipster": {"name": "<PERSON><PERSON>", "description": "Moderno e na moda"}, "green": {"name": "Verde", "description": "Design inspirado na natureza"}, "premium_black": {"name": "Preto Premium", "description": "Tema escuro luxuoso"}, "premium_green": {"name": "Verde Premium", "description": "Verde sofisticado"}, "professor_gray": {"name": "Professor <PERSON><PERSON><PERSON>", "description": "Acadêmico e erudito"}, "gamer_gray": {"name": "<PERSON><PERSON>", "description": "Design inspirado em jogos"}, "competitor_analysis_blue": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Focado em análise de negócios"}, "numbers_clean": {"name": "Números <PERSON>", "description": "Visualização de dados limpa"}, "numbers_colorful": {"name": "Números Coloridos", "description": "Apresentação de dados vibrante"}, "prd": {"name": "PRD", "description": "Documento de requisitos do produto"}, "technical": {"name": "Técnico", "description": "Documentação técnica"}, "proposal": {"name": "Proposta", "description": "Proposta comercial"}, "report": {"name": "Relat<PERSON><PERSON>", "description": "Formato de relatório detalhado"}, "guide": {"name": "<PERSON><PERSON><PERSON>", "description": "Guia passo a passo"}, "wiki": {"name": "Wiki", "description": "Artigo da base de conhecimento"}, "policy": {"name": "Política", "description": "Documento de política"}, "meeting-notes": {"name": "Atas da Reunião", "description": "Atas da reunião"}}, "outputFormats": {"spreadsheet": {"name": "<PERSON><PERSON><PERSON>", "description": "Tabela com fórmulas"}, "dashboard": {"name": "<PERSON><PERSON>", "description": "Gráficos interativos"}, "report": {"name": "Relat<PERSON><PERSON>", "description": "Análise com visuais"}, "slides": {"name": "Slides", "description": "Formato de apresentação"}}, "charts": {"bar": "Barras", "line": "<PERSON><PERSON>", "pie": "Pizza", "scatter": "Di<PERSON>são", "heatmap": "Mapa de calor", "bubble": "Bolhas", "wordcloud": "Nuvem de palavras", "stacked": "Barras empilhadas", "area": "Á<PERSON>"}, "prompts": {"image": {"0": "Uma majestosa águia dourada voando através de picos de montanhas nebulosas ao nascer do sol com iluminação dramática", "1": "Retrato close-up de uma modelo de moda com maquiagem avant-garde, iluminação de estúdio, sombras de alto contraste", "2": "Sala de estar escandinava aconchegante com móveis de madeira natural, plantas de interior e suave luz matinal", "3": "Mercado de rua futurista cyberpunk à noite com sinais neon, calçada molhada pela chuva e displays holográficos", "4": "Fotografia elegante de produto de frasco de perfume de luxo em superfície de mármore com reflexos suaves", "5": "Ilhas flutuantes caprichosas conectadas por pontes de corda em um céu pastel com nuvens sonhadoras", "6": "Close-up macro de gotas de orvalho matinal em pétalas de flores vibrantes com fundo bokeh", "7": "Configuração moderna de mesa de espaço de trabalho com laptop, café, caderno e plantas suculentas de cima", "8": "Caminho místico na floresta com árvores antigas, vaga-lumes brilhantes e feixes de luz etéreos através da névoa", "9": "Detalhe arquitetônico de fachada de edifício de vidro contemporâneo com padrões geométricos e reflexos", "10": "Barraca vibrante de vendedor de comida de rua com ingredientes coloridos, vapor subindo e iluminação quente", "11": "<PERSON><PERSON><PERSON> zen japonês sereno com areia penteada, pedras cobertas de musgo e pétalas de cerejeira", "12": "Foto de ação dinâmica de atleta no meio do salto contra céu de pôr do sol dramático, efeito silhueta", "13": "Cozinha rústica de fazenda com panelas de cobre, ervas frescas, tábuas de corte de madeira e texturas naturais", "14": "Arte fluida abstrata com padrões orgânicos de ouro metálico giratório, azul profundo e verde esmeralda"}, "slides": {"0": "Criar um pitch deck da Série A com tamanho de mercado, tração e projeções financeiras", "1": "Construir uma revisão de negócios do Q4 mostrando KPIs, vitórias e iniciativas estratégicas", "2": "Projetar uma apresentação de lançamento de produto com vídeos de demonstração e depoimentos de clientes", "3": "Desenvolver um deck de capacitação de vendas explicando nossa proposta de valor e vantagens competitivas", "4": "Criar uma atualização para investidores destacando métricas-chave e marcos futuros", "5": "Construir uma apresentação de estudo de caso do cliente mostrando ROI e métricas de sucesso", "6": "Projetar uma apresentação all-hands cobrindo atualizações da empresa e visão", "7": "Desenvolver um deck de treinamento para novos recursos e fluxos de trabalho do produto", "8": "Criar uma palestra de conferência sobre escalar equipes de engenharia", "9": "Construir uma apresentação de reunião de diretoria com recomendações estratégicas"}, "data": {"0": "Construir um modelo financeiro projetando crescimento de ARR com diferentes cenários de preços", "1": "Criar um painel de vendas interativo rastreando métricas por região e trimestre", "2": "Analisar 50 mil avaliações de clientes e visualizar tendências de sentimento ao longo do tempo", "3": "Projetar um calendário de conteúdo rastreando campanhas com gráficos de ROI e engajamento", "4": "Construir uma análise de coorte mostrando padrões de retenção e churn de usuários", "5": "Criar um modelo de atribuição de marketing comparando desempenho de canais", "6": "Desenvolver um rastreador de contratação com métricas de pipeline e análise de tempo para preencher", "7": "Construir uma planilha de planejamento de orçamento com modelagem de cenários", "8": "Analisar dados de tráfego do site e visualizar funis de conversão", "9": "Criar um sistema de gestão de inventário com alertas automáticos de reabastecimento"}, "docs": {"0": "Escrever um PRD abrangente para um motor de recomendação alimentado por IA", "1": "Rascunhar um documento de arquitetura técnica para uma plataforma de microsserviços escalável", "2": "Criar um documento de estratégia go-to-market para nosso lançamento de produto do Q2", "3": "Desenvolver um playbook de onboarding de 90 dias para gerentes de engenharia", "4": "Escrever um guia de documentação de API com exemplos e melhores práticas", "5": "Criar um manual da empresa cobrindo cultura, políticas e benefícios", "6": "Rascunhar uma política de privacidade de dados compatível com GDPR e CCPA", "7": "Desenvolver um playbook de sucesso do cliente para contas empresariais SaaS", "8": "Escrever um plano de resposta a incidentes de segurança com procedimentos de escalação", "9": "Criar um guia de estilo abrangente para marca e conteúdo"}, "people": {"0": "Encontrar candidatos a VP de Engenharia em startups de IA/ML da Série B+ na área da baía de San Francisco com 10+ anos de experiência e histórico comprovado de escalar equipes de engenharia", "1": "Construir lista de leads de CMOs em empresas B2B SaaS ($10M-$50M ARR) que recentemente levantaram financiamento da Série A/B - incluir padrões de email e stack tecnológico", "2": "Pesquisar Engenheiros Sênior de Blockchain com experiência em Solidity/Rust em projetos cripto de topo, abertos a realocação para Dubai ou Singapura", "3": "Gerar lista de prospects de fundadores técnicos em startups Seed-Série A em IA Empresarial que levantaram $2M-$15M nos últimos 6 meses", "4": "Identificar Gerentes de Produto Sênior em empresas fintech com 5-10 anos de experiência da FAANG ou unicórnios, habilidosos em desenvolvimento de produto 0-1", "5": "Encontrar CIOs e VP de Engenharia em empresas de TI de saúde de médio mercado (500-5000 funcionários) com orçamentos de TI de $500K+ planejando migração para nuvem", "6": "Pesquisar VP de Vendas em empresas B2B SaaS mostrando crescimento de 100%+ YoY, com 7+ anos fechando negócios de $100K+ e experiência PLG", "7": "Construir lista de CTOs em empresas empresariais implementando ativamente infraestrutura de IA com orçamentos multimilionários em 2024", "8": "Encontrar Designers Sênior UX/UI com experiência em app de consumidor mobile-first e portfólios de 1M+ usuários, procurando ativamente ou abertos a oportunidades", "9": "Identificar Engenheiros DevOps Sênior em startups cloud-native com expertise em Kubernetes/Terraform e 5-8 anos construindo infraestrutura para 10M+ usuários"}, "research": {"0": "Analisar tendências emergentes em computação quântica e aplicações comerciais potenciais", "1": "Pesquisar os 10 principais concorrentes no espaço de CRM alimentado por IA com comparação de recursos", "2": "Investigar requisitos regulatórios para lançar um app fintech na UE", "3": "Compilar análise de mercado sobre taxas de adoção de veículos elétricos em mercados principais", "4": "Estudar o impacto do trabalho remoto na demanda de imóveis comerciais em cidades principais", "5": "Pesquisar padrões de adoção Web3 entre empresas Fortune 500", "6": "Analisar sentimento do consumidor em relação a marcas de moda sustentável", "7": "Investigar os últimos desenvolvimentos em terapia genética para doenças raras", "8": "Estudar estratégias de preços de empresas bem-sucedidas de caixas de assinatura D2C", "9": "Pesquisar o panorama competitivo de soluções de cibersegurança alimentadas por IA"}}}}