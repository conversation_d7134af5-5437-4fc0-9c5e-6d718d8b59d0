{"common": {"cancel": "Annuler", "save": "Enregistrer", "saving": "Enregistrement...", "success": "Su<PERSON>ès", "done": "OK", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "O<PERSON>", "no": "Non", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "search": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "send": "Envoyer", "back": "Retour", "new": "Nouveau", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "remove": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "processing": "Traitement...", "keep": "<PERSON><PERSON><PERSON>", "active": "Actif", "inactive": "Inactif", "next": "Suivant", "upgrade": "<PERSON><PERSON><PERSON><PERSON>", "basic": "Basique", "tryFree": "Commencer"}, "settings": {"title": "Paramètres", "tabs": {"general": "Général", "plan": "Plan", "billing": "Facturation", "usage": "Utilisation", "knowledgeBase": "Base de Connaissances", "integrations": "Intégrations", "envManager": "Gestionnaire d'Environnement"}, "general": {"title": "Paramètres du Profil", "description": "Gère les infos de ton compte", "name": "Nom", "email": "Email", "namePlaceholder": "Entre ton nom", "emailCannotChange": "L'email ne peut pas être changé d'ici", "saveChanges": "<PERSON><PERSON><PERSON><PERSON>", "profileUpdated": "Profil mis à jour avec succès", "profileUpdateFailed": "Échec de la mise à jour du profil", "language": {"title": "<PERSON><PERSON>", "description": "Choisis ta langue préférée", "current": "<PERSON>ue Actuelle", "select": "Sélectionner la Langue"}, "deleteAccount": {"title": "<PERSON><PERSON><PERSON><PERSON> le Compte", "description": "Supprime définitivement ton compte et toutes les données associées", "button": "<PERSON><PERSON><PERSON><PERSON> le Compte", "scheduled": "Suppression Programmée", "scheduledDescription": "Ton compte sera définitivement supprimé le {date}.", "canCancel": "Tu peux annuler cette demande à tout moment avant la date de suppression.", "cancelButton": "Annuler la Demande de Suppression", "dialogTitle": "<PERSON><PERSON><PERSON><PERSON> le Compte", "warning": "Cette action ne peut pas être annulée après 30 jours", "whenDelete": "Quand tu supprimes ton compte :", "agentsDeleted": "Tous tes agents et versions d'agents seront supprimés", "threadsDeleted": "Tous tes fils et conversations seront supprimés", "credentialsRemoved": "Toutes tes identifiants et intégrations seront supprimées", "subscriptionCancelled": "Ton abonnement sera annulé", "billingRemoved": "Toutes les données de facturation seront supprimées", "scheduled30Days": "Ton compte sera programmé pour suppression dans 30 jours", "gracePeriod": "Tu peux annuler cette demande à tout moment pendant la période de grâce de 30 jours. Après 30 jours, toutes tes données seront définitivement supprimées et ne pourront pas être récupérées.", "confirmText": "Tape supprimer pour confirmer", "confirmPlaceholder": "delete", "keepAccount": "<PERSON><PERSON><PERSON> le Compte", "cancelDeletionTitle": "Annuler la Suppression de Compte", "cancelDeletionDescription": "Veux-tu vraiment annuler la suppression de ton compte ? Ton compte et toutes les données seront préservés.", "cancelDeletion": "Annuler la Suppression"}}, "billing": {"title": "Facturation et Abonnement", "description": "<PERSON><PERSON><PERSON> vos crédits et votre abonnement", "insufficientCredits": "Crédits insuffisants", "insufficientCreditsMessage": "Vérification de facturation échouée : Crédits insuffisants. Votre solde est {balance}. Veuillez ajouter des crédits pour continuer.", "creditsExhausted": "Vous avez épuisé vos crédits. Mettez à niveau maintenant.", "billingCheckFailed": "Vérification de facturation échouée. Veuillez mettre à niveau pour continuer.", "failedToStartAgent": "Échec du démarrage de l'agent {agentName} : {message}"}, "usage": {"title": "Utilisation"}, "knowledgeBase": {"opening": "Ouverture de la Base de Connaissances", "redirecting": "Redirection vers la page Base de Connaissances..."}, "integrations": "Intégrations"}, "triggers": {"getStarted": "Commence en ajoutant un trigger", "getStartedDescription": "Programme un trigger pour automatiser les actions et recevoir des rappels quand elles se terminent.", "search": "<PERSON><PERSON><PERSON>", "scheduledTrigger": "<PERSON><PERSON>", "eventBasedTrigger": "<PERSON>gger <PERSON> sur Événement", "chooseAgent": "Choisir un Agent", "schedule": "Programmer", "agentInstructions": "Instructions de l'Agent", "agentInstructionsDescription": "Prompt personnalisé pour l'agent", "assignedAgent": "Agent <PERSON><PERSON><PERSON>", "unknownAgent": "Agent <PERSON><PERSON><PERSON>", "technicalDetails": "Détails Techniques", "type": "Type", "provider": "Fournisseur", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Dernière Mise à Jour", "enable": "Activer", "disable": "Désactiver", "deleteTask": "Supp<PERSON>er la Tâche", "deleteTaskDescription": "Es-tu sûr de vouloir supprimer \"{name}\" ? Cette action ne peut pas être annulée et arrêtera toutes les exécutions automatiques de cette tâche.", "taskEnabled": "Tâche activée", "taskDisabled": "Tâche désactivée", "toggleTaskFailed": "Échec du basculement de la tâche", "taskDeletedSuccess": "Tâche supprimée avec succès", "deleteTaskFailed": "Échec de la suppression de la tâche", "taskUpdatedSuccess": "Tâche mise à jour avec succès", "updateTaskFailed": "Échec de la mise à jour de la tâche", "pleaseSelectAgent": "<PERSON><PERSON><PERSON>lectionner un agent", "defaultScheduledTriggerName": "<PERSON><PERSON>", "defaultScheduledTriggerDescription": "Trigger programmée automatiquement", "scheduleTriggerUpdated": "Trigger programmée mise à jour avec succès", "scheduleTriggerCreated": "Trigger program<PERSON>e créée avec succès", "upgradeForMoreScheduledTriggers": "Améliore pour créer plus de triggers programmées", "upgradeForMoreAppTriggers": "Améliore pour créer plus de triggers basés sur app", "updateScheduleTriggerFailed": "Échec de la mise à jour du trigger programmée", "createScheduleTriggerFailed": "Échec de la création du trigger programmée", "editScheduledTask": "Modifier la Tâche Programmée", "createScheduledTask": "<PERSON><PERSON><PERSON> une Tâche Programmée", "editAppBasedTask": "Modifier la Tâche Basée sur App", "createAppBasedTask": "<PERSON><PERSON><PERSON> une Tâche Basée sur App", "updateAgentForTask": "Mettre à jour l'agent pour cette tâche", "selectAgentForTask": "<PERSON><PERSON>abord, choisis quel agent doit gérer cette tâche", "createNew": "Créer nouveau", "notConfigured": "Non configuré", "every15Minutes": "Toutes les 15 minutes", "every30Minutes": "Toutes les 30 minutes", "everyHour": "Toutes les heures", "dailyAt9AM": "Quotidiennement à 9h", "weekdaysAt9AM": "Jours ouvrables à 9h", "weeklyOnMonday": "Hebdomadaire le lundi", "monthlyOn1st": "Mensuel le 1er"}, "sidebar": {"chats": "Chats", "agents": "Agents", "workers": "Workers", "triggers": "Triggers", "newChat": "Nouveau Chat", "newAgent": "Nouvel Agent", "settings": "Paramètres", "logout": "Déconnexion", "openMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "expandSidebar": "Développer la barre latérale (CMD+B)", "searchChats": "Rechercher chats", "searchChatsPlaceholder": "Rechercher chats...", "allTriggers": "<PERSON><PERSON> les Triggers", "addWorkers": "Ajouter Workers", "addTrigger": "<PERSON><PERSON><PERSON>", "triggerConfig": "Config <PERSON>", "scheduledTrigger": "<PERSON><PERSON>", "scheduledTriggerDescription": "Programmer un trigger à une heure précise", "eventBasedTrigger": "<PERSON>gger <PERSON> sur Événement", "eventBasedTriggerDescription": "<PERSON><PERSON>er un trigger qui s'exécute lors d'un événement", "noChatsFound": "Aucun chat trouvé", "noConversations": "Pas encore de chats", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher...", "searchResults": "Résultats de Recherche", "recent": "<PERSON><PERSON><PERSON>", "personalAccount": "Compte Personnel", "workspaces": "Espaces de Travail", "theme": "Thème", "integrations": "Intégrations", "billing": "Facturation", "usage": "Utilisation", "adminPanel": "<PERSON><PERSON><PERSON>min", "apiKeys": "Clés API", "envManager": "Gestionnaire d'Environnement", "advanced": "<PERSON><PERSON><PERSON>", "plan": "Plan", "knowledgeBase": "Base de Connaissances", "conversation": "Conversation", "conversations": "Conversations", "startNewChat": "Commencer un nouveau chat", "openInNewTab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression", "allThreadsLoaded": "Tous les fils chargés", "myWorkforce": "<PERSON>-d'Œuvre", "addTeam": "Ajouter une Équipe", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "selectAllConversations": "Sé<PERSON><PERSON><PERSON> toutes les conversations", "deselectAllConversations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toutes les conversations", "loadMore": "Charger plus", "remaining": "restants"}, "dashboard": {"title": "Tableau de bord", "welcome": "Bienvenue", "whatWouldYouLike": "Qu'est-ce que tu veux faire ?", "describeWhatYouNeed": "<PERSON><PERSON><PERSON><PERSON> ce dont tu as besoin d'aide...", "usageLimits": "Limites d'Utilisation", "threads": "Fils", "customWorkers": "Workers Personnalisés", "scheduledTriggers": "Trigger<PERSON> Programmées", "appTriggers": "Triggers d'App", "limitsExceeded": "Tu as atteint ta limite de chats ({current}/{limit}). Améliore ton plan pour créer plus de chats.", "agentInitiatedSuccessfully": "Agent <PERSON><PERSON><PERSON><PERSON> avec succès", "aiAssistantReady": "Ton assistant I<PERSON> est prêt à aider"}, "thread": {"integrations": "Intégrations", "connect": "Connecter", "seeAllIntegrations": "+ Voir toutes les intégrations", "connectIntegrations": "Connecter les intégrations", "adaptive": "<PERSON><PERSON><PERSON><PERSON>", "adaptiveDescription": "Réponses rapides avec changement de contexte intelligent", "autonomous": "Autonome", "autonomousDescription": "Mode travail approfondi pour résolution de problèmes en plusieurs étapes", "chat": "Cha<PERSON>", "chatDescription": "Conversation simple aller-retour", "sampleAnswers": "Exemples de réponses", "samplePrompts": "Exemples de prompts", "waitingForUserResponse": "Kortix continuera à travailler de manière autonome après ta réponse.", "taskCompleted": "Tâche terminée", "uploadingFiles": "Téléchargement de {count} fichier{plural}...", "uploadingFilesOne": "Téléchargement de {count} fichier...", "uploadingFilesMany": "Téléchargement de {count} fichiers...", "scrollToBottom": "Faire défiler vers le bas", "tools": "Outils", "instructions": "Instructions", "knowledge": "Connaissances", "triggers": "Triggers", "agents": "Agents", "superWorker": "Super Worker", "searchWorkers": "Rechercher workers...", "myWorkers": "Mes Workers", "running": "En cours", "minimizeToFloatingPreview": "Réduire en aperçu flottant", "close": "<PERSON><PERSON><PERSON>", "noActionsYet": "Pas encore d'actions", "workerActionsDescription": "Les actions et résultats du Worker apparaîtront ici lors de l'exécution", "toolIsRunning": "L'outil est en cours d'exécution", "toolCurrentlyExecuting": "{toolName} est en cours d'exécution. Les résultats apparaîtront ici une fois terminé.", "rateThisResult": "Évaluer ce résultat", "feedbackHelpsImprove": "Vos commentaires nous aident à nous améliorer", "additionalFeedbackOptional": "Commentaire supplémentaire (optionnel)", "helpKortixImprove": "Aidez Kortix à s'améliorer avec vos commentaires", "submit": "Envoyer", "submitting": "Envoi en cours...", "feedbackSubmittedSuccess": "Commentaire envoyé avec succès", "feedbackSubmitFailed": "Échec de l'envoi du commentaire"}, "agents": {"title": "Agents", "create": "<PERSON><PERSON><PERSON> un Agent", "createNewWorker": "C<PERSON>er un Nouveau Worker", "edit": "Modifier l'Agent", "delete": "Supprimer l'Agent", "name": "Nom", "description": "Description", "model": "<PERSON><PERSON><PERSON><PERSON>", "systemPrompt": "Prompt Système", "systemPromptPlaceholder": "Définis comment ton agent doit se comporter...", "customizeIcon": "Clique pour personnaliser l'icône de l'agent"}, "home": {"heroPlaceholder": "Décris la tâche que tu veux que ton Worker accomplisse..."}, "showcase": {"title": "Kortix : Votre Worker IA Autonome", "subtitle": "Conçu pour les tâches complexes, pensé pour tout. L'assistant <PERSON><PERSON> ultime qui gère tout—des demandes simples aux projets méga-complexes.", "tryItOut": "Essayer", "kortixComputer": "Kortix Computer", "running": "En cours d'exécution", "workers": {"images": {"title": "Images", "description": "Crée des images à la demande. Des photos de produits aux graphiques pour réseaux sociaux en passant par des illustrations complètes. Ajuste le style, l'éclairage, les couleurs et la mise en page, ou affine les visuels existants avec des modifications rapides et des retouches.", "capabilities": {"0": "Générer des photos de produits", "1": "Créer des graphiques pour réseaux sociaux", "2": "<PERSON><PERSON><PERSON> des illustrations", "3": "Variations de style et d'éclairage", "4": "Création de logos / ressources", "5": "+ Beaucoup plus"}, "imageAlt": "Exemple graphique La croissance n'est pas linéaire", "fileType": "image"}, "slides": {"title": "Présentations", "description": "Crée des présentations époustouflantes instantanément. Des pitch decks aux rapports en passant par les matériaux de formation. Ajuste les thèmes, les mises en page, la structure du contenu ou affine les présentations existantes avec des modifications et mises à jour rapides.", "capabilities": {"0": "Pitch decks", "1": "Matériel de formation", "2": "Présentations de rapports", "3": "Variations de thème et de mise en page", "4": "Restructuration du contenu", "5": "+ Beaucoup plus"}, "imageAlt": "Exemple de diapositive Nexus Enterprise Automation Platform", "fileType": "PPTX"}, "data": {"title": "<PERSON><PERSON><PERSON>", "description": "Transforme les données brutes en insights. Des feuilles de calcul aux tableaux de bord en passant par les visualisations. Nettoie les ensembles de données, crée des graphiques, génère des rapports ou affine les analyses existantes avec des mises à jour rapides.", "capabilities": {"0": "Tableaux de bord", "1": "Visualisations", "2": "Rapports de données", "3": "Nettoyer et organiser les données", "4": "Générer des insights", "5": "+ Beaucoup plus"}, "imageAlt": "Exemple de tableau de bord de modèle financier", "fileType": "<PERSON><PERSON><PERSON><PERSON>"}, "docs": {"title": "Documents", "description": "Écrit et édite des documents sans effort. Des propositions aux guides en passant par les contenus. Ajuste le ton, la structure, le formatage ou affine les documents existants avec des réécritures rapides et du polissage.", "capabilities": {"0": "Propositions", "1": "Guides et manuels", "2": "<PERSON>tenus", "3": "Variations de ton et de style", "4": "Formater et restructurer", "5": "+ Beaucoup plus"}, "imageAlt": "Exemple de rapport exécutif récapitulatif Q3 2025", "fileType": "PDF"}, "research": {"title": "Recherche", "description": "Recherche des sujets de manière approfondie. Des tendances du marché à l'analyse concurrentielle en passant par les études approfondies. Rassemble des sources, synthétise les résultats ou affine les recherches existantes avec des mises à jour rapides.", "capabilities": {"0": "Analyser les tendances du marché", "1": "Recherche concurrentielle", "2": "Études approfondies de sujets", "3": "Rassembler des sources", "4": "Synthétiser les résultats", "5": "+ Beaucoup plus"}, "imageAlt": "Exemple de recherche Profils détaillés des concurrents", "fileType": "PDF"}}}, "threads": {"title": "Fils", "newThread": "Nouveau Fil", "sendMessage": "Envoyer un message", "placeholder": "Tape ton message..."}, "billing": {"title": "Facturation & Abonnement", "subscription": "Abonnement", "credits": "Crédits", "popular": "Populaire", "creditsExplained": "Crédit<PERSON> expliqués", "creditsExhaustedRefreshIn": "Vous avez épuisé vos crédits. Ils seront renouvelés dans {time}", "trialActive": "Essai Actif", "trialBadge": "Essai de 7 Jours", "currentPlan": "Plan Actuel", "currentBadge": "Actuel", "scheduled": "<PERSON><PERSON><PERSON>", "scheduledBadge": "<PERSON><PERSON><PERSON>", "loading": "Chargement des informations de facturation...", "subscriptionChangeScheduled": "Changement d'abonnement programmé", "planWillChangeOn": "Ton plan changera le {date}.", "failedToInitiateSubscription": "Échec du démarrage de l'abonnement. Réessaie.", "subscriptionUpgraded": "Abonnement amélioré de {currentPrice} à {newPrice}", "subscriptionUpdated": "Abonnement mis à jour avec succès", "cannotDowngradeDuringCommitment": "Impossible de rétrograder pendant la période d'engagement", "alreadyOnThisPlan": "Tu es déjà sur ce plan.", "localModeMessage": "Exécution en mode développement local - fonctionnalités de facturation désactivées", "creditsExplainedPage": {"title": "Cré<PERSON><PERSON> Expliqué<PERSON>", "subtitle": "Tout ce que tu dois savoir sur le fonctionnement des crédits sur Kortix", "understandingCredits": {"title": "Comprendre les Crédits", "description": "Les crédits servent de monnaie universelle de Kortix pour les opérations de la plateforme. Chaque action que tes agents IA effectuent—de l'analyse de données à la génération de code—consomme des crédits en fonction de la complexité de la tâche et des ressources nécessaires."}, "howCreditsWork": {"title": "Comment Fonctionnent les Crédits", "description": "Les crédits sont consommés en fonction des ressources que tes agents IA utilisent :", "aiModelUsage": {"title": "Utilisation du Modèle IA", "description": "Le principal facteur de consommation de crédits", "content": "Différents modèles IA ont des coûts différents selon leurs capacités et l'utilisation des tokens. Les crédits sont consommés pour les tokens d'entrée (tes prompts et contexte), les tokens de sortie (réponses de l'agent) et varient selon le niveau du modèle (Claude, GPT, etc.)."}, "pricingModel": {"title": "Modèle de Tarification", "description": "Marge de 20% sur les coûts du modèle IA", "content": "On applique une marge de 20% sur tous les coûts d'API et de modèle pour couvrir l'infrastructure de la plateforme, la sécurité et le développement continu. Ce tarif transparent garantit que tu sais exactement ce pour quoi tu paies."}}, "gettingMoreCredits": {"title": "Obtenir Plus de Crédits", "description": "Il y a plusieurs façons d'obtenir des crédits sur Kortix :", "monthlySubscription": {"title": "Crédits d'Abonnement Mensuel", "description": "Inclus dans ton plan payant et renouvelés automatiquement chaque mois. Ce sont des crédits avec expiration."}, "topUpCredits": {"title": "Cré<PERSON>s de Recharge", "description": "Achète des crédits supplémentaires quand tu en as besoin. Ce sont des crédits sans expiration disponibles aux membres premium."}, "promotionalGrants": {"title": "Dons Promotionnels et d'Événements", "description": "Crédits bonus provenant d'événements spéciaux, promotions ou parrainages. Ce sont des crédits sans expiration."}, "refunds": {"title": "Remboursements", "description": "Crédits retournés en raison de problèmes techniques ou de tâches échouées. Ce sont des crédits sans expiration."}}, "typesOfCredits": {"title": "Types de Crédits", "description": "Kortix utilise deux types de crédits pour te donner de la flexibilité dans la gestion de ton utilisation :", "expiringCredits": {"title": "Crédits avec Expiration", "description": "Crédits d'abonnement mensuel", "content": "Ces crédits sont inclus dans ton abonnement payant et sont renouvelés automatiquement chaque mois à la date de ton abonnement. Ils expirent à la fin de chaque cycle de facturation et sont toujours consommés en premier avant tout crédit sans expiration."}, "nonExpiringCredits": {"title": "Crédits sans Expiration", "description": "Crédits permanents qui n'expirent jamais", "content": "Ces crédits n'expirent jamais et se reportent de mois en mois. Ils incluent les achats de recharge, remboursements et dons promotionnels. Les crédits sans expiration ne sont utilisés qu'après que tes crédits avec expiration soient épuisés."}, "creditPriority": {"title": "Priorité des Crédits :", "description": "Quand tu utilises Kortix, les crédits avec expiration sont consommés en premier. Ce n'est qu'après que tes crédits avec expiration soient épuisés que les crédits sans expiration seront utilisés."}}}, "close": "<PERSON><PERSON><PERSON>", "cancelScheduledChange": "Annuler Changement Programmée", "threadUsage": "Utilisation des Fils", "error": "<PERSON><PERSON><PERSON>", "totalUsage": "Utilisation Totale", "usage": "Utilisation", "thread": "Fil", "lastUsed": "Dernière Utilisation", "creditsUsed": "Crédits Utilisés", "creditTransactions": "Transactions de Crédits", "loadingTransactionHistory": "Chargement de ton historique de transactions...", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "discount": "15% de réduction", "perMonth": "/mois", "billedYearly": "facturé annuellement", "downgradePending": "Rétrogradation en Attente", "downgrade": "Rétrograder", "switchToLegacyYearly": "Passer à l'Annuel Legacy", "pickPlan": "Choisis le plan qui fonctionne pour toi.", "reachedLimit": "<PERSON> as atteint tes limites.", "projectLimit": "Limite de Projet ({current}/{limit})", "threadLimit": "<PERSON>ite de <PERSON> ({current}/{limit})", "workerLimit": "<PERSON>ite de <PERSON> ({current}/{limit})", "triggerLimit": "<PERSON><PERSON> ({current}/{limit})", "getAdditionalCredits": "Obtenir des Crédits Supplémentaires", "selectPlan": "Sélectionner le Plan", "changeScheduled": "Changement Programmée", "notAvailable": "Non Disponible", "features": {"creditsPerMonth": "{count} crédits/mois", "customWorker": "{count} Worker personnalisé", "customWorkers": "{count} Workers personnalisés", "privateProject": "{count} projet privé", "privateProjects": "Projets privés", "customTrigger": "{count} trigger personnalisé", "integrations": "100+ intégrations", "premiumAIModels": "Modèles IA Premium", "prioritySupport": "Support Prioritaire"}}, "auth": {"logIntoAccount": "Connecte-toi à ton compte", "createAccount": "<PERSON><PERSON><PERSON> un compte", "signInOrCreateAccount": "Connecte-toi ou crée ton compte Kortix", "continueWithGoogle": "Continuer avec Google", "continueWithGitHub": "Continuer avec GitHub", "orEmail": "ou email", "emailAddress": "<PERSON><PERSON><PERSON> email", "password": "Mot de passe", "confirmPassword": "Confirme ton mot de passe", "signIn": "Se connecter", "signUp": "S'inscrire", "signingIn": "Connexion...", "creatingAccount": "Création du compte...", "forgotPassword": "Mot de passe oublié?", "dontHaveAccount": "Pas encore de compte?", "alreadyHaveAccount": "Déjà un compte?", "acceptPrivacyTerms": "J'accepte la <privacyPolicy>Politique de Confidentialité</privacyPolicy> et les <termsOfService>Conditions d'Utilisation</termsOfService>", "privacyPolicy": "Politique de Confidentialité", "termsOfService": "Conditions d'Utilisation", "checkYourEmail": "Regarde tes emails", "confirmationLinkSent": "On t'a envoyé un lien à", "clickLinkToActivate": "Clique sur le lien dans l'email pour activer ton compte. Si tu ne vois pas l'email, vérifie ton dossier spam.", "returnToHome": "Retour à l'accueil", "backToSignIn": "Retour au login", "resetPassword": "Réinitialiser", "resetPasswordDescription": "Entre ton email et on t'enverra un lien de réinitialisation", "sendResetLink": "Envoyer le lien", "cancel": "Annuler", "pleaseEnterValidEmail": "<PERSON><PERSON><PERSON> d'entrer une adresse email valide", "signInFailed": "Connexion échouée. Vérifie tes identifiants.", "signUpFailed": "Inscription échouée. Réessaie.", "sendMagicLink": "Envoyer magic link", "sending": "Envoi en cours...", "magicLinkSent": "On t'a envoyé un magic link à", "magicLinkDescription": "Clique sur le lien dans ton email pour te connecter. Le lien expirera dans 1 heure.", "magicLinkExplanation": "On t'enverra un lien sécurisé pour te connecter. Pas besoin de mot de passe.", "byContinuingYouAgree": "En continuant, tu acceptes notre <privacyPolicy>Politique de Confidentialité</privacyPolicy> et nos <termsOfService>Conditions d'Utilisation</termsOfService>"}, "onboarding": {"welcome": "Bienvenue", "next": "Suivant", "skip": "Passer"}, "tools": {"executeCommand": "Exécution de la commande", "checkCommandOutput": "Vérification de la sortie de la commande", "terminateCommand": "<PERSON><PERSON><PERSON><PERSON>e", "listCommands": "Liste des commandes", "createFile": "Création du fichier", "deleteFile": "Suppression du fichier", "fullFileRewrite": "Réécriture du fichier", "strReplace": "Édition du texte", "editFile": "Édition du fichier", "uploadFile": "Téléchargement du fichier", "createDocument": "Création du document", "updateDocument": "Mise à jour du document", "readDocument": "Lecture du document", "listDocuments": "Liste des documents", "deleteDocument": "Suppression du document", "createTasks": "Création des tâches", "updateTasks": "Mise à jour des tâches", "browserNavigateTo": "Navigation vers la page", "browserAct": "Exécution de l'action", "browserExtractContent": "Extraction du contenu", "browserScreenshot": "Capture d'écran", "executeDataProviderCall": "Appel du fournisseur de données", "getDataProviderEndpoints": "Obtention des endpoints", "ask": "<PERSON><PERSON><PERSON>", "wait": "<PERSON><PERSON><PERSON>", "complete": "Finalisation de la tâche", "crawlWebpage": "Exploration du site web", "exposePort": "Exposition du port", "scrapeWebpage": "Scraping de la page web", "webSearch": "Recherche web", "loadImage": "Chargement de l'image", "createPresentationOutline": "Création du plan de présentation", "createPresentation": "Création de la présentation", "presentPresentation": "Présentation", "clearImagesFromContext": "Nettoyage des images du contexte", "imageSearch": "Recherche d'image", "createSheet": "Création de la feuille", "updateSheet": "Mise à jour de la feuille", "viewSheet": "Visualisation de la feuille", "analyzeSheet": "<PERSON><PERSON><PERSON> de la feuille", "visualizeSheet": "Visualisation de la feuille", "formatSheet": "Formatage de la feuille", "updateAgent": "Mise à jour de l'agent", "getCurrentAgentConfig": "Obtention de la config de l'agent", "searchMcpServers": "Recherche de serveurs MCP", "getMcpServerTools": "Obtention des outils du serveur MCP", "configureMcpServer": "Configuration du serveur MCP", "getPopularMcpServers": "Obtention des serveurs MCP populaires", "testMcpServerConnection": "Test de connexion du serveur MCP", "listAppEventTriggers": "Recherche de triggers d'événement", "createEventTrigger": "Création du trigger d'événement", "getProjectStructure": "Obtention de la structure du projet", "buildProject": "Construction du projet", "makePhoneCall": "Appel téléphonique", "endCall": "Fin de l'appel", "getCallDetails": "Obtention des détails de l'appel", "listCalls": "Liste des appels", "monitorCall": "Surveillance de l'appel", "waitForCallCompletion": "Attente de la finalisation", "getPaperDetails": "Obtention des détails de l'article", "searchAuthors": "Recherche d'auteurs", "getAuthorDetails": "Obtention des détails de l'auteur", "getAuthorPapers": "Obtention des articles de l'auteur", "getPaperCitations": "Obtention des citations de l'article", "getPaperReferences": "Obtention des références de l'article", "paperSearch": "Recherche d'articles", "discoverUserMcpServers": "Découverte d'outils", "createCredentialProfile": "Création du profil", "getCredentialProfiles": "Obtention des profils", "configureProfileForAgent": "Ajout d'outils à l'agent", "createNewAgent": "Création d'un nouvel agent", "searchMcpServersForAgent": "Recherche de serveurs MCP", "createCredentialProfileForAgent": "Création du profil d'identifiants", "discoverMcpToolsForAgent": "Découverte d'outils MCP", "configureAgentIntegration": "Configuration de l'intégration de l'agent", "createAgentScheduledTrigger": "Création du trigger programmé", "listAgentScheduledTriggers": "Liste des triggers programmés de l'agent", "executingTool": "Exécution de l'outil", "toolExecutedSuccessfully": "Outil exécuté avec succès", "toolExecutionFailed": "Échec de l'exécution de l'outil", "input": "Entrée", "output": "<PERSON><PERSON><PERSON>", "copyFileContent": "<PERSON><PERSON><PERSON> le contenu du fichier", "fileContentCopied": "Contenu du fichier copié dans le presse-papiers", "failedToCopyFileContent": "Échec de la copie du contenu du fichier", "noContentAvailable": "Aucun contenu disponible", "noContentDescription": "Cette exécution d'outil n'a produit aucun contenu d'entrée ou de sortie à afficher.", "tool": "Outil"}, "agentRunLimit": {"gotIt": "<PERSON><PERSON><PERSON>", "parallelRunsLimitReached": "Limite d'Exécutions Parallèles <PERSON>", "parallelRunsLimitDescription": "Tu as atteint le maximum d'exécutions parallèles d'agents autorisées.", "needMoreParallelRuns": "Besoin de plus d'exécutions parallèles?", "upgradeDescription": "Améliore ton plan pour exécuter plusieurs agents simultanément et booster ta productivité.", "upgradePlan": "Améliorer le Plan", "currentlyRunningAgents": "Agents Actuellement en Cours", "loadingThreads": "Chargement des fils...", "foundRunningAgents": "Trouvé {count} agents en cours mais impossible de charger les détails du fil.", "threadIds": "IDs de Fil: {ids}", "moreRunning": "+{count} en cours", "whatCanYouDo": "Que peux-tu faire?", "stopAgentsHint": "Clique sur le bouton arrêter pour arrêter les agents en cours", "waitForCompletion": "Attends qu'un agent se termine automatiquement", "upgradeYourPlan": "Améliore ton plan", "forMoreParallelRuns": "pour plus d'exécutions parallèles", "stopAgent": "<PERSON><PERSON><PERSON><PERSON> l'agent", "stopThisAgent": "Arrêter cet agent", "openThread": "<PERSON><PERSON><PERSON><PERSON><PERSON> le fil", "agentStoppedSuccessfully": "Agent <PERSON><PERSON><PERSON><PERSON> avec succès", "failedToStopAgent": "Échec de l'arrêt de l'agent", "upgradeToRunMore": "Améliore pour exécuter plus d'agents en parallèle"}, "errors": {"generic": "Une erreur s'est produite", "network": "<PERSON><PERSON><PERSON>", "unauthorized": "Non autorisé", "notFound": "Non trouvé"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano", "zh": "中文", "ja": "日本語", "pt": "Português", "fr": "Français", "es": "Español"}, "suna": {"samplePrompts": "Exemples de prompts", "chooseStyle": "Choisis un style", "chooseTemplate": "Choisis un template", "chooseOutputFormat": "Choisis le format de sortie", "preferredCharts": "Graphiques préférés", "whatWouldYouLike": "Qu'est-ce que tu veux faire ?", "describeTask": "Décris la tâche que tu veux que ton Worker accomplisse...", "modes": {"image": "Image", "slides": "Slides", "data": "<PERSON><PERSON><PERSON>", "docs": "Documents", "people": "Gens", "research": "Recherche"}, "styles": {"photorealistic": "Photo", "watercolor": "<PERSON><PERSON><PERSON><PERSON>", "digital-art": "Digital", "oil-painting": "Peinture à l'huile", "minimalist": "Minimaliste", "isometric": "Isométrique", "vintage": "Vintage", "comic": "BD", "neon": "Néon", "pastel": "Pastel", "geometric": "Géométrique", "abstract": "Abstrait", "anime": "Anime", "impressionist": "Impressionniste", "surreal": "Surréaliste"}, "templates": {"minimalist": {"name": "Minimaliste", "description": "Design propre et simple"}, "minimalist_2": {"name": "Minimaliste 2", "description": "Style minimal alternatif"}, "black_and_white_clean": {"name": "Noir et Blanc", "description": "Monochrome classique"}, "colorful": {"name": "Coloré", "description": "Vibrant et énergique"}, "startup": {"name": "Startup", "description": "Dynamique et innovant"}, "elevator_pitch": {"name": "Elevator Pitch", "description": "Rapide et impactant"}, "portfolio": {"name": "Portfolio", "description": "Montre ton travail"}, "textbook": {"name": "<PERSON>", "description": "Éducatif et structuré"}, "architect": {"name": "Architecte", "description": "Professionnel et précis"}, "hipster": {"name": "<PERSON><PERSON>", "description": "Moderne et tendance"}, "green": {"name": "<PERSON>ert", "description": "Design inspiré de la nature"}, "premium_black": {"name": "Noir Premium", "description": "Thème sombre luxueux"}, "premium_green": {"name": "Vert Premium", "description": "<PERSON><PERSON> sophist<PERSON>"}, "professor_gray": {"name": "Professeur <PERSON>", "description": "Académique et savant"}, "gamer_gray": {"name": "<PERSON><PERSON>", "description": "Design inspiré des jeux"}, "competitor_analysis_blue": {"name": "<PERSON><PERSON><PERSON>", "description": "Focalisé sur l'analyse business"}, "numbers_clean": {"name": "Nombres Propres", "description": "Visualisation de données propre"}, "numbers_colorful": {"name": "Nombres Colorés", "description": "Présentation de données vibrante"}, "prd": {"name": "PRD", "description": "Document de spécifications produit"}, "technical": {"name": "Technique", "description": "Documentation technique"}, "proposal": {"name": "Proposition", "description": "Proposition commerciale"}, "report": {"name": "Rapport", "description": "Format de rapport détaillé"}, "guide": {"name": "Guide", "description": "Guide étape par étape"}, "wiki": {"name": "Wiki", "description": "Article de base de connaissances"}, "policy": {"name": "Politique", "description": "Document de politique"}, "meeting-notes": {"name": "Procès-verbal", "description": "Procès-verbal de réunion"}}, "outputFormats": {"spreadsheet": {"name": "Tableur", "description": "Tableau avec formules"}, "dashboard": {"name": "Tableau de bord", "description": "Graphiques interactifs"}, "report": {"name": "Rapport", "description": "Analyse avec visuels"}, "slides": {"name": "Slides", "description": "Format de présentation"}}, "charts": {"bar": "<PERSON><PERSON>", "line": "<PERSON><PERSON><PERSON>", "pie": "<PERSON><PERSON><PERSON>", "scatter": "Nuage de points", "heatmap": "<PERSON><PERSON> de chaleur", "bubble": "<PERSON><PERSON>", "wordcloud": "Nuage de mots", "stacked": "<PERSON><PERSON> empilé<PERSON>", "area": "Aire"}, "prompts": {"image": {"0": "Un majestueux aigle doré planant à travers les pics de montagnes brumeuses au lever du soleil avec un éclairage dramatique", "1": "Portrait en gros plan d'un mannequin de mode avec un maquillage avant-gardiste, éclairage de studio, ombres à fort contraste", "2": "Salon scandinave confortable avec des meubles en bois naturel, des plantes d'intérieur et une douce lumière matinale", "3": "Marché de rue futuriste cyberpunk la nuit avec des enseignes au néon, trottoir glissant sous la pluie et écrans holographiques", "4": "Photographie de produit élégante d'un flacon de parfum de luxe sur une surface en marbre avec des reflets doux", "5": "Îles flottantes fantaisistes reliées par des ponts de corde dans un ciel pastel avec des nuages oniriques", "6": "Gros plan macro de gouttes de rosée matinale sur des pétales de fleurs vibrantes avec un arrière-plan bokeh", "7": "Configuration moderne de bureau d'espace de travail avec ordinateur portable, café, carnet et plantes succulentes vue de dessus", "8": "Sentier mystique dans la forêt avec d'anciens arbres, des lucioles brillantes et des faisceaux de lumière éthérés à travers le brouillard", "9": "Détail architectural de la façade d'un bâtiment en verre contemporain avec des motifs géométriques et des reflets", "10": "Étal vibrant de vendeur de nourriture de rue avec des ingrédients colorés, de la vapeur qui monte et un éclairage chaleureux", "11": "Jardin zen japonais serein avec du sable ratissé, des pierres couvertes de mousse et des pétales de cerisier", "12": "Photo d'action dynamique d'un athlète en plein saut contre un ciel de coucher de soleil dramatique, effet de silhouette", "13": "Cuisine rustique de ferme avec des casseroles en cuivre, des herbes fraîches, des planches à découper en bois et des textures naturelles", "14": "Art fluide abstrait avec des motifs organiques d'or métallique tourbillonnant, bleu profond et vert émeraude"}, "slides": {"0": "C<PERSON>er un pitch deck de Série A avec la taille du marché, la traction et les projections financières", "1": "Construire une revue d'affaires Q4 mettant en valeur les KPI, les victoires et les initiatives stratégiques", "2": "Concevoir une présentation de lancement de produit avec des vidéos de démo et des témoignages clients", "3": "Développer un deck de vente expliquant notre proposition de valeur et nos avantages concurrentiels", "4": "<PERSON><PERSON><PERSON> une mise à jour investisseur mettant en évidence les métriques clés et les jalons à venir", "5": "Construire une présentation d'étude de cas client montrant le ROI et les métriques de succès", "6": "Concevoir une présentation all-hands couvrant les mises à jour de l'entreprise et la vision", "7": "Développer un deck de formation pour les nouvelles fonctionnalités et flux de travail du produit", "8": "<PERSON><PERSON>er une conférence sur la mise à l'échelle des équipes d'ingénierie", "9": "Construire une présentation de réunion du conseil avec des recommandations stratégiques"}, "data": {"0": "Construire un modèle financier projetant la croissance de l'ARR avec différents scénarios de prix", "1": "Créer un tableau de bord de ventes interactif suivant les métriques par région et trimestre", "2": "Analyser 50 000 avis clients et visualiser les tendances de sentiment dans le temps", "3": "Concevoir un calendrier de contenu suivant les campagnes avec des graphiques de ROI et d'engagement", "4": "Construire une analyse de cohorte montrant les modèles de rétention et de désabonnement des utilisateurs", "5": "C<PERSON>er un modèle d'attribution marketing comparant les performances des canaux", "6": "Développer un suivi de recrutement avec des métriques de pipeline et une analyse du temps de remplissage", "7": "Construire une feuille de calcul de planification budgétaire avec modélisation de scénarios", "8": "Analyser les données de trafic du site web et visualiser les entonnoirs de conversion", "9": "Créer un système de gestion des stocks avec des alertes automatiques de réapprovisionnement"}, "docs": {"0": "Rédiger un PRD complet pour un moteur de recommandation alimenté par l'IA", "1": "Rédiger un document d'architecture technique pour une plateforme de microservices évolutive", "2": "Créer un document de stratégie go-to-market pour notre lancement de produit Q2", "3": "Développer un playbook d'intégration de 90 jours pour les managers d'ingénierie", "4": "Rédiger un guide de documentation API avec des exemples et des meilleures pratiques", "5": "C<PERSON>er un manuel d'entreprise couvrant la culture, les politiques et les avantages", "6": "Rédiger une politique de confidentialité des données conforme au GDPR et au CCPA", "7": "Développer un playbook de succès client pour les comptes SaaS entreprise", "8": "Rédiger un plan de réponse aux incidents de sécurité avec des procédures d'escalade", "9": "C<PERSON>er un guide de style complet pour la marque et le contenu"}, "people": {"0": "Trouver des candidats VP d'Ingénierie dans des startups IA/ML Série B+ de la région de la baie de San Francisco avec 10+ ans d'expérience et un historique prouvé de mise à l'échelle d'équipes d'ingénierie", "1": "Construire une liste de prospects de CMO dans des entreprises B2B SaaS ($10M-$50M ARR) qui ont récemment levé des fonds Série A/B - inclure les modèles d'email et la stack technologique", "2": "Rechercher des Ingénieurs Blockchain Senior avec expérience Solidity/Rust dans des projets crypto de premier plan, ouverts à la réinstallation à Dubaï ou Singapour", "3": "Générer une liste de prospects de fondateurs techniques dans des startups Seed-Série A en IA d'entreprise qui ont levé $2M-$15M au cours des 6 derniers mois", "4": "Identifier des Product Managers Senior dans des entreprises fintech avec 5-10 ans d'expérience de FAANG ou de licornes, compétents en développement de produit 0-1", "5": "Trouver des CIO et VP d'Ingénierie dans des entreprises de TI de santé de marché moyen (500-5000 employés) avec des budgets TI de $500K+ planifiant une migration cloud", "6": "Rechercher des VP Ventes dans des entreprises B2B SaaS affichant une croissance de 100%+ en glissement annuel, avec 7+ ans de clôture de transactions de $100K+ et expérience PLG", "7": "Construire une liste de CTO dans des entreprises qui mettent activement en œuvre une infrastructure IA avec des budgets multi-millions en 2024", "8": "Trouver des Designers UX/UI Senior avec expérience d'apps consommateurs mobile-first et portefeuilles de 1M+ utilisateurs, recherchant activement ou ouverts aux opportunités", "9": "Identifier des Ingénieurs DevOps Senior dans des startups cloud-native avec expertise Kubernetes/Terraform et 5-8 ans de construction d'infrastructure pour 10M+ utilisateurs"}, "research": {"0": "Analyser les tendances émergentes en informatique quantique et les applications commerciales potentielles", "1": "Rechercher les 10 principaux concurrents dans l'espace CRM alimenté par l'IA avec comparaison des fonctionnalités", "2": "Enquêter sur les exigences réglementaires pour lancer une app fintech dans l'UE", "3": "Compiler une analyse de marché sur les taux d'adoption de véhicules électriques dans les principaux marchés", "4": "Étudier l'impact du travail à distance sur la demande immobilière commerciale dans les grandes villes", "5": "Rechercher les modèles d'adoption Web3 parmi les entreprises Fortune 500", "6": "Analyser le sentiment des consommateurs envers les marques de mode durable", "7": "Enquêter sur les derniers développements en thérapie génique pour les maladies rares", "8": "Étudier les stratégies de prix des entreprises de boîtes d'abonnement D2C réussies", "9": "Rechercher le paysage concurrentiel des solutions de cybersécurité alimentées par l'IA"}}}}