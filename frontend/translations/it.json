{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvataggio in corso...", "success": "Successo", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON>", "ok": "OK", "yes": "Sì", "no": "No", "delete": "Elimina", "edit": "Modifica", "search": "Cerca", "loading": "Caricamento...", "error": "Errore", "retry": "<PERSON><PERSON><PERSON><PERSON>", "send": "Invia", "back": "Indietro", "new": "Nuovo", "create": "<PERSON><PERSON>", "update": "Aggiorna", "remove": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Conferma", "processing": "Elaborazione...", "keep": "<PERSON><PERSON><PERSON>", "active": "Attivo", "inactive": "Inattivo", "next": "<PERSON><PERSON>", "upgrade": "Aggiorna", "basic": "Base", "tryFree": "Inizia"}, "settings": {"title": "Impostazioni", "tabs": {"general": "Generale", "plan": "Piano", "billing": "Fatturazione", "usage": "<PERSON><PERSON><PERSON><PERSON>", "knowledgeBase": "Base di conoscenza", "integrations": "Integrazioni", "envManager": "Gestore ambiente"}, "general": {"title": "Impostazioni profilo", "description": "Gestisci le informazioni del tuo account", "name": "Nome", "email": "Email", "namePlaceholder": "Inser<PERSON>ci il tuo nome", "emailCannotChange": "L'email non può essere modificata da qui", "saveChanges": "Salva modifiche", "profileUpdated": "Profilo a<PERSON>rna<PERSON> con successo", "profileUpdateFailed": "Impossibile aggiornare il profilo", "language": {"title": "<PERSON><PERSON>", "description": "Scegli la tua lingua preferita", "current": "Lingua corrente", "select": "Seleziona lingua"}, "deleteAccount": {"title": "Elimina Account", "description": "Rimuovi permanentemente il tuo account e tutti i dati associati", "button": "Elimina account", "scheduled": "Eliminazione Programmata", "scheduledDescription": "Il tuo account sarà eliminato permanentemente il {date}.", "canCancel": "Puoi annullare questa richiesta in qualsiasi momento prima della data di eliminazione.", "cancelButton": "Annulla Eliminazione", "dialogTitle": "Elimina Account", "warning": "Questa azione non può essere annullata", "whenDelete": "Quando elimini il tuo account:", "agentsDeleted": "Tutti i tuoi agenti e versioni degli agenti", "threadsDeleted": "Tutti i tuoi thread e conversazioni", "credentialsRemoved": "<PERSON>tte le tue credenziali e integrazioni", "subscriptionCancelled": "I tuoi dati di abbonamento e fatturazione", "billingRemoved": "Tutti i dati di fatturazione saranno rimossi", "scheduled30Days": "Il tuo account sarà programmato per l'eliminazione tra 30 giorni", "gracePeriod": "Il tuo account sarà programmato per l'eliminazione tra 30 giorni. Puoi annullare questa richiesta in qualsiasi momento durante il periodo di grazia.", "confirmText": "Digita {text} per confermare", "confirmPlaceholder": "ELIMINA", "keepAccount": "Man<PERSON>i account", "cancelDeletionTitle": "Annulla Eliminazione Account", "cancelDeletionDescription": "Vuoi annullare l'eliminazione del tuo account? Il tuo account e tutti i dati saranno preservati.", "cancelDeletion": "Annulla Eliminazione"}}, "billing": {"title": "Fatturazione & Abbonamento", "description": "Gestisci i tuoi crediti e abbonamento", "totalCredits": "Crediti totali disponibili", "allCredits": "<PERSON><PERSON> i crediti", "monthlyCredits": "Crediti mensili", "renewalIn": "<PERSON><PERSON><PERSON> tra {days} {days, plural, one {giorno} other {giorni}}", "noRenewal": "<PERSON><PERSON><PERSON> rinnovo programmato", "extraCredits": "Crediti extra", "nonExpiring": "Non scadono", "renews": "<PERSON> rinnova {date}", "manageSubscription": "Gestisci abbonamento", "getAdditionalCredits": "Ottieni crediti aggiuntivi", "changePlan": "Cambia piano", "annualCommitment": "Impegno annuale", "activeUntil": "Attivo fino al {date}", "subscriptionCancelled": "Il tuo abbonamento sarà annullato il {date}", "reactivate": "<PERSON><PERSON><PERSON><PERSON>", "creditsExplained": "Crediti spiegati", "cancelPlan": "<PERSON><PERSON><PERSON> piano", "reactivateSubscription": "Riattiva abbonamento", "reactivating": "Riattivazione in corso...", "cancelDialogTitle": "<PERSON><PERSON><PERSON> a<PERSON>", "cancelDialogDescription": "Sei sicuro di voler annullare il tuo abbonamento? Continuerai ad avere accesso fino al {date}.", "keepSubscription": "<PERSON><PERSON><PERSON> abb<PERSON>", "cancelling": "Annullamento in corso...", "localMode": "Modalità locale attiva", "localModeDescription": "Tutte le funzionalità premium sono disponibili in questo ambiente", "insufficientCredits": "Crediti insufficienti", "insufficientCreditsMessage": "Controllo fatturazione fallito: Crediti insufficienti. Il tuo saldo è {balance}. Aggiungi crediti per continuare.", "creditsExhausted": "Hai esaurito i crediti. Aggiorna ora.", "billingCheckFailed": "Controllo fatturazione fallito. Aggiorna per continuare.", "failedToStartAgent": "Impossibile avviare l'agente {agentName}: {message}"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledgeBase": {"opening": "Apertura base di conoscenza", "redirecting": "Reindirizzamento alla pagina della base di conoscenza..."}, "integrations": {"opening": "Apertura integrazioni", "redirecting": "Reindirizzamento alla pagina delle integrazioni..."}}, "triggers": {"getStarted": "Inizia aggiungendo un trigger", "getStartedDescription": "Programma un trigger per automatizzare le azioni e ricevere promemoria al completamento.", "search": "Cerca", "scheduledTrigger": "Trigger programmato", "eventBasedTrigger": "Trigger basato su eventi", "chooseAgent": "Scegli un agente", "schedule": "Programma", "agentInstructions": "Istruzioni agente", "agentInstructionsDescription": "Prompt personalizzato per l'agente", "assignedAgent": "Agente assegnato", "unknownAgent": "<PERSON><PERSON>", "technicalDetails": "Dettagli tecnici", "type": "Tipo", "provider": "Fornitore", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Ultimo aggiornamento", "enable": "Abilita", "disable": "Disabilita", "deleteTask": "Elimina attività", "deleteTaskDescription": "Sei sicuro di voler eliminare \"{name}\"? Questa azione non può essere annullata e fermerà tutte le esecuzioni automatizzate di questa attività.", "taskEnabled": "Attività abilitata", "taskDisabled": "Attività disabilitata", "toggleTaskFailed": "Impossibile cambiare lo stato dell'attività", "taskDeletedSuccess": "Attività eliminata con successo", "deleteTaskFailed": "Impossibile eliminare l'attività", "taskUpdatedSuccess": "Attività aggiornata con successo", "updateTaskFailed": "Impossibile aggiornare l'attività", "pleaseSelectAgent": "Seleziona un agente", "defaultScheduledTriggerName": "Trigger programmato", "defaultScheduledTriggerDescription": "Trigger programmato automaticamente", "scheduleTriggerUpdated": "Trigger programmato aggiornato con successo", "scheduleTriggerCreated": "Trigger programmato creato con successo", "upgradeForMoreScheduledTriggers": "Aggiorna per creare più trigger programmati", "upgradeForMoreAppTriggers": "Aggiorna per creare più trigger basati su app", "updateScheduleTriggerFailed": "Impossibile aggiornare il trigger programmato", "createScheduleTriggerFailed": "Impossibile creare il trigger programmato", "editScheduledTask": "Modifica attività programmata", "createScheduledTask": "Crea attivit<PERSON> programmata", "editAppBasedTask": "Modifica attività basata su app", "createAppBasedTask": "Crea attività basata su app", "updateAgentForTask": "Aggiorna l'agente per questa attività", "selectAgentForTask": "Prima, seleziona quale agente dovrebbe gestire questa attività", "createNew": "Crea nuovo", "notConfigured": "Non configurato", "every15Minutes": "Ogni 15 minuti", "every30Minutes": "Ogni 30 minuti", "everyHour": "Ogni ora", "dailyAt9AM": "Giornalmente alle 9", "weekdaysAt9AM": "Giorni feriali alle 9", "weeklyOnMonday": "Settimanalmente il lunedì", "monthlyOn1st": "Mensilmente il 1°"}, "sidebar": {"chats": "Cha<PERSON>", "agents": "Agenti", "workers": "Worker", "triggers": "<PERSON><PERSON>", "newChat": "Nuova chat", "newAgent": "Nuovo agente", "settings": "Impostazioni", "logout": "<PERSON><PERSON><PERSON>", "openMenu": "Apri menu", "expandSidebar": "Espandi barra laterale (CMD+B)", "searchChats": "Cerca chat", "searchChatsPlaceholder": "Cerca chat...", "allTriggers": "<PERSON>tti i trigger", "addWorkers": "Aggiungi worker", "addTrigger": "Aggiungi trigger", "triggerConfig": "Configurazione trigger", "scheduledTrigger": "<PERSON><PERSON> pianificato", "scheduledTriggerDescription": "Pianifica un trigger da eseguire a un orario specifico", "eventBasedTrigger": "Trigger basato su eventi", "eventBasedTriggerDescription": "Crea un trigger da eseguire quando si verifica un evento", "noChatsFound": "Nessuna chat trovata", "search": "Cerca", "searchPlaceholder": "Cerca...", "searchResults": "Risultati di ricerca", "recent": "<PERSON><PERSON>", "personalAccount": "Account personale", "workspaces": "Aree di lavoro", "theme": "<PERSON><PERSON>", "integrations": "Integrazioni", "billing": "Fatturazione", "usage": "<PERSON><PERSON><PERSON><PERSON>", "adminPanel": "Pannello admin", "apiKeys": "Chiavi API", "envManager": "Gestore ambiente", "localEnvManager": "Gestore .Env locale", "advanced": "<PERSON><PERSON><PERSON>", "plan": "Piano", "knowledgeBase": "Base di conoscenza", "conversation": "conversazione", "conversations": "conversazioni", "noConversations": "Ancora nessun chat", "startNewChat": "Inizia un nuovo chat", "openInNewTab": "Apri in nuova scheda", "delete": "Elimina", "deleting": "Eliminazione", "allThreadsLoaded": "<PERSON>tti i thread caricati", "myWorkforce": "La mia forza lavoro", "addTeam": "Aggiungi team", "selectAll": "Se<PERSON><PERSON>na tutto", "deselectAll": "Deseleziona tutto", "selectAllConversations": "Seleziona tutte le conversazioni", "deselectAllConversations": "Deseleziona tutte le conversazioni", "loadMore": "Carica altro", "remaining": "<PERSON><PERSON><PERSON>"}, "dashboard": {"title": "Dashboard", "welcome": "<PERSON><PERSON><PERSON>", "whatWouldYouLike": "Cosa vuoi fare?", "describeWhatYouNeed": "Descrivi di cosa hai bisogno...", "usageLimits": "Limiti di utilizzo", "threads": "<PERSON><PERSON><PERSON>", "customWorkers": "Worker <PERSON><PERSON><PERSON><PERSON>", "scheduledTriggers": "<PERSON><PERSON> pianificati", "appTriggers": "Trigger app", "limitsExceeded": "Hai raggiunto il limite delle chat ({current}/{limit}). Aggiorna il tuo piano per creare più chat.", "agentInitiatedSuccessfully": "Agente avviato con successo", "aiAssistantReady": "Il tuo assistente AI è pronto ad aiutare"}, "thread": {"integrations": "Integrazioni", "connect": "<PERSON><PERSON><PERSON>", "seeAllIntegrations": "+ Vedi tutte le integrazioni", "connectIntegrations": "Connetti integrazioni", "adaptive": "Adattivo", "adaptiveDescription": "Risposte rapide con cambio di contesto intelligente", "autonomous": "Autonomo", "autonomousDescription": "Modalità lavoro approfondito per risoluzione di problemi multi-step", "chat": "Cha<PERSON>", "chatDescription": "Semplice conversazione", "sampleAnswers": "Risposte di esempio", "samplePrompts": "Prompt di esempio", "waitingForUserResponse": "Kortix continuerà a lavorare in modo autonomo dopo la tua risposta.", "taskCompleted": "Attività completata", "uploadingFiles": "Caricamento di {count} file{plural}...", "uploadingFilesOne": "Caricamento di {count} file...", "uploadingFilesMany": "Caricamento di {count} file...", "scrollToBottom": "<PERSON><PERSON><PERSON> in basso", "tools": "Strumenti", "instructions": "Istruzioni", "knowledge": "Conoscenza", "triggers": "<PERSON><PERSON>", "agents": "Agenti", "superWorker": "Super Worker", "searchWorkers": "Cerca worker...", "myWorkers": "I miei worker", "running": "In esecuzione", "minimizeToFloatingPreview": "Riduci a anteprima fluttuante", "close": "<PERSON><PERSON>", "noActionsYet": "Nessuna azione ancora", "workerActionsDescription": "Le azioni e i risultati del worker appariranno qui durante l'esecuzione", "toolIsRunning": "Strumento in esecuzione", "toolCurrentlyExecuting": "{toolName} è attualmente in esecuzione. I risultati appariranno qui al completamento.", "rateThisResult": "Valuta questo risultato", "feedbackHelpsImprove": "Il tuo feedback ci aiuta a migliorare", "additionalFeedbackOptional": "Feedback aggiuntivo (opzionale)", "helpKortixImprove": "Aiuta Kortix a migliorare con il tuo feedback", "submit": "Invia", "submitting": "Invio in corso...", "feedbackSubmittedSuccess": "Feedback inviato con successo", "feedbackSubmitFailed": "Invio del feedback fallito"}, "agents": {"title": "Agenti", "create": "<PERSON>rea agente", "createNewWorker": "Crea nuovo worker", "edit": "Modifica agente", "delete": "Elimina agente", "name": "Nome", "description": "Descrizione", "model": "<PERSON><PERSON>", "systemPrompt": "Prompt di sistema", "systemPromptPlaceholder": "Definisci come dovrebbe comportarsi il tuo agente...", "customizeIcon": "Clicca per personalizzare l'icona dell'agente"}, "home": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Inizia", "heroPlaceholder": "Descrivi il compito che vuoi che il tuo Worker completi..."}, "showcase": {"title": "Kortix: Il Tuo Worker IA Autonomo", "subtitle": "Costruito per compiti complessi, progettato per tutto. L'assistente IA definitivo che gestisce tutto—dalle richieste semplici ai progetti mega-complessi.", "tryItOut": "Provalo", "kortixComputer": "Kortix Computer", "running": "In esecuzione", "workers": {"images": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Crea immagini su richiesta. Dalle foto di prodotti ai grafici per social media fino alle illustrazioni complete. Regola stile, illuminazione, colori e layout, o perfeziona i visual esistenti con modifiche rapide e ritocchi.", "capabilities": {"0": "Generare foto di prodotti", "1": "<PERSON><PERSON><PERSON> grafici per social media", "2": "<PERSON><PERSON>re illustrazioni", "3": "Variazioni di stile e illuminazione", "4": "Creazione di logo / risorse", "5": "+ <PERSON><PERSON><PERSON> altro"}, "imageAlt": "Esempio grafico La crescita non è lineare", "fileType": "image"}, "slides": {"title": "Presentazioni", "description": "Crea presentazioni straordinarie all'istante. Dalle pitch deck ai rapporti fino ai materiali di formazione. Regola temi, layout, struttura del contenuto o perfeziona le presentazioni esistenti con modifiche e aggiornamenti rapidi.", "capabilities": {"0": "Pitch deck", "1": "Materiale di formazione", "2": "Presentazioni di rapporti", "3": "Variazioni di tema e layout", "4": "Ristrutturazione del contenuto", "5": "+ <PERSON><PERSON><PERSON> altro"}, "imageAlt": "Esempio di diapositiva Nexus Enterprise Automation Platform", "fileType": "PPTX"}, "data": {"title": "<PERSON><PERSON>", "description": "Trasforma i dati grezzi in insights. <PERSON> fogli di calcolo alle dashboard alle visualizzazioni. Pulisce i dataset, crea grafici, genera rapporti o perfeziona le analisi esistenti con aggiornamenti rapidi.", "capabilities": {"0": "Dashboard", "1": "Visualizzazioni", "2": "Rapporti sui dati", "3": "Pulire e organizzare i dati", "4": "Generare insights", "5": "+ <PERSON><PERSON><PERSON> altro"}, "imageAlt": "Esempio di dashboard modello finanziario", "fileType": "Anteprima"}, "docs": {"title": "Documenti", "description": "Scrive e modifica documenti senza sforzo. <PERSON>le proposte alle guide ai contenuti. Regola tono, struttura, formattazione o perfeziona i documenti esistenti con riscritture rapide e rifiniture.", "capabilities": {"0": "Proposte", "1": "Guide e manuali", "2": "Contenuti", "3": "Variazioni di tono e stile", "4": "Formattare e ristrutturare", "5": "+ <PERSON><PERSON><PERSON> altro"}, "imageAlt": "Esempio di rapporto esecutivo riepilogativo Q3 2025", "fileType": "PDF"}, "research": {"title": "Ricerca", "description": "Ricerca argomenti in modo completo. <PERSON>le tendenze di mercato all'analisi competitiva fino agli approfondimenti. Raccoglie fonti, sintetizza i risultati o perfeziona le ricerche esistenti con aggiornamenti rapidi.", "capabilities": {"0": "<PERSON><PERSON><PERSON><PERSON> le tendenze di mercato", "1": "Ricerca competitiva", "2": "Approfondimenti su argomenti", "3": "Ra<PERSON><PERSON><PERSON> fonti", "4": "Sintetizzare i risultati", "5": "+ <PERSON><PERSON><PERSON> altro"}, "imageAlt": "Esempio di ricerca Profili dettagliati dei concorrenti", "fileType": "PDF"}}}, "threads": {"title": "<PERSON><PERSON><PERSON>", "newThread": "Nuovo thread", "sendMessage": "Invia messaggio", "placeholder": "Digita il tuo messaggio..."}, "billing": {"title": "Fatturazione & Abbonamento", "subscription": "Abbonamento", "credits": "Crediti", "popular": "Popolare", "creditsExplained": "Crediti spiegati", "creditsExhaustedRefreshIn": "Hai esaurito i tuoi crediti. Si rinnoveranno tra {time}", "trialActive": "<PERSON>va attiva", "trialBadge": "Prova di 7 giorni", "currentPlan": "Piano Attuale", "currentBadge": "<PERSON><PERSON><PERSON>", "scheduled": "Programmato", "scheduledBadge": "Programmato", "loading": "Caricamento...", "subscriptionChangeScheduled": "Modifica abbonamento programmata", "planWillChangeOn": "Il tuo piano cambierà il {date}.", "failedToInitiateSubscription": "Impossibile avviare l'abbonamento. Riprova.", "subscriptionUpgraded": "Abbonamento aggiornato da {currentPrice} a {newPrice}", "subscriptionUpdated": "Abbonamento aggiornato con successo", "cannotDowngradeDuringCommitment": "Impossibile passare a un piano inferiore durante il periodo di impegno", "alreadyOnThisPlan": "Sei già su questo Piano.", "localModeMessage": "Esecuzione in modalità sviluppo locale - le funzionalità di fatturazione sono disabilitate", "creditsExplainedPage": {"title": "Crediti spiegati", "subtitle": "<PERSON>tto ciò che devi sapere su come funzionano i crediti su Kortix", "understandingCredits": {"title": "Comprendere i crediti", "description": "I crediti servono come valuta universale di Kortix per le operazioni della piattaforma. Ogni azione che i tuoi agenti AI eseguono—dall'analisi dei dati alla generazione di codice—consuma crediti in base alla complessità del compito e alle risorse richieste."}, "howCreditsWork": {"title": "Come funzionano i crediti", "description": "I crediti vengono consumati in base alle risorse che i tuoi agenti AI utilizzano:", "aiModelUsage": {"title": "Utilizzo del modello AI", "description": "Il principale motore del consumo di crediti", "content": "Diversi modelli AI hanno costi diversi in base alle loro capacità e all'utilizzo dei token. I crediti vengono consumati per i token di input (i tuoi prompt e contesto), i token di output (risposte dell'agente) e variano in base al livello del modello (Claude, GPT, ecc.)."}, "pricingModel": {"title": "<PERSON><PERSON> di <PERSON>zzo", "description": "20% di ricarico sui costi del modello AI", "content": "Applichiamo un ricarico del 20% su tutti i costi API e del modello per coprire l'infrastruttura della piattaforma, la sicurezza e lo sviluppo continuo. Questa tariffazione trasparente garantisce che tu sappia esattamente per cosa stai pagando."}}, "gettingMoreCredits": {"title": "Ottenere più crediti", "description": "Ci sono diversi modi per ottenere crediti su Kortix:", "monthlySubscription": {"title": "Crediti abbonamento mensile", "description": "Inclusi nel tuo piano a pagamento e rinnovati automaticamente ogni mese. Questi sono crediti in scadenza."}, "topUpCredits": {"title": "Crediti di ricarica", "description": "Acquista crediti aggiuntivi quando ne hai bisogno. Questi non scadono e sono disponibili per i membri premium."}, "promotionalGrants": {"title": "Buoni promozionali ed eventi", "description": "Crediti bonus da eventi speciali, promozioni o referral. Questi non scadono."}, "refunds": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Crediti restituiti a causa di problemi tecnici o attività fallite. Questi non scadono."}}, "typesOfCredits": {"title": "Tipi di crediti", "description": "Kortix utilizza due tipi di crediti per darti flessibilità nella gestione del tuo utilizzo:", "expiringCredits": {"title": "Crediti in scadenza", "description": "Crediti abbonamento mensile", "content": "Questi crediti sono inclusi nel tuo abbonamento a pagamento e vengono rinnovati automaticamente ogni mese nella data del tuo abbonamento. Scadono alla fine di ogni ciclo di fatturazione e vengono sempre consumati per primi prima di qualsiasi credito non in scadenza."}, "nonExpiringCredits": {"title": "Crediti non in scadenza", "description": "Crediti permanenti che non scadono mai", "content": "Questi crediti non scadono mai e si trasferiscono di mese in mese. Includono acquisti di ricarica, rimborsi e buoni promozionali. I crediti non in scadenza vengono utilizzati solo dopo che i tuoi crediti in scadenza sono stati esauriti."}, "creditPriority": {"title": "Priorità crediti:", "description": "Quando usi Kortix, i crediti in scadenza vengono consumati per primi. Solo dopo che i tuoi crediti in scadenza sono esauriti, verranno utilizzati i crediti non in scadenza."}}}, "close": "<PERSON><PERSON>", "cancelScheduledChange": "<PERSON><PERSON><PERSON> modifica pianificata", "threadUsage": "Utilizzo thread", "error": "Errore", "totalUsage": "Utilizzo totale", "usage": "<PERSON><PERSON><PERSON><PERSON>", "thread": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "creditsUsed": "Crediti utilizzati", "creditTransactions": "Transazioni crediti", "loadingTransactionHistory": "Caricamento cronologia transazioni...", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "Annuale", "discount": "15% di sconto", "perMonth": "/mese", "billedYearly": "fatturato annualmente", "downgradePending": "Downgrade in sospeso", "downgrade": "Passa a un piano inferiore", "switchToLegacyYearly": "Passa al piano annuale legacy", "pickPlan": "<PERSON><PERSON><PERSON> il piano che fa per te.", "reachedLimit": "Hai raggiunto i tuoi limiti.", "projectLimit": "Limite pro<PERSON><PERSON> ({current}/{limit})", "threadLimit": "Limite thread ({current}/{limit})", "workerLimit": "Limite worker ({current}/{limit})", "triggerLimit": "Limite trigger ({current}/{limit})", "getAdditionalCredits": "Ottieni crediti aggiuntivi", "selectPlan": "Seleziona piano", "changeScheduled": "Modifica pianificata", "notAvailable": "Non disponibile", "features": {"creditsPerMonth": "{count} crediti/mese", "customWorker": "{count} Worker <PERSON><PERSON><PERSON><PERSON>", "customWorkers": "{count} Worker <PERSON><PERSON><PERSON><PERSON>", "privateProject": "{count} progetto privato", "privateProjects": "Progetti privati", "customTrigger": "{count} trigger personal<PERSON>zato", "integrations": "100+ integrazioni", "premiumAIModels": "Modelli AI Premium", "prioritySupport": "Supporto prioritario"}}, "auth": {"logIntoAccount": "Accedi al tuo account", "createAccount": "Crea account", "signInOrCreateAccount": "Accedi o crea il tuo account Kortix", "continueWithGoogle": "Continua con Google", "continueWithGitHub": "Continua con GitHub", "orEmail": "o email", "emailAddress": "Indirizzo email", "password": "Password", "confirmPassword": "Conferma password", "signIn": "Accedi", "signUp": "Registrati", "signingIn": "Accesso in corso...", "creatingAccount": "Creazione account in corso...", "forgotPassword": "Password dimenticata?", "dontHaveAccount": "Non hai un account?", "alreadyHaveAccount": "Hai già un account?", "acceptPrivacyTerms": "Accetto l'<privacyPolicy>Informativa sulla Privacy</privacyPolicy> e i <termsOfService>Termini di Servizio</termsOfService>", "privacyPolicy": "Informativa sulla Privacy", "termsOfService": "Termini di Servizio", "checkYourEmail": "Controlla la tua email", "confirmationLinkSent": "Ti abbiamo inviato un link a", "clickLinkToActivate": "Clicca sul link nell'email per attivare il tuo account. Se non vedi l'email, controlla la cartella spam.", "returnToHome": "Torna alla home", "backToSignIn": "Torna al login", "resetPassword": "Reim<PERSON>a password", "resetPasswordDescription": "Inserisci la tua email e ti invieremo un link per reimpostare la password", "sendResetLink": "Invia link", "cancel": "<PERSON><PERSON><PERSON>", "pleaseEnterValidEmail": "Inserisci un indirizzo email valido", "signInFailed": "Accesso fallito. Controlla le tue credenziali.", "signUpFailed": "Registrazione fallita. Riprova.", "sendMagicLink": "Invia magic link", "sending": "Invio in corso...", "magicLinkSent": "Ti abbiamo inviato un magic link a", "magicLinkDescription": "Clicca sul link nella tua email per accedere. Il link scadrà tra 1 ora.", "magicLinkExplanation": "Ti invieremo un link sicuro per accedere. Nessuna password necessaria.", "byContinuingYouAgree": "<PERSON><PERSON><PERSON><PERSON>, accetti la nostra <privacyPolicy>Informativa sulla Privacy</privacyPolicy> e i nostri <termsOfService>Termini di Servizio</termsOfService>"}, "onboarding": {"welcome": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "skip": "Salta"}, "tools": {"executeCommand": "Esecuzione comando", "checkCommandOutput": "Controllo output comando", "terminateCommand": "Terminazione comando", "listCommands": "Elencazione comandi", "createFile": "Creazione file", "deleteFile": "Eliminazione file", "fullFileRewrite": "Riscrittura file", "strReplace": "Modifica testo", "editFile": "Modifica file", "uploadFile": "Caricamento file", "createDocument": "Creazione documento", "updateDocument": "Aggiornamento documento", "readDocument": "Lettura documento", "listDocuments": "Elencazione documenti", "deleteDocument": "Eliminazione documento", "createTasks": "Creazione attività", "updateTasks": "Aggiornamento attività", "browserNavigateTo": "Navigazione alla pagina", "browserAct": "Esecuzione azione", "browserExtractContent": "Estrazione contenuto", "browserScreenshot": "Acquisizione screenshot", "executeDataProviderCall": "Chiamata provider dati", "getDataProviderEndpoints": "Recupero endpoint", "ask": "<PERSON><PERSON><PERSON>", "wait": "<PERSON><PERSON><PERSON>", "complete": "Completamento attività", "crawlWebpage": "Scansione sito web", "exposePort": "Esposizione porta", "scrapeWebpage": "Scraping sito web", "webSearch": "Ricerca web", "loadImage": "Caricamento immagine", "createPresentationOutline": "Creazione struttura presentazione", "createPresentation": "Creazione presentazione", "presentPresentation": "Presentazione", "clearImagesFromContext": "Rimozione immagini dal contesto", "imageSearch": "Ricerca immagine", "createSheet": "Creazione foglio", "updateSheet": "Aggiornamento foglio", "viewSheet": "Visualizzazione foglio", "analyzeSheet": "<PERSON><PERSON><PERSON>", "visualizeSheet": "Visualizzazione foglio", "formatSheet": "Formattazione foglio", "updateAgent": "Aggiornamento agente", "getCurrentAgentConfig": "Recupero configurazione agente", "searchMcpServers": "Ricerca server MCP", "getMcpServerTools": "Recupero strumenti server MCP", "configureMcpServer": "Configurazione server MCP", "getPopularMcpServers": "Recupero server MCP popolari", "testMcpServerConnection": "Test connessione server MCP", "listAppEventTriggers": "Ricerca trigger eventi", "createEventTrigger": "Creazione trigger evento", "getProjectStructure": "Recup<PERSON> struttura progetto", "buildProject": "Creazione progetto", "makePhoneCall": "Effettuazione chiamata", "endCall": "Terminazione chiamata", "getCallDetails": "<PERSON><PERSON><PERSON> de<PERSON> chiamata", "listCalls": "Elencazione chiamate", "monitorCall": "<PERSON><PERSON><PERSON> chiamata", "waitForCallCompletion": "Attesa completamento", "getPaperDetails": "Recupero de<PERSON>gli documento", "searchAuthors": "Ricerca autori", "getAuthorDetails": "Recupero dettagli autore", "getAuthorPapers": "Recupero documenti autore", "getPaperCitations": "Recupero citazioni documento", "getPaperReferences": "Recupero riferimenti documento", "paperSearch": "Ricerca documenti", "discoverUserMcpServers": "Scoper<PERSON> strumenti", "createCredentialProfile": "Creazione profilo", "getCredentialProfiles": "<PERSON><PERSON><PERSON> profili", "configureProfileForAgent": "Aggiunta strumenti all'agente", "createNewAgent": "Creazione nuovo agente", "searchMcpServersForAgent": "Ricerca server MCP", "createCredentialProfileForAgent": "Creazione profilo credenziali", "discoverMcpToolsForAgent": "Scoperta strumenti MCP", "configureAgentIntegration": "Configurazione integrazione agente", "createAgentScheduledTrigger": "Creazione trigger programmato", "listAgentScheduledTriggers": "Elencazione trigger programmati agente", "executingTool": "Esecuzione strumento", "toolExecutedSuccessfully": "Strumento eseguito con successo", "toolExecutionFailed": "Esecuzione strumento fallita", "input": "Input", "output": "Output", "copyFileContent": "Copia contenuto file", "fileContentCopied": "Contenuto file copiato negli appunti", "failedToCopyFileContent": "Impossibile copiare il contenuto del file", "noContentAvailable": "<PERSON><PERSON><PERSON> contenuto disponibile", "noContentDescription": "Questa esecuzione dello strumento non ha prodotto alcun contenuto di input o output da visualizzare.", "tool": "Strumento"}, "agentRunLimit": {"gotIt": "Capito", "parallelRunsLimitReached": "Limite esecuzioni parallele raggiunto", "parallelRunsLimitDescription": "Hai raggiunto il numero massimo di esecuzioni parallele di agenti consentite.", "needMoreParallelRuns": "Hai bisogno di più esecuzioni parallele?", "upgradeDescription": "Aggiorna il tuo piano per eseguire più agenti contemporaneamente e aumentare la tua produttività.", "upgradePlan": "Aggiorna piano", "currentlyRunningAgents": "Agenti attualmente in esecuzione", "loadingThreads": "Caricamento thread...", "foundRunningAgents": "Trovati {count} agenti in esecuzione ma impossibile caricare i dettagli del thread.", "threadIds": "ID thread: {ids}", "moreRunning": "+{count} altri in esecuzione", "whatCanYouDo": "Cosa puoi fare?", "stopAgentsHint": "Clicca il pulsante di stop per fermare gli agenti in esecuzione", "waitForCompletion": "Attendi che un agente si completi automaticamente", "upgradeYourPlan": "Aggiorna il tuo piano", "forMoreParallelRuns": "per più esecuzioni parallele", "stopAgent": "Ferma agente", "stopThisAgent": "Ferma questo agente", "openThread": "Apri thread", "agentStoppedSuccessfully": "Agente fermato con successo", "failedToStopAgent": "Impossibile fermare l'agente", "upgradeToRunMore": "Aggiorna per eseguire più agenti in parallelo"}, "errors": {"generic": "Si è verificato un errore", "network": "Errore di rete", "unauthorized": "Non autorizzato", "notFound": "Non trovato"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano"}, "suna": {"samplePrompts": "Prompt di esempio", "chooseStyle": "<PERSON><PERSON><PERSON> uno stile", "chooseTemplate": "Scegli un modello", "chooseOutputFormat": "Scegli formato di output", "preferredCharts": "Grafici preferiti", "whatWouldYouLike": "Cosa vuoi fare?", "describeTask": "Descrivi il compito che vuoi che il tuo Worker completi...", "modes": {"image": "<PERSON><PERSON><PERSON><PERSON>", "slides": "Presentazioni", "data": "<PERSON><PERSON>", "docs": "Documenti", "people": "<PERSON>e", "research": "Ricerca"}, "styles": {"photorealistic": "Fotorealistico", "watercolor": "<PERSON><PERSON><PERSON><PERSON>", "digital-art": "Arte digitale", "oil-painting": "Pittura ad olio", "minimalist": "Minimalista", "isometric": "Isometrico", "vintage": "Vintage", "comic": "<PERSON><PERSON><PERSON>", "neon": "Neon", "pastel": "<PERSON><PERSON>", "geometric": "Geometrico", "abstract": "<PERSON><PERSON><PERSON>", "anime": "Anime", "impressionist": "Impressionista", "surreal": "Surreale"}, "templates": {"minimalist": {"name": "Minimalista", "description": "Design pulito e semplice"}, "minimalist_2": {"name": "Minimalista 2", "description": "Stile minimalista alternativo"}, "black_and_white_clean": {"name": "Bianco e nero", "description": "Monocromatico classico"}, "colorful": {"name": "Colorato", "description": "Vibrante ed energico"}, "startup": {"name": "Startup", "description": "Dinamico e innovativo"}, "elevator_pitch": {"name": "Elevator Pitch", "description": "Rapido e impattante"}, "portfolio": {"name": "Portfolio", "description": "Mostra il tuo lavoro"}, "textbook": {"name": "Manuale", "description": "Educativo e strutturato"}, "architect": {"name": "<PERSON><PERSON><PERSON>", "description": "Professionale e preciso"}, "hipster": {"name": "<PERSON><PERSON>", "description": "Moderno e trendy"}, "green": {"name": "Verde", "description": "Design ispirato alla natura"}, "premium_black": {"name": "Premium Nero", "description": "Tema scuro di lusso"}, "premium_green": {"name": "Premium Verde", "description": "Verde sofisticato"}, "professor_gray": {"name": "<PERSON><PERSON>", "description": "Accademico e erudito"}, "gamer_gray": {"name": "<PERSON><PERSON>", "description": "Design ispirato ai giochi"}, "competitor_analysis_blue": {"name": "<PERSON><PERSON><PERSON>", "description": "Focalizzato sull'analisi aziendale"}, "numbers_clean": {"name": "<PERSON><PERSON><PERSON>", "description": "Visualizzazione dati pulita"}, "numbers_colorful": {"name": "Numeri Colorati", "description": "Presentazione dati vibrante"}, "prd": {"name": "PRD", "description": "Documento dei requisiti del prodotto"}, "technical": {"name": "Tecnico", "description": "Documentazione tecnica"}, "proposal": {"name": "Proposta", "description": "Proposta commerciale"}, "report": {"name": "Report", "description": "Formato report dettagliato"}, "guide": {"name": "<PERSON><PERSON><PERSON>", "description": "Guida passo-passo"}, "wiki": {"name": "Wiki", "description": "Articolo della knowledge base"}, "policy": {"name": "Politica", "description": "Documento politico"}, "meeting-notes": {"name": "Note di riunione", "description": "Verbale di riunione"}}, "outputFormats": {"spreadsheet": {"name": "Foglio di calcolo", "description": "Tabella con formule"}, "dashboard": {"name": "Dashboard", "description": "Grafici interattivi"}, "report": {"name": "Report", "description": "Analisi con visualizzazioni"}, "slides": {"name": "Presentazioni", "description": "Formato presentazione"}}, "charts": {"bar": "Barr<PERSON>", "line": "Linea", "pie": "<PERSON><PERSON>", "scatter": "Dispersione", "heatmap": "Mappa di calore", "bubble": "<PERSON><PERSON>", "wordcloud": "Nuvola di parole", "stacked": "Barra impilata", "area": "Area"}, "prompts": {"image": {"0": "Una maestosa aquila dorata che vola attraverso picchi montuosi nebbiosi all'alba con illuminazione drammatica", "1": "<PERSON><PERSON><PERSON><PERSON> di una modella con trucco avant-garde, illuminazione da studio, ombre ad alto contrasto", "2": "Soggiorno scandinavo accogliente con mobili in legno naturale, piante d'appartamento e morbida luce mattutina", "3": "Mercato stradale cyberpunk futuristico di notte con insegne al neon, pavimento bagnato dalla pioggia e display olografici", "4": "Fotografia di prodotto elegante di una bottiglia di profumo di lusso su superficie di marmo con morbide riflessioni", "5": "<PERSON><PERSON> galleggianti capricciose collegate da ponti di corda in un cielo pastello con nuvole sognanti", "6": "Macro ravvicinato di gocce di rugiada mattutina su petali di fiori vibranti con sfondo bokeh", "7": "Setup moderno di scrivania da lavoro con laptop, caffè, taccuino e piante grasse dall'alto", "8": "Sentiero forestale mistico con alberi antichi, lucciole luminose e raggi di luce eterei attraverso la nebbia", "9": "Dettaglio architettonico di una facciata di edificio in vetro contemporaneo con motivi geometrici e riflessi", "10": "Bancarella di street food vibrante con ingredienti colorati, vapore che sale e illuminazione calda", "11": "Giardino zen giapponese sereno con sabbia rastrellata, pietre ricoperte di muschio e petali di ciliegio", "12": "Scatto d'azione dinamico di un atleta a metà salto contro un cielo al tramonto drammatico, effetto silhouette", "13": "<PERSON><PERSON>ina rustica di una fattoria con pentole di rame, erbe fresche, taglieri di legno e texture naturali", "14": "Arte fluida astratta con motivi organici vorticosi in oro metallico, blu profondo e verde smeraldo"}, "slides": {"0": "Crea un pitch deck Series A con dimensione del mercato, trazione e proiezioni finanziarie", "1": "Costruisci una revisione aziendale Q4 che mostra KPI, successi e iniziative strategiche", "2": "Progetta una presentazione di lancio prodotto con video demo e testimonianze dei clienti", "3": "Sviluppa un deck di abilitazione alle vendite che spiega la nostra proposta di valore e i vantaggi competitivi", "4": "Crea un aggiornamento per investitori che evidenzia metriche chiave e traguardi imminenti", "5": "Costruisci una presentazione di case study cliente che mostra ROI e metriche di successo", "6": "Progetta una presentazione all-hands che copre aggiornamenti aziendali e visione", "7": "Sviluppa un deck di formazione per nuove funzionalità prodotto e workflow", "8": "Crea un talk da conferenza sul ridimensionamento dei team di ingegneria", "9": "Costruisci una presentazione per riunione del consiglio con raccomandazioni strategiche"}, "data": {"0": "Costruisci un modello finanziario che proietta la crescita ARR con diversi scenari di prezzo", "1": "Crea una dashboard di vendita interattiva che traccia metriche per regione e trimestre", "2": "Analizza 50K recensioni dei clienti e visualizza le tendenze del sentiment nel tempo", "3": "Progetta un calendario dei contenuti che traccia campagne con grafici ROI e coinvolgimento", "4": "Costruisci un'analisi di coorte che mostra pattern di retention e churn degli utenti", "5": "Crea un modello di attribuzione marketing che confronta le prestazioni dei canali", "6": "Sviluppa un tracker di assunzione con metriche della pipeline e analisi time-to-fill", "7": "Costruisci un foglio di calcolo per la pianificazione del budget con modellazione di scenari", "8": "Analizza i dati del traffico del sito web e visualizza i funnel di conversione", "9": "Crea un sistema di gestione dell'inventario con avvisi di riordino automatizzati"}, "docs": {"0": "Scrivi un PRD completo per un motore di raccomandazione alimentato da AI", "1": "Redigi un documento di architettura tecnica per una piattaforma di microservizi scalabile", "2": "Crea un documento di strategia go-to-market per il lancio del nostro prodotto Q2", "3": "Sviluppa un playbook di onboarding di 90 giorni per i manager di ingegneria", "4": "Scrivi una guida alla documentazione API con esempi e best practice", "5": "Crea un manuale aziendale che copre cultura, politiche e benefici", "6": "Redigi una politica sulla privacy dei dati conforme a GDPR e CCPA", "7": "Sviluppa un playbook di successo del cliente per account SaaS enterprise", "8": "Sc<PERSON>vi un piano di risposta agli incidenti di sicurezza con procedure di escalation", "9": "Crea una guida di stile completa per brand e contenuti"}, "people": {"0": "<PERSON><PERSON><PERSON> candidati VP of Engineering presso startup AI/ML Series B+ nell'area di San Francisco Bay con 10+ anni di esperienza e comprovata esperienza nel ridimensionamento di team di ingegneria", "1": "Costruisci una lista di lead di CMO presso aziende B2B SaaS (10-50M $ ARR) che hanno recentemente raccolto finanziamenti Series A/B - includi pattern email e tech stack", "2": "Ricerca Senior Blockchain Engineers con esperienza Solidity/Rust presso progetti crypto di alto livello, aperti al trasferimento a Dubai o Singapore", "3": "Genera una lista di prospect di fondatori tecnici presso startup Seed-Series A nel settore Enterprise AI che hanno raccolto 2-15M $ negli ultimi 6 mesi", "4": "Identifica Senior Product Manager presso aziende fintech con 5-10 anni di esperienza da FAANG o unicorni, esperti nello sviluppo prodo<PERSON> 0-1", "5": "Trova CIO e VP Engineering presso aziende IT sanitarie mid-market (500-5000 dipendenti) con budget IT da 500K+ $ che pianificano la migrazione cloud", "6": "Ricerca VP Sales presso aziende B2B SaaS che mostrano crescita YoY del 100%+, con 7+ anni di esperienza nella chiusura di deal da 100K+ $ ed esperienza PLG", "7": "Costruisci una lista di CTO presso aziende enterprise che implementano attivamente infrastruttura AI con budget multi-milionari nel 2024", "8": "Trova Senior UX/UI Designer con esperienza in app consumer mobile-first e portfolio con 1M+ utenti, attivamente in cerca o aperti a opportunità", "9": "Identifica Senior DevOps Engineers presso startup cloud-native con competenze Kubernetes/Terraform e 5-8 anni di esperienza nella costruzione di infrastruttura per 10M+ utenti"}, "research": {"0": "<PERSON><PERSON>zza le tendenze emergenti nel quantum computing e le potenziali applicazioni aziendali", "1": "Ricerca i top 10 competitor nello spazio CRM alimentato da AI con confronto delle funzionalità", "2": "Investiga i requisiti normativi per il lancio di un'app fintech nell'UE", "3": "Compila un'analisi di mercato sui tassi di adozione di veicoli elettrici nei principali mercati", "4": "Studia l'impatto del lavoro remoto sulla domanda di immobili commerciali nelle principali città", "5": "Ricerca i pattern di adozione Web3 tra le aziende Fortune 500", "6": "Analizza il sentiment dei consumatori verso i brand di moda sostenibile", "7": "Investiga gli ultimi sviluppi nella terapia genica per malattie rare", "8": "Studia le strategie di prezzo delle aziende di successo di scatole di abbonamento D2C", "9": "Ricerca il panorama competitivo delle soluzioni di cybersecurity alimentate da AI"}}}}