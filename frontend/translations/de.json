{"common": {"cancel": "Abbrechen", "save": "Speichern", "saving": "Wird gespeichert...", "success": "Erfolg", "done": "<PERSON><PERSON><PERSON>", "close": "Schließen", "ok": "OK", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "loading": "Lädt...", "error": "<PERSON><PERSON>", "retry": "Noch<PERSON> versuchen", "send": "Senden", "back": "Zurück", "new": "<PERSON>eu", "create": "<PERSON><PERSON><PERSON><PERSON>", "update": "Aktualisieren", "remove": "Entfernen", "confirm": "Bestätigen", "processing": "Wird verarbeitet...", "keep": "Behalten", "active": "Aktiv", "inactive": "Inaktiv", "next": "<PERSON><PERSON>", "upgrade": "Upgrade", "basic": "<PERSON><PERSON>", "tryFree": "Loslegen"}, "settings": {"title": "Einstellungen", "tabs": {"general": "Allgemein", "plan": "Plan", "billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usage": "<PERSON><PERSON>ung", "knowledgeBase": "Wissensdatenbank", "integrations": "Integrationen", "envManager": "Umgebungs-Manager"}, "general": {"title": "Profileinstellungen", "description": "Verwalte deine Kontoinformationen", "name": "Name", "email": "E-Mail", "namePlaceholder": "Gib deinen <PERSON>n ein", "emailCannotChange": "E-Mail kann hier nicht geändert werden", "saveChanges": "Änderungen speichern", "profileUpdated": "<PERSON>il er<PERSON><PERSON>g<PERSON>ich aktualisiert", "profileUpdateFailed": "Profil konnte nicht aktualisiert werden", "language": {"title": "<PERSON><PERSON><PERSON>", "description": "Wähle deine bevorzugte Sprache", "current": "Aktuelle Sprache", "select": "Sprache auswählen"}, "deleteAccount": {"title": "Konto löschen", "description": "Entferne dein Konto und alle zugehörigen Daten dauerhaft", "button": "Konto löschen", "scheduled": "Löschung geplant", "scheduledDescription": "De<PERSON> wird am {date} dauerhaft gelöscht.", "canCancel": "Du kannst diese Anfrage jederzeit vor dem Löschdatum stornieren.", "cancelButton": "Löschungsanfrage stornieren", "dialogTitle": "Konto löschen", "warning": "Diese Aktion kann nach 30 Tagen nicht rückgängig gemacht werden", "whenDelete": "Wenn du dein Konto löschst:", "agentsDeleted": "Alle deine Agenten und Agent-Versionen werden gelöscht", "threadsDeleted": "Alle deine Threads und Unterhaltungen werden gelöscht", "credentialsRemoved": "Alle deine Anmeldedaten und Integrationen werden entfernt", "subscriptionCancelled": "<PERSON><PERSON> wird g<PERSON><PERSON>gt", "billingRemoved": "Alle Abrechnungsdaten werden entfernt", "scheduled30Days": "<PERSON><PERSON> wird für die Löschung in 30 Tagen eingeplant", "gracePeriod": "Du kannst diese Anfrage jederzeit innerhalb der 30-tägigen Frist stornieren. Nach 30 Tagen werden alle deine Daten dauerhaft gelöscht und können nicht wiederhergestellt werden.", "confirmText": "<PERSON><PERSON><PERSON> delete zur Bestätigung", "confirmPlaceholder": "delete", "keepAccount": "<PERSON><PERSON> be<PERSON>en", "cancelDeletionTitle": "Kontolöschung stornieren", "cancelDeletionDescription": "Möchtest du die Löschung deines Kontos stornieren? Dein Ko<PERSON> und alle Daten bleiben erhalten.", "cancelDeletion": "Löschung stornieren"}}, "billing": {"title": "Abrechnungss<PERSON>us", "description": "Verwalte deine Credits und dein Abonnement", "totalCredits": "Gesamte verfügbare Credits", "allCredits": "Alle Credits", "monthlyCredits": "Monatliche Credits", "renewalIn": "Erneuerung in {days} {days, plural, one {Tag} other {Tagen}}", "noRenewal": "<PERSON><PERSON> gep<PERSON>", "extraCredits": "Zusätzliche Credits", "nonExpiring": "<PERSON>ä<PERSON>t nicht ab", "renews": "<PERSON><PERSON><PERSON><PERSON> sich {date}", "manageSubscription": "Abonnement verwalten", "getAdditionalCredits": "Zusätzliche Credits erhalten", "changePlan": "Plan ändern", "annualCommitment": "Jährliche Verpflichtung", "activeUntil": "Aktiv bis {date}", "subscriptionCancelled": "De<PERSON> wird am {date} g<PERSON><PERSON><PERSON><PERSON>", "reactivate": "Reaktivieren", "creditsExplained": "Credits erklärt", "cancelPlan": "Plan kündigen", "reactivateSubscription": "Abonnement reaktivieren", "reactivating": "Wird reaktiviert...", "cancelDialogTitle": "Abonnement kündigen", "cancelDialogDescription": "B<PERSON> du sicher, dass du dein Abonnement kündigen möchtest? Du hast weiterhin Zugriff bis {date}.", "keepSubscription": "Abonnement behalten", "cancelling": "Wird gekündigt...", "localMode": "Lokaler Modus aktiv", "localModeDescription": "Alle Premium-Funktionen sind in dieser Umgebung verfügbar", "insufficientCredits": "Unzureichende Credits", "insufficientCreditsMessage": "Abrechnungsprüfung fehlgeschlagen: Unzureichende Credits. Dein G<PERSON>aben beträgt {balance}. Bitte füge Credits hinzu, um fortzufahren.", "creditsExhausted": "Dir sind die Credits ausgegangen. Jetzt upgraden.", "billingCheckFailed": "Abrechnungsprüfung fehlgeschlagen. Bitte upgraden, um fortzufahren.", "failedToStartAgent": "Agent {agent<PERSON><PERSON>} konnte nicht gestartet werden: {message}"}, "usage": {"title": "<PERSON><PERSON>ung"}, "knowledgeBase": {"opening": "Wissensdatenbank wird geöffnet", "redirecting": "Weiterleitung zur Wissensdatenbank-Seite..."}, "integrations": {"opening": "Integrationen werden geöffnet", "redirecting": "Weiterleitung zur Integrations-Seite..."}}, "triggers": {"getStarted": "Beginnen Sie mit dem Hinzufügen eines Triggers", "getStartedDescription": "Planen Sie einen Trigger, um Aktionen zu automatisieren und Erinnerungen zu erhalten, wenn sie abgeschlossen sind.", "search": "<PERSON><PERSON>", "scheduledTrigger": "<PERSON><PERSON><PERSON><PERSON>", "eventBasedTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseAgent": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schedule": "Zeitplan", "agentInstructions": "Agent-An<PERSON><PERSON>gen", "agentInstructionsDescription": "Benutzerdefinierter Prompt für den Agenten", "assignedAgent": "Zugewiesener Agent", "unknownAgent": "Unbekannter Agent", "technicalDetails": "Technische Details", "type": "<PERSON><PERSON>", "provider": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "lastUpdated": "Zuletzt aktualisiert", "enable": "Aktivieren", "disable": "Deaktivieren", "deleteTask": "Aufgabe löschen", "deleteTaskDescription": "Bist du sicher, dass du \"{name}\" löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden und stoppt alle automatisierten Ausführungen dieser Aufgabe.", "taskEnabled": "Aufgabe aktiviert", "taskDisabled": "Aufgabe deaktiviert", "toggleTaskFailed": "Aufgabe konnte nicht umgeschaltet werden", "taskDeletedSuccess": "Aufgabe erfolgreich <PERSON>t", "deleteTaskFailed": "Aufgabe konnte nicht gelöscht werden", "taskUpdatedSuccess": "Aufgabe erfolgreich aktualisiert", "updateTaskFailed": "Aufgabe konnte nicht aktualisiert werden", "pleaseSelectAgent": "<PERSON>te wähle einen Agenten aus", "defaultScheduledTriggerName": "<PERSON><PERSON><PERSON><PERSON>", "defaultScheduledTriggerDescription": "Automatisch geplanter Trigger", "scheduleTriggerUpdated": "Geplanter Trigger erfolgreich aktualisiert", "scheduleTriggerCreated": "Geplanter <PERSON> erfolgreich erstellt", "upgradeForMoreScheduledTriggers": "Upgrade, um mehr geplante <PERSON> zu erstellen", "upgradeForMoreAppTriggers": "Upgrade, um mehr App-basierte <PERSON>gger zu erstellen", "updateScheduleTriggerFailed": "Fehler beim Aktualisieren des geplanten Triggers", "createScheduleTriggerFailed": "Fehler beim Erstellen des geplanten Triggers", "editScheduledTask": "G<PERSON>lante Aufgabe bearbeiten", "createScheduledTask": "Geplante Aufgabe erstellen", "editAppBasedTask": "App-basierte Aufgabe bearbeiten", "createAppBasedTask": "App-basierte Aufgabe erstellen", "updateAgentForTask": "Agent für diese Aufgabe aktualisieren", "selectAgentForTask": "<PERSON><PERSON><PERSON>e zu<PERSON>t den Agenten aus, der diese Aufgabe bearbeiten soll", "createNew": "<PERSON><PERSON>", "notConfigured": "<PERSON>cht konfiguriert", "every15Minutes": "Alle 15 Minuten", "every30Minutes": "Alle 30 Minuten", "everyHour": "Jede Stunde", "dailyAt9AM": "Täglich um 9 Uhr", "weekdaysAt9AM": "Werktags um 9 Uhr", "weeklyOnMonday": "Wöchentlich am Montag", "monthlyOn1st": "Monatlich am 1."}, "sidebar": {"chats": "Chats", "agents": "<PERSON><PERSON>", "workers": "Worker", "triggers": "<PERSON><PERSON>", "newChat": "<PERSON><PERSON><PERSON>", "newAgent": "Neuer Agent", "settings": "Einstellungen", "logout": "Abmelden", "openMenu": "<PERSON><PERSON>", "expandSidebar": "Sidebar erweitern (CMD+B)", "searchChats": "<PERSON>ts durchsuchen", "searchChatsPlaceholder": "Chats durchsuchen...", "allTriggers": "<PERSON><PERSON>", "addWorkers": "<PERSON> hinz<PERSON><PERSON><PERSON>", "addTrigger": "<PERSON><PERSON> hi<PERSON>", "triggerConfig": "Trigger-Konfiguration", "scheduledTrigger": "<PERSON><PERSON><PERSON><PERSON>", "scheduledTriggerDescription": "<PERSON>en Trigger planen, der zu einer bestimmten Zeit ausgeführt wird", "eventBasedTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventBasedTriggerDescription": "<PERSON>en <PERSON> erstellen, der bei einem Ereignis ausgeführt wird", "noChatsFound": "<PERSON><PERSON> gefunden", "search": "<PERSON><PERSON>", "searchPlaceholder": "Suchen...", "searchResults": "Suchergebnisse", "recent": "<PERSON><PERSON><PERSON><PERSON>", "personalAccount": "Persönliches Konto", "workspaces": "Arbeitsbereiche", "theme": "Design", "integrations": "Integrationen", "billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usage": "<PERSON><PERSON>ung", "adminPanel": "Admin-<PERSON><PERSON><PERSON>", "apiKeys": "API-Schlüssel", "envManager": "Umgebungs-Manager", "localEnvManager": "Lokaler .Env-Manager", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plan": "Plan", "knowledgeBase": "Wissensdatenbank", "conversation": "Unterhaltung", "conversations": "Unterhaltungen", "noConversations": "<PERSON><PERSON> keine <PERSON>", "startNewChat": "Starte einen neuen Chat", "openInNewTab": "In neuem Tab <PERSON>", "delete": "Löschen", "deleting": "Löschen", "allThreadsLoaded": "Alle Threads geladen", "myWorkforce": "Meine Belegschaft", "addTeam": "Team hinzufügen", "selectAll": "Alle auswählen", "deselectAll": "Auswahl aufheben", "selectAllConversations": "Alle Unterhaltungen auswählen", "deselectAllConversations": "Auswahl aller Unterhaltungen aufheben", "loadMore": "<PERSON><PERSON> <PERSON>", "remaining": "verbleibend"}, "dashboard": {"title": "Dashboard", "welcome": "<PERSON><PERSON><PERSON><PERSON>", "whatWouldYouLike": "Was möchtest du erledigen?", "describeWhatYouNeed": "<PERSON><PERSON><PERSON><PERSON>, wobei du Hilfe benötigst...", "usageLimits": "Nutzungslimits", "threads": "Threads", "customWorkers": "Benutzerdefinierte Worker", "scheduledTriggers": "<PERSON><PERSON><PERSON>", "appTriggers": "App-<PERSON><PERSON>", "limitsExceeded": "Du hast dein Chat-<PERSON>it erreicht ({current}/{limit}). Upgraden deinen Plan, um mehr Chats zu erstellen.", "agentInitiatedSuccessfully": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gestartet", "aiAssistantReady": "<PERSON><PERSON>-Assistent ist bereit zu helfen"}, "thread": {"integrations": "Integrationen", "connect": "Verbinden", "seeAllIntegrations": "+ Alle Integrationen anzeigen", "connectIntegrations": "Integrationen verbinden", "adaptive": "Adaptiv", "adaptiveDescription": "Schnelle Antworten mit intelligentem Kontextwechsel", "autonomous": "Autonom", "autonomousDescription": "Tiefenarbeitsmodus für mehrstufige Problemlösung", "chat": "Cha<PERSON>", "chatDescription": "Einfache Unterhaltung", "sampleAnswers": "Beispielantworten", "samplePrompts": "Beispiel-Prompts", "waitingForUserResponse": "Kortix wird nach deiner Antwort eigenständig weiterarbeiten.", "taskCompleted": "Aufgabe abgeschlossen", "uploadingFiles": "Lade {count} Datei{plural} hoch...", "uploadingFilesOne": "Lade {count} <PERSON>i hoch...", "uploadingFilesMany": "Lade {count} <PERSON><PERSON> hoch...", "scrollToBottom": "Nach unten scrollen", "tools": "Werkzeuge", "instructions": "Anweisungen", "knowledge": "Wissen", "triggers": "<PERSON><PERSON>", "agents": "<PERSON><PERSON>", "superWorker": "Super Worker", "searchWorkers": "Worker suchen...", "myWorkers": "<PERSON>ne <PERSON>", "running": "Läuft", "minimizeToFloatingPreview": "Auf schwebende Vorschau minimieren", "close": "Schließen", "noActionsYet": "Noch keine Aktionen", "workerActionsDescription": "Worker-Aktionen und Ergebnisse werden hier angezeigt, wenn sie ausgeführt werden", "toolIsRunning": "<PERSON><PERSON> l<PERSON>t", "toolCurrentlyExecuting": "{toolName} wird derzeit ausgeführt. Ergebnisse werden hier angezeigt, wenn sie abgeschlossen sind.", "rateThisResult": "<PERSON><PERSON> be<PERSON>ten", "feedbackHelpsImprove": "<PERSON><PERSON> hilft uns, uns zu verbessern", "additionalFeedbackOptional": "Zusätzliches Feedback (optional)", "helpKortixImprove": "<PERSON><PERSON> mit deine<PERSON> Feedback, sich zu verbessern", "submit": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Wird gesendet...", "feedbackSubmittedSuccess": "Feedback erfolgreich übermittelt", "feedbackSubmitFailed": "Feedback konnte nicht übermittelt werden"}, "agents": {"title": "<PERSON><PERSON>", "create": "Agent <PERSON><PERSON><PERSON>", "edit": "Agent bear<PERSON>ten", "delete": "Agent <PERSON><PERSON>", "name": "Name", "description": "Beschreibung"}, "threads": {"title": "Threads", "newThread": "<PERSON><PERSON><PERSON>", "sendMessage": "Nachricht senden", "placeholder": "Gib deine Nachricht ein..."}, "billing": {"title": "Abrechnung & Abonnement", "subscription": "Abonnement", "credits": "Credits", "popular": "Beliebt", "creditsExplained": "Credits erklärt", "creditsExhaustedRefreshIn": "Ihre Credits sind aufgebraucht. Sie werden in {time} erneuert", "trialActive": "Testversion aktiv", "trialBadge": "7-<PERSON><PERSON>-<PERSON><PERSON>", "currentPlan": "Aktueller Plan", "currentBadge": "Aktuell", "scheduled": "<PERSON><PERSON><PERSON>", "scheduledBadge": "<PERSON><PERSON><PERSON>", "loading": "Lädt...", "subscriptionChangeScheduled": "Abonnementänderung geplant", "planWillChangeOn": "<PERSON><PERSON> sich am {date}.", "failedToInitiateSubscription": "Abonnement konnte nicht gestartet werden. Versuch's nochmal.", "subscriptionUpgraded": "Abonnement von {currentPrice} auf {newPrice} aktualisiert", "subscriptionUpdated": "Abonnement erfolgreich aktualisiert", "cannotDowngradeDuringCommitment": "Während der Verpflichtungsperiode kann nicht herabgestuft werden", "alreadyOnThisPlan": "Du bist bereits auf diesem Plan.", "localModeMessage": "Läuft im lokalen Entwicklungsmodus - Abrechnungsfunktionen sind deaktiviert", "creditsExplainedPage": {"title": "Credits erklärt", "subtitle": "<PERSON><PERSON>, was du über die Funktionsweise von Credits bei Kortix wissen musst", "understandingCredits": {"title": "<PERSON> verstehen", "description": "Credits dienen als universelle Währung von Kortix für Plattformoperationen. Jede Aktion, die deine KI-Agenten ausführen – von der Datenanalyse bis zur Codegenerierung – verbraucht Credits basierend auf der Komplexität der Aufgabe und den erforderlichen Ressourcen."}, "howCreditsWork": {"title": "<PERSON><PERSON> <PERSON>", "description": "Credits werden basierend auf den Ressourcen verbraucht, die deine KI-Agenten verwenden:", "aiModelUsage": {"title": "KI-Modellnutzung", "description": "Der Haupttreiber des Credit-Verbrauchs", "content": "Verschiedene KI-Modelle haben unterschiedliche Kosten basierend auf ihren Fähigkeiten und der Token-Nutzung. Credits werden für Eingabe-Tokens (deine Prompts und Kontext), Ausgabe-Tokens (Agent-Antworten) verbraucht und variieren je nach Modell-Tier (Claude, GPT usw.)."}, "pricingModel": {"title": "<PERSON>ismodell", "description": "20% Aufschlag auf KI-Modellkosten", "content": "Wir erheben einen Aufschlag von 20% auf alle API- und Modellkosten, um die Plattforminfrastruktur, Sicherheit und laufende Entwicklung zu decken. Diese transparente Preisgestaltung stellt sicher, dass du genau weißt, wofür du bezahlst."}}, "gettingMoreCredits": {"title": "<PERSON><PERSON> <PERSON>", "description": "<PERSON>s gibt mehrere Möglichkeiten, Credits bei Kortix zu erhalten:", "monthlySubscription": {"title": "Monatliche Abonnement-Credits", "description": "Im Rahmen deines kostenpflichtigen Plans enthalten und jeden Monat automatisch erneuert. Dies sind ablaufende Credits."}, "topUpCredits": {"title": "Auflade-Credits", "description": "<PERSON><PERSON><PERSON> zusätzliche Credits, wenn du sie benötigst. Diese laufen nicht ab und sind für Premium-Mitglieder verfügbar."}, "promotionalGrants": {"title": "Werbe- und Event-Gutschriften", "description": "Bonus-Credits von besonderen Veranstaltungen, Werbeaktionen oder Empfehlungen. Diese laufen nicht ab."}, "refunds": {"title": "Rückerstattungen", "description": "Credits, die aufgrund technischer Probleme oder fehlgeschlagener Aufgaben zurückgegeben wurden. Diese laufen nicht ab."}}, "typesOfCredits": {"title": "<PERSON><PERSON>", "description": "Kortix verwendet zwei Arten von Credits, um dir Flexibilität bei der Verwaltung deiner Nutzung zu geben:", "expiringCredits": {"title": "Ablaufende Credits", "description": "Monatliche Abonnement-Credits", "content": "Diese Credits sind in deinem kostenpflichtigen Abonnement enthalten und werden jeden Monat automatisch an deinem Abonnementdatum erneuert. Sie laufen am Ende jedes Abrechnungszyklus ab und werden immer zuerst verbraucht, bevor ablauffreie Credits verwendet werden."}, "nonExpiringCredits": {"title": "Nicht ablaufende Credits", "description": "Permanente Credits, die nie ablaufen", "content": "Diese Credits laufen nie ab und werden von Monat zu Monat übertragen. <PERSON>e umfassen Aufladekäufe, Rückerstattungen und Werbegutschriften. Nicht ablaufende Credits werden nur verwendet, nachdem deine ablaufenden Credits aufgebraucht sind."}, "creditPriority": {"title": "Credit-Priorität:", "description": "Wenn du Kortix verwendest, werden zuerst ablaufende Credits verbraucht. Erst nachdem deine ablaufenden Credits aufgebraucht sind, werden nicht ablaufende Credits verwendet."}}}, "close": "Schließen", "cancelScheduledChange": "Geplante Änderung abbrechen", "threadUsage": "Thread-<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "totalUsage": "Gesamtnutzung", "usage": "<PERSON><PERSON>ung", "thread": "<PERSON><PERSON><PERSON>", "lastUsed": "Zuletzt verwendet", "creditsUsed": "Verwendete Credits", "creditTransactions": "Credit-Transaktionen", "loadingTransactionHistory": "Lade Transaktionsverlauf...", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON><PERSON>", "discount": "15% Ra<PERSON><PERSON>", "perMonth": "/Monat", "billedYearly": "j<PERSON><PERSON><PERSON> abgerechnet", "downgradePending": "Downgrade ausstehend", "downgrade": "Herabstufen", "switchToLegacyYearly": "Zu Legacy-Jahresplan wechseln", "pickPlan": "<PERSON><PERSON><PERSON><PERSON> den Plan, der zu dir passt.", "reachedLimit": "Du hast deine Limits erreicht.", "projectLimit": "Projektlimit ({current}/{limit})", "threadLimit": "Thread-Limit ({current}/{limit})", "workerLimit": "Worker-Limit ({current}/{limit})", "triggerLimit": "Trigger-Limit ({current}/{limit})", "getAdditionalCredits": "Zusätzliche Credits erhalten", "selectPlan": "Plan auswählen", "changeScheduled": "<PERSON><PERSON><PERSON>", "notAvailable": "Nicht verfügbar", "features": {"creditsPerMonth": "{count} Credits/Monat", "customWorker": "{count} ben<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Worker", "customWorkers": "{count} benutzerd<PERSON>inierte Worker", "privateProject": "{count} privates <PERSON><PERSON><PERSON>", "privateProjects": "Private Projekte", "customTrigger": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "integrations": "100+ <PERSON>en", "premiumAIModels": "Premium-KI-Modelle", "prioritySupport": "Prioritätssupport"}}, "auth": {"logIntoAccount": "In dein Konto einloggen", "createAccount": "<PERSON><PERSON> er<PERSON>", "signInOrCreateAccount": "Melde dich an oder erstelle dein Kortix-Konto", "continueWithGoogle": "Mit Google fortfahren", "continueWithGitHub": "<PERSON><PERSON>", "orEmail": "oder E-Mail", "emailAddress": "E-Mail-Adresse", "password": "Passwort", "confirmPassword": "Passwort bestätigen", "signIn": "Anmelden", "signUp": "Registrieren", "signingIn": "Wird angemeldet...", "creatingAccount": "Konto wird erstellt...", "forgotPassword": "Passwort vergessen?", "dontHaveAccount": "Noch keinen Account? Registrieren", "alreadyHaveAccount": "Schon einen Account? Anmelden", "acceptPrivacyTerms": "Ich stimme den <privacyPolicy>Datenschutzerklärung</privacyPolicy> und den <termsOfService>Nutzungsbedingungen</termsOfService> zu", "privacyPolicy": "Datenschutzerklärung", "termsOfService": "Nutzungsbedingungen", "checkYourEmail": "Check deine E-Mails", "confirmationLinkSent": "Wir haben dir einen Bestätigungslink geschickt an:", "clickLinkToActivate": "<PERSON>licke auf den Link in der E-Mail, um dein Konto zu aktivieren. Wenn du die E-Mail nicht siehst, check deinen Spam-Ordner.", "returnToHome": "Zur Startseite zurückkehren", "backToSignIn": "<PERSON><PERSON><PERSON> zum Login", "resetPassword": "Passwort zurücksetzen", "resetPasswordDescription": "Gib deine E-Mail-Adresse ein und wir schicken dir einen Link zum Zurücksetzen", "sendResetLink": "<PERSON> senden", "cancel": "Abbrechen", "pleaseEnterValidEmail": "<PERSON>te gib eine gültige E-Mail-Adresse ein", "signInFailed": "Login fehlgeschlagen. Check deine Zugangsdaten.", "signUpFailed": "Registrierung fehlgeschlagen. Versuch's nochmal.", "sendMagicLink": "Magic Link senden", "sending": "Wird gesendet...", "magicLinkSent": "Wir haben einen Magic Link gesendet an", "magicLinkDescription": "<PERSON><PERSON>e auf den Link in deiner E-Mail, um dich anzumelden. Der Link läuft in 1 Stunde ab.", "magicLinkExplanation": "Wir senden dir einen sicheren Link zum Anmelden. Kein Passwort erford<PERSON>lich.", "byContinuingYouAgree": "Durch Fortfahren stimmst du unserer <privacyPolicy>Datenschutzerklärung</privacyPolicy> und unseren <termsOfService>Nutzungsbedingungen</termsOfService> zu"}, "onboarding": {"welcome": "<PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON>", "skip": "Überspringen"}, "tools": {"executeCommand": "Befehl ausführen", "checkCommandOutput": "Befehlsausgabe prüfen", "terminateCommand": "<PERSON><PERSON><PERSON>den", "listCommands": "<PERSON><PERSON><PERSON><PERSON> auflisten", "createFile": "<PERSON><PERSON> erstellen", "deleteFile": "<PERSON><PERSON>", "fullFileRewrite": "<PERSON><PERSON>n", "strReplace": "Text bearbeiten", "editFile": "<PERSON><PERSON> bearbeiten", "uploadFile": "<PERSON><PERSON> ho<PERSON>n", "createDocument": "Dokument erstellen", "updateDocument": "Dokument aktualisieren", "readDocument": "Do<PERSON><PERSON> lesen", "listDocuments": "Dokumente auflisten", "deleteDocument": "Dokument löschen", "createTasks": "Aufgaben erstellen", "updateTasks": "Aufgaben aktualisieren", "browserNavigateTo": "Zu Seite navigieren", "browserAct": "Aktion ausführen", "browserExtractContent": "Inhalt extrahieren", "browserScreenshot": "Screenshot erstellen", "executeDataProviderCall": "Datenanbieter aufrufen", "getDataProviderEndpoints": "Endpunkte abrufen", "ask": "Fragen", "wait": "<PERSON><PERSON>", "complete": "Aufgabe abschließen", "crawlWebpage": "Website durchsuchen", "exposePort": "Port freigeben", "scrapeWebpage": "Website scrapen", "webSearch": "Web durchsuchen", "loadImage": "Bild laden", "createPresentationOutline": "Präsentationsgliederung erstellen", "createPresentation": "Präsentation erstellen", "presentPresentation": "Präsentieren", "clearImagesFromContext": "Bilder aus Kontext löschen", "imageSearch": "<PERSON><PERSON>d suchen", "createSheet": "<PERSON><PERSON><PERSON>", "updateSheet": "Tabelle aktualisieren", "viewSheet": "<PERSON><PERSON><PERSON> anzeigen", "analyzeSheet": "Tabelle analysieren", "visualizeSheet": "Tabelle visualisieren", "formatSheet": "Tabelle formatieren", "updateAgent": "Agent aktualisieren", "getCurrentAgentConfig": "Agent-Konfiguration abrufen", "searchMcpServers": "MCP-Server suchen", "getMcpServerTools": "MCP-Server-Tools abrufen", "configureMcpServer": "MCP-Server konfigurieren", "getPopularMcpServers": "Beliebte MCP-Server abrufen", "testMcpServerConnection": "MCP-Server-Verbindung testen", "listAppEventTriggers": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> finden", "createEventTrigger": "<PERSON><PERSON>ignis<PERSON><PERSON><PERSON> er<PERSON>", "getProjectStructure": "Projektstruktur abrufen", "buildProject": "Projekt erstellen", "makePhoneCall": "<PERSON><PERSON><PERSON>", "endCall": "<PERSON><PERSON><PERSON>", "getCallDetails": "Anrufdetails abrufen", "listCalls": "<PERSON><PERSON><PERSON> auflisten", "monitorCall": "<PERSON><PERSON><PERSON>", "waitForCallCompletion": "<PERSON><PERSON> warten", "getPaperDetails": "Papierdetails abrufen", "searchAuthors": "<PERSON><PERSON>en", "getAuthorDetails": "Autorendetails abrufen", "getAuthorPapers": "Autorenpapiere abrufen", "getPaperCitations": "Papierzitate abrufen", "getPaperReferences": "Papierreferenzen abrufen", "paperSearch": "<PERSON><PERSON>en", "discoverUserMcpServers": "<PERSON>ls entdecken", "createCredentialProfile": "<PERSON><PERSON>", "getCredentialProfiles": "Profile abrufen", "configureProfileForAgent": "Tools zum Agenten hinzufügen", "createNewAgent": "Neuen Agenten erstellen", "searchMcpServersForAgent": "MCP-Server suchen", "createCredentialProfileForAgent": "Anmeldedatenprofil erstellen", "discoverMcpToolsForAgent": "MCP-<PERSON><PERSON> entdecken", "configureAgentIntegration": "Agenten-Integration konfigurieren", "createAgentScheduledTrigger": "Geplanten Trigger erstellen", "listAgentScheduledTriggers": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> auflisten", "executingTool": "<PERSON>l ausführen", "toolExecutedSuccessfully": "<PERSON><PERSON> erfolgreich ausgeführt", "toolExecutionFailed": "Tool-Ausführung fehlgeschlagen", "input": "Eingabe", "output": "Ausgabe", "copyFileContent": "Dateiinhalt kopieren", "fileContentCopied": "Dateiinhalt in die Zwischenablage kopiert", "failedToCopyFileContent": "Dateiinhalt konnte nicht kopiert werden", "noContentAvailable": "<PERSON><PERSON> Inhalt verfügbar", "noContentDescription": "Diese <PERSON>l-Ausführung hat keinen Eingabe- oder Ausgabeinhalt zum Anzeigen erzeugt.", "tool": "Tool"}, "agentRunLimit": {"gotIt": "Verstanden", "parallelRunsLimitReached": "Limit für parallele Ausführungen erreicht", "parallelRunsLimitDescription": "Du hast die maximale Anzahl paralleler Agenten-Ausführungen erreicht.", "needMoreParallelRuns": "Mehr parallele Ausführungen benötigt?", "upgradeDescription": "Upgraden deinen Plan, um mehrere Agenten gleichzeitig auszuführen und deine Produktivität zu steigern.", "upgradePlan": "Plan upgraden", "currentlyRunningAgents": "Aktuell laufende Agenten", "loadingThreads": "Threads werden geladen...", "foundRunningAgents": "{count} lauf<PERSON>e Agenten gefunden, aber Thread-Details konnten nicht geladen werden.", "threadIds": "Thread-IDs: {ids}", "moreRunning": "+{count} weitere laufen", "whatCanYouDo": "Was kannst du tun?", "stopAgentsHint": "<PERSON>licke auf die Stopp-Schaltfläche, um laufende Agenten zu stoppen", "waitForCompletion": "<PERSON><PERSON>, bis ein Agent automatisch abgeschlossen ist", "upgradeYourPlan": "Upgraden deinen Plan", "forMoreParallelRuns": "für mehr parallele Ausführungen", "stopAgent": "Agent stoppen", "stopThisAgent": "Diesen Agenten stoppen", "openThread": "<PERSON><PERSON><PERSON>", "agentStoppedSuccessfully": "Agent er<PERSON><PERSON><PERSON><PERSON><PERSON> gestoppt", "failedToStopAgent": "Agent kon<PERSON> nicht gestop<PERSON> werden", "upgradeToRunMore": "Upgraden, um mehr Agenten parallel auszuführen"}, "errors": {"generic": "Ein Fehler ist aufgetreten", "network": "Netzwerkfehler", "unauthorized": "Nicht autorisiert", "notFound": "Nicht gefunden"}, "home": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Loslegen"}, "showcase": {"title": "Kortix: <PERSON><PERSON> autonomer KI-Worker", "subtitle": "Gebaut für komplexe Aufgaben, entwickelt für alles. Der ultimative KI-Assistent, der alles bewältigt—von einfachen Anfragen bis hin zu mega-komplexen Projekten.", "tryItOut": "Ausprobieren", "kortixComputer": "Kortix Computer", "running": "Läuft", "workers": {"images": {"title": "Bilder", "description": "<PERSON><PERSON>elle Bilder nach Bedarf. Von Produktfotos über Social-Media-Grafiken bis hin zu vollständigen Illustrationen. Passt Stil, Beleuchtung, Farben und Layout an oder verfeinert bestehende Visuals mit schnellen Bearbeitungen und Retuschen.", "capabilities": {"0": "Produktfotos generieren", "1": "Social-Media-Graf<PERSON><PERSON> er<PERSON>", "2": "Illustrationen erstellen", "3": "Stil- & Beleuchtungsvarianten", "4": "Logo- / Asset-Erstellung", "5": "+ <PERSON><PERSON><PERSON> mehr"}, "imageAlt": "Wachstum ist nicht linear Grafik-Beispiel", "fileType": "image"}, "slides": {"title": "Präsentationen", "description": "<PERSON>rst<PERSON> auf Anhieb beeindruckende Präsentationen. Von Pitch-Decks über Berichte bis hin zu Schulungsmaterialien. Passt Designs, Layouts, Inhaltsstruktur an oder verfeinert bestehende Decks mit schnellen Bearbeitungen und Updates.", "capabilities": {"0": "Pitch-Decks", "1": "Schulungsmaterial", "2": "Berichtspräsentationen", "3": "Design- & Layoutvarianten", "4": "Inhaltsumstrukturierung", "5": "+ <PERSON><PERSON><PERSON> mehr"}, "imageAlt": "Nexus Enterprise Automation Platform Folien-Beispiel", "fileType": "PPTX"}, "data": {"title": "Daten", "description": "Verwandelt Rohdaten in Erkenntnisse. Von Tabellenkalkulationen über Dashboards bis hin zu Visualisierungen. Bereinigt <PERSON>ätze, erstellt Diagramme, erstellt Berichte oder verfeinert bestehende Analysen mit schnellen Updates.", "capabilities": {"0": "Dashboards", "1": "Visualisierungen", "2": "Datenberichte", "3": "Daten bereinigen & organisieren", "4": "Erkenntnisse generieren", "5": "+ <PERSON><PERSON><PERSON> mehr"}, "imageAlt": "Finanzmodell-Dashboard-Beispiel", "fileType": "Vorschau"}, "docs": {"title": "Dokumente", "description": "Schreibt und bearbeitet Dokumente mühelos. Von Vorschlägen über Anleitungen bis hin zu Inhalten. <PERSON><PERSON> To<PERSON>, Struktur, Formatierung an oder verfeinert bestehende Dokumente mit schnellen Umschreibungen und Polierungen.", "capabilities": {"0": "Vorschläge", "1": "Anleitungen & Handbücher", "2": "Inhalte", "3": "Ton- & Stilvarianten", "4": "Formatieren & umstrukturieren", "5": "+ <PERSON><PERSON><PERSON> mehr"}, "imageAlt": "Q3 2025 Executive Summary Report Beispiel", "fileType": "PDF"}, "research": {"title": "Recherche", "description": "Erforscht Themen umfassend. Von Markttrends über Wettbewerbsanalysen bis hin zu tiefgreifenden Untersuchungen. <PERSON><PERSON><PERSON>, synthetisiert Erkenntnisse oder verfeinert bestehende Recherchen mit schnellen Updates.", "capabilities": {"0": "Markttrends analysieren", "1": "Wettbewerbsrecherche", "2": "Tiefgreifende Themenuntersuchungen", "3": "<PERSON><PERSON> sammeln", "4": "Erkenntnisse synthetisieren", "5": "+ <PERSON><PERSON><PERSON> mehr"}, "imageAlt": "Detaillierte Wettbewerber-Profile Recherche-Beispiel", "fileType": "PDF"}}}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano"}, "suna": {"samplePrompts": "Beispiel-Prompts", "chooseStyle": "<PERSON><PERSON><PERSON><PERSON> einen Stil", "chooseTemplate": "<PERSON><PERSON><PERSON><PERSON> eine Vorlage", "chooseOutputFormat": "Wähle Ausgabeformat", "preferredCharts": "Bevorzugte Diagramme", "whatWouldYouLike": "Was möchtest du erledigen?", "describeTask": "Beschreibe die Aufgabe, die dein Worker erledigen soll...", "modes": {"image": "Bild", "slides": "Folien", "data": "Daten", "docs": "Dokumente", "people": "<PERSON>en", "research": "Recherche"}, "styles": {"photorealistic": "Fotorealistisch", "watercolor": "<PERSON><PERSON><PERSON>", "digital-art": "<PERSON><PERSON> Kunst", "oil-painting": "Ö<PERSON>mä<PERSON>", "minimalist": "Minimalistisch", "isometric": "Isometrisch", "vintage": "Vintage", "comic": "Comic", "neon": "Neon", "pastel": "<PERSON><PERSON>", "geometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "Abstrakt", "anime": "Anime", "impressionist": "Impressionistisch", "surreal": "Surreal"}, "templates": {"minimalist": {"name": "Minimalistisch", "description": "Sauberes und einfaches Design"}, "minimalist_2": {"name": "Minimalistisch 2", "description": "Alternativer minimalistischer Stil"}, "black_and_white_clean": {"name": "Schwarz & Weiß", "description": "Klassisches Monochrom"}, "colorful": {"name": "Bunt", "description": "Lebendig und energisch"}, "startup": {"name": "Startup", "description": "Dynamisch und innovativ"}, "elevator_pitch": {"name": "Elevator Pitch", "description": "Schnell und wirkungsvoll"}, "portfolio": {"name": "Portfolio", "description": "Zeige deine Arbeit"}, "textbook": {"name": "Lehrbuch", "description": "Bildend und strukturiert"}, "architect": {"name": "Architekt", "description": "Professionell und präzise"}, "hipster": {"name": "<PERSON><PERSON>", "description": "Modern und trendy"}, "green": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Naturinspiriertes Design"}, "premium_black": {"name": "Premium Schwarz", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> dunkles <PERSON>"}, "premium_green": {"name": "Premium Grün", "description": "Raffiniertes Grün"}, "professor_gray": {"name": "Professor <PERSON><PERSON><PERSON>", "description": "Akademisch und gelehrt"}, "gamer_gray": {"name": "<PERSON><PERSON>", "description": "Gaming-inspiriertes Design"}, "competitor_analysis_blue": {"name": "<PERSON><PERSON><PERSON>", "description": "Fokus auf Geschäftsanalyse"}, "numbers_clean": {"name": "<PERSON><PERSON><PERSON>", "description": "Saubere Datenvisualisierung"}, "numbers_colorful": {"name": "<PERSON><PERSON><PERSON>", "description": "Lebendige Datenpräsentation"}, "prd": {"name": "PRD", "description": "Produktanforderungsdokument"}, "technical": {"name": "Technisch", "description": "Technische Dokumentation"}, "proposal": {"name": "Vorschlag", "description": "Geschäftsvorschlag"}, "report": {"name": "Bericht", "description": "Detailliertes Berichtsformat"}, "guide": {"name": "Anleitung", "description": "Schritt-für-Schritt-Anleitung"}, "wiki": {"name": "Wiki", "description": "Wissensbasis-Artikel"}, "policy": {"name": "Rich<PERSON><PERSON><PERSON>", "description": "Richtliniendokument"}, "meeting-notes": {"name": "Besprechungsnotizen", "description": "Besprechungsprotokoll"}}, "outputFormats": {"spreadsheet": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> mit Formeln"}, "dashboard": {"name": "Dashboard", "description": "Interaktive Diagramme"}, "report": {"name": "Bericht", "description": "Analyse mit Visualisierungen"}, "slides": {"name": "Folien", "description": "Präsentationsformat"}}, "charts": {"bar": "<PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON>", "pie": "Kreis", "scatter": "Streudiagramm", "heatmap": "Heatmap", "bubble": "Blase", "wordcloud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stacked": "Gestapelte Balken", "area": "Fläche"}, "prompts": {"image": {"0": "Ein majestätischer goldener Adler, der durch neblige Berggipfel bei Sonnenaufgang mit dramatischer Beleuchtung schwebt", "1": "Nahaufnahme-Porträt eines Models mit avantgardistischem Make-up, Studio-Beleuchtung und hohen Kontrastschatten", "2": "Gemütliches skandinavisches Wohnzimmer mit natürlichen Holzmöbeln, Zimmerpflanzen und sanftem Morgenlicht", "3": "Futuristischer Cyberpunk-Straßenmarkt bei Nacht mit Neon-Schildern, regennassem Pflaster und holografischen Displays", "4": "Elegante Produktfotografie einer Luxus-Parfümflasche auf Marmoroberfläche mit sanften Reflexionen", "5": "Verspielte schwebende Inseln, verbunden durch Seilbrücken in einem pastellfarbenen Himmel mit verträumten Wolken", "6": "Makro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Morgentautropfen auf lebendigen Blütenblättern mit Bokeh-Hintergrund", "7": "Modernes Arbeitsplatz-Schreibtisch-Setup mit Laptop, <PERSON><PERSON><PERSON>, Notizbuch und Sukkulenten von oben", "8": "Mystischer Waldpfad mit uralten Bäumen, leuchtenden Glühwürmchen und ätherischen Lichtstrahlen durch Nebel", "9": "Architektonisches Detail einer zeitgenössischen Glasfassade mit geometrischen Mustern und Reflexionen", "10": "Lebendiger Street-Food-Verkaufsstand mit bunten Zutaten, aufsteigendem Dampf und warmem Licht", "11": "R<PERSON>iger japanischer Zen-Garten mit geharktem Sand, moosbedeckten Steinen und Kirschblütenblättern", "12": "Dynamische Actionaufnahme eines Athleten mitten im Sprung gegen dramatischen Sonnenuntergangshimmel, Silhouetten-Effekt", "13": "<PERSON><PERSON><PERSON><PERSON>kü<PERSON> mit Kupfertöpfen, f<PERSON><PERSON>, Holzschneidebrettern und natürlichen Texturen", "14": "Abstrakte Fluid-Kunst mit wirbelnden metallischen Gold-, Tiefblau- und Smaragdgrün-Organischen Mustern"}, "slides": {"0": "<PERSON><PERSON><PERSON> ein Series-A-Pitch-Deck mit Marktgröße, Traktion und Finanzprognosen", "1": "E<PERSON>elle eine Q4-Geschäftsübersicht mit KPIs, Erfolgen und strategischen Initiativen", "2": "Gestalte eine Produkt-Launch-Präsentation mit Demo-Videos und Kundenstimmen", "3": "Entwickle ein Sales-Enablement-Deck, das unseren Wertversprechen und Wettbewerbsvorteile erklärt", "4": "<PERSON><PERSON><PERSON> ein Investor-Update mit wichtigen Metriken und bevorstehenden Meilensteinen", "5": "Erstelle eine Kunden-Fallstudien-Präsentation mit ROI und Erfolgsmetriken", "6": "Gestalte eine All-Hands-Präsentation mit Unternehmens-Updates und Vision", "7": "Entwickle ein Schulungs-Deck für neue Produktfunktionen und Workflows", "8": "<PERSON><PERSON><PERSON> einen Konferenz-Vortrag über die Skalierung von Engineering-Teams", "9": "<PERSON><PERSON><PERSON> eine Vorstands-Präsentation mit strategischen Empfehlungen"}, "data": {"0": "<PERSON><PERSON><PERSON> ein <PERSON>dell, das ARR-Wachstum mit verschiedenen Preisszenarien projiziert", "1": "<PERSON><PERSON><PERSON> ein interaktives Sales-Dashboard, das Metriken nach Region und Quartal verfolgt", "2": "Analysiere 50K Kundenbewertungen und visualisiere Stimmungstrends über die Zeit", "3": "Gestalte einen Content-Kalender, der Kampagnen mit ROI- und Engagement-Diagrammen verfolgt", "4": "<PERSON><PERSON><PERSON> eine Kohortenanalyse, die Nutzerbindung und Abwanderungsmuster zeigt", "5": "Erstelle ein Marketing-Attributionsmodell, das Kanalleistung vergleicht", "6": "Entwickle einen Recruiting-Tracker mit Pipeline-Metriken und Time-to-Fill-Analyse", "7": "<PERSON><PERSON><PERSON> eine Budgetplanungs-Tabelle mit Szenario-Modellierung", "8": "Analysiere Website-Traffic-Daten und visualisiere Conversion-Funnels", "9": "<PERSON><PERSON><PERSON> ein Inventarverwaltungssystem mit automatischen Nachbestellungsalarmen"}, "docs": {"0": "Schreibe ein umfassendes PRD für eine KI-gestützte Empfehlungsmaschine", "1": "Erstelle ein technisches Architekturdokument für eine skalierbare Microservices-Plattform", "2": "<PERSON><PERSON><PERSON> ein Go-to-Market-Strategiedokument für unseren Q2-Produkt-Launch", "3": "Entwickle ein 90-Tag<PERSON>-Onboarding-Playbook für Engineering-Manager", "4": "Schreibe einen API-Dokumentationsleitfaden mit Beispielen und Best Practices", "5": "<PERSON><PERSON><PERSON> ein Unternehmenshandbuch mit Kultur, Richtlinien und Vorteilen", "6": "<PERSON>rst<PERSON> eine Datenschutzrichtlinie, die GDPR und CCPA entspricht", "7": "Entwickle ein Customer-Success-Playbook für SaaS-Enterprise-Konten", "8": "Schreibe einen Sicherheitsvorfall-Reaktionsplan mit Eskalationsverfahren", "9": "<PERSON><PERSON><PERSON> einen umfassenden Styleguide für Marke und Content"}, "people": {"0": "Finde VP of Engineering-Kandidaten bei Series B+ KI/ML-Startups im San Francisco Bay Area mit 10+ Jahren Erfahrung und bewährter Erfolgsbilanz beim Skalieren von Engineering-Teams", "1": "<PERSON><PERSON><PERSON> eine Lead-List<PERSON> von CMOs bei B2B-SaaS-Unternehmen (10-50 Mio. $ ARR), die kürzlich Series A/B-Finanzierung erhalten haben - inklusive E-Mail-Mustern und Tech-Stack", "2": "Recherchiere Senior Blockchain Engineers mit Solidity/Rust-Erfahrung bei Top-Krypto-Projekten, offen für Umzug nach Dubai oder Singapur", "3": "Generiere eine Prospect-Liste von technischen Gründern bei Seed-Series A-Startups im Enterprise-KI-Bereich, die in den letzten 6 Monaten 2-15 Mio. $ erhalten haben", "4": "Identifiziere Senior Product Manager bei Fintech-Unternehmen mit 5-10 J<PERSON>ren Erfahrung von FAANG oder Unicorns, vers<PERSON><PERSON> in 0-1-Produktentwicklung", "5": "Finde CIOs und VP Engineering bei mittelständischen Healthcare-IT-Unternehmen (500-5000 Mitarbeiter) mit 500K+ $ IT-Budgets, die Cloud-Migration planen", "6": "Recherchiere VP Sales bei B2B-SaaS-Unternehmen mit 100%+ YoY-<PERSON><PERSON><PERSON><PERSON>, mit 7+ J<PERSON>ren Erfahrung im Abschluss von 100K+ $ Deals und PLG-Erfahrung", "7": "<PERSON>rstelle eine Liste von CTOs bei Enterprise-Unternehmen, die aktiv KI-Infrastruktur mit Multi-Millionen-Dollar-Budgets in 2024 implementieren", "8": "Finde Senior UX/UI Designer mit Mobile-First Consumer-App-Erfahrung und 1M+ Nutzer-Portfolios, aktiv suchend oder offen für Möglichkeiten", "9": "Identifiziere Senior DevOps Engineers bei Cloud-Native-Startups mit Kubernetes/Terraform-Expertise und 5-8 J<PERSON>ren Erfahrung beim Aufbau von Infrastruktur für 10M+ <PERSON><PERSON>er"}, "research": {"0": "Analysiere aufkommende Trends im Quantencomputing und potenzielle Geschäftsanwendungen", "1": "Recherchiere die Top 10 Wettbewerber im KI-gestützten CRM-Bereich mit Feature-Vergleich", "2": "Untersuche regulatorische Anforderungen für den Launch einer Fintech-App in der EU", "3": "<PERSON>rst<PERSON> eine Marktanalyse über Elektrofahrzeug-Adoptionsraten in wichtigen Märkten", "4": "Studiere die Auswirkungen von Remote-Arbeit auf die Nachfrage nach Gewerbeimmobilien in Großstädten", "5": "Recherchiere Web3-Adoptionsmuster unter Fortune-500-Unternehmen", "6": "Analysiere das Verbraucherstimmungsbild gegenüber nachhaltigen Modemarken", "7": "Untersuche die neuesten Entwicklungen in der Gentherapie für seltene Krankheiten", "8": "Studiere Preisstrategien erfolgreicher D2C-Abonnementbox-Unternehmen", "9": "Recherchiere das Wettbewerbsumfeld KI-gestützter Cybersicherheitslösungen"}}}}