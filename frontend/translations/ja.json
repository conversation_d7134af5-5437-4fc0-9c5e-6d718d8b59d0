{"common": {"cancel": "キャンセル", "save": "保存", "saving": "保存中...", "success": "成功", "done": "完了", "close": "閉じる", "ok": "OK", "yes": "はい", "no": "いいえ", "delete": "削除", "edit": "編集", "search": "検索", "loading": "読み込み中...", "error": "エラー", "retry": "もう一度", "send": "送信", "back": "戻る", "new": "新規", "create": "作成", "update": "更新", "remove": "削除", "confirm": "確認", "processing": "処理中...", "keep": "保持", "active": "有効", "inactive": "無効", "next": "次へ", "upgrade": "アップグレード", "basic": "ベーシック", "tryFree": "始める"}, "settings": {"title": "設定", "tabs": {"general": "一般", "plan": "プラン", "billing": "請求", "usage": "使用状況", "knowledgeBase": "ナレッジベース", "integrations": "統合", "envManager": "環境マネージャー"}, "general": {"title": "プロフィール設定", "description": "アカウント情報を管理", "name": "名前", "email": "メール", "namePlaceholder": "名前を入力", "emailCannotChange": "メールはここから変更できません", "saveChanges": "変更を保存", "profileUpdated": "プロフィールが正常に更新されました", "profileUpdateFailed": "プロフィールの更新に失敗しました", "language": {"title": "言語", "description": "希望の言語を選択", "current": "現在の言語", "select": "言語を選択"}, "deleteAccount": {"title": "アカウント削除", "description": "アカウントとすべての関連データを永久に削除", "button": "アカウント削除", "scheduled": "削除予定", "scheduledDescription": "アカウントは {date} に永久に削除されます。", "canCancel": "削除日より前にいつでもこのリクエストをキャンセルできます。", "cancelButton": "削除リクエストをキャンセル", "dialogTitle": "アカウント削除", "warning": "この操作は30日後には元に戻せません", "whenDelete": "アカウントを削除すると：", "agentsDeleted": "すべてのエージェントとエージェントバージョンが削除されます", "threadsDeleted": "すべてのスレッドと会話が削除されます", "credentialsRemoved": "すべての認証情報と統合が削除されます", "subscriptionCancelled": "サブスクリプションがキャンセルされます", "billingRemoved": "すべての請求データが削除されます", "scheduled30Days": "アカウントは30日後に削除予定となります", "gracePeriod": "30日の猶予期間中はいつでもこのリクエストをキャンセルできます。30日後、すべてのデータが永久に削除され、復元できません。", "confirmText": "削除と入力して確認", "confirmPlaceholder": "delete", "keepAccount": "アカウントを保持", "cancelDeletionTitle": "アカウント削除をキャンセル", "cancelDeletionDescription": "アカウントの削除をキャンセルしますか？アカウントとすべてのデータが保持されます。", "cancelDeletion": "削除をキャンセル"}}, "billing": {"title": "請求とサブスクリプション", "description": "クレジットとサブスクリプションを管理", "insufficientCredits": "クレジット不足", "insufficientCreditsMessage": "請求チェックに失敗しました：クレジット不足。残高は {balance} です。続行するにはクレジットを追加してください。", "creditsExhausted": "クレジットがなくなりました。今すぐアップグレードしてください。", "billingCheckFailed": "請求チェックに失敗しました。続行するにはアップグレードしてください。", "failedToStartAgent": "エージェント {agentName} の起動に失敗しました：{message}"}, "usage": {"title": "使用状況"}, "knowledgeBase": {"opening": "ナレッジベースを開いています", "redirecting": "ナレッジベースページにリダイレクト中..."}, "integrations": "連携"}, "triggers": {"getStarted": "トリガーを追加して始める", "getStartedDescription": "トリガーをスケジュールしてアクションを自動化し、完了時にリマインダーを受け取る。", "search": "検索", "scheduledTrigger": "スケジュールトリガー", "eventBasedTrigger": "イベントベースのトリガー", "chooseAgent": "エージェントを選択", "schedule": "スケジュール", "agentInstructions": "エージェントの指示", "agentInstructionsDescription": "エージェント用のカスタムプロンプト", "assignedAgent": "割り当てられたエージェント", "unknownAgent": "不明なエージェント", "technicalDetails": "技術詳細", "type": "タイプ", "provider": "プロバイダー", "created": "作成日時", "lastUpdated": "最終更新", "enable": "有効化", "disable": "無効化", "deleteTask": "タスクを削除", "deleteTaskDescription": "\"{name}\" を削除してもよろしいですか？この操作は元に戻せず、このタスクのすべての自動実行が停止されます。", "taskEnabled": "タスクが有効化されました", "taskDisabled": "タスクが無効化されました", "toggleTaskFailed": "タスクの切り替えに失敗しました", "taskDeletedSuccess": "タスクが正常に削除されました", "deleteTaskFailed": "タスクの削除に失敗しました", "taskUpdatedSuccess": "タスクが正常に更新されました", "updateTaskFailed": "タスクの更新に失敗しました", "pleaseSelectAgent": "エージェントを選択してください", "defaultScheduledTriggerName": "スケジュールトリガー", "defaultScheduledTriggerDescription": "自動スケジュールトリガー", "scheduleTriggerUpdated": "スケジュールトリガーが正常に更新されました", "scheduleTriggerCreated": "スケジュールトリガーが正常に作成されました", "upgradeForMoreScheduledTriggers": "より多くのスケジュールトリガーを作成するにはアップグレード", "upgradeForMoreAppTriggers": "より多くのアプリベースのトリガーを作成するにはアップグレード", "updateScheduleTriggerFailed": "スケジュールトリガーの更新に失敗しました", "createScheduleTriggerFailed": "スケジュールトリガーの作成に失敗しました", "editScheduledTask": "スケジュールタスクを編集", "createScheduledTask": "スケジュールタスクを作成", "editAppBasedTask": "アプリベースのタスクを編集", "createAppBasedTask": "アプリベースのタスクを作成", "updateAgentForTask": "このタスクのエージェントを更新", "selectAgentForTask": "まず、このタスクを処理するエージェントを選択", "createNew": "新規作成", "notConfigured": "未設定", "every15Minutes": "15分ごと", "every30Minutes": "30分ごと", "everyHour": "1時間ごと", "dailyAt9AM": "毎日午前9時", "weekdaysAt9AM": "平日午前9時", "weeklyOnMonday": "毎週月曜日", "monthlyOn1st": "毎月1日"}, "sidebar": {"chats": "チャット", "agents": "エージェント", "workers": "ワーカー", "triggers": "トリガー", "newChat": "新しいチャット", "newAgent": "新しいエージェント", "settings": "設定", "logout": "ログアウト", "openMenu": "メニューを開く", "expandSidebar": "サイドバーを展開 (CMD+B)", "searchChats": "チャットを検索", "searchChatsPlaceholder": "チャットを検索...", "allTriggers": "すべてのトリガー", "addWorkers": "Workersを追加", "addTrigger": "トリガーを追加", "triggerConfig": "トリガー設定", "scheduledTrigger": "スケジュールトリガー", "scheduledTriggerDescription": "特定の時間にトリガーを実行", "eventBasedTrigger": "イベントベースのトリガー", "eventBasedTriggerDescription": "イベント発生時に実行されるトリガーを作成", "noChatsFound": "チャットが見つかりません", "noConversations": "まだチャットなし", "search": "検索", "searchPlaceholder": "検索...", "searchResults": "検索結果", "recent": "最近", "personalAccount": "個人アカウント", "workspaces": "ワークスペース", "theme": "テーマ", "integrations": "統合", "billing": "請求", "usage": "使用状況", "adminPanel": "管理パネル", "apiKeys": "APIキー", "envManager": "環境マネージャー", "advanced": "高度", "plan": "プラン", "knowledgeBase": "ナレッジベース", "conversation": "会話", "conversations": "会話", "startNewChat": "新しいチャットを開始", "openInNewTab": "新しいタブで開く", "delete": "削除", "deleting": "削除中", "allThreadsLoaded": "すべてのスレッドが読み込まれました", "myWorkforce": "マイワークフォース", "addTeam": "チームを追加", "selectAll": "すべて選択", "deselectAll": "すべて解除", "selectAllConversations": "すべての会話を選択", "deselectAllConversations": "すべての会話を解除", "loadMore": "もっと読み込む", "remaining": "残り"}, "dashboard": {"title": "ダッシュボード", "welcome": "ようこそ", "whatWouldYouLike": "何をやりたい？", "describeWhatYouNeed": "何のヘルプが必要か説明して...", "usageLimits": "使用制限", "threads": "スレッド", "customWorkers": "カスタム Workers", "scheduledTriggers": "スケジュールトリガー", "appTriggers": "アプリトリガー", "limitsExceeded": "チャットの制限に達しました（{current}/{limit}）。プランをアップグレードしてもっとチャットを作成しよう。", "agentInitiatedSuccessfully": "エージェントが正常に開始されました", "aiAssistantReady": "AIアシスタントがヘルプの準備ができています"}, "thread": {"integrations": "統合", "connect": "接続", "seeAllIntegrations": "+ すべての統合を見る", "connectIntegrations": "統合を接続", "adaptive": "適応型", "adaptiveDescription": "スマートなコンテキスト切り替えによる迅速な応答", "autonomous": "自律型", "autonomousDescription": "多段階の問題解決のための深い作業モード", "chat": "チャット", "chatDescription": "シンプルな往復会話", "sampleAnswers": "サンプル回答", "samplePrompts": "サンプルプロンプト", "waitingForUserResponse": "あなたの回答後、Kortixは自律的に作業を続けます。", "taskCompleted": "タスク完了", "uploadingFiles": "{count} 個のファイルをアップロード中...", "uploadingFilesOne": "{count} 個のファイルをアップロード中...", "uploadingFilesMany": "{count} 個のファイルをアップロード中...", "scrollToBottom": "一番下にスクロール", "tools": "ツール", "instructions": "指示", "knowledge": "知識", "triggers": "トリガー", "agents": "エージェント", "superWorker": "Super Worker", "searchWorkers": "Workerを検索...", "myWorkers": "マイW<PERSON><PERSON>", "running": "実行中", "minimizeToFloatingPreview": "フローティングプレビューに最小化", "close": "閉じる", "noActionsYet": "まだアクションなし", "workerActionsDescription": "Workerのアクションと結果は実行時にここに表示されます", "toolIsRunning": "ツールが実行中です", "toolCurrentlyExecuting": "{toolName} が現在実行中です。完了すると結果がここに表示されます。", "rateThisResult": "この結果を評価", "feedbackHelpsImprove": "フィードバックは改善に役立ちます", "additionalFeedbackOptional": "追加のフィードバック（任意）", "helpKortixImprove": "フィードバックでKortixの改善にご協力ください", "submit": "送信", "submitting": "送信中...", "feedbackSubmittedSuccess": "フィードバックが正常に送信されました", "feedbackSubmitFailed": "フィードバックの送信に失敗しました"}, "agents": {"title": "エージェント", "create": "エージェントを作成", "createNewWorker": "新しいWorkerを作成", "edit": "エージェントを編集", "delete": "エージェントを削除", "name": "名前", "description": "説明", "model": "モデル", "systemPrompt": "システムプロンプト", "systemPromptPlaceholder": "エージェントの動作を定義...", "customizeIcon": "エージェントアイコンをカスタマイズするにはクリック"}, "home": {"heroPlaceholder": "Workerに完了させたいタスクを説明..."}, "showcase": {"title": "Kortix: あなたの自律型AIワーカー", "subtitle": "複雑なタスクのために構築され、すべてのために設計されました。シンプルなリクエストからメガ複雑なプロジェクトまで、すべてを処理する究極のAIアシスタント。", "tryItOut": "試してみる", "kortixComputer": "Kortix Computer", "running": "実行中", "workers": {"images": {"title": "画像", "description": "オンデマンドで画像を作成。製品写真からソーシャルグラフィック、完全なイラストまで。スタイル、照明、色、レイアウトを調整したり、既存のビジュアルを迅速な編集とタッチアップで洗練させます。", "capabilities": {"0": "製品写真を生成", "1": "ソーシャルグラフィックを作成", "2": "イラストを作成", "3": "スタイルと照明のバリエーション", "4": "ロゴ/アセット作成", "5": "+ その他多数"}, "imageAlt": "成長は線形ではないグラフィック例", "fileType": "image"}, "slides": {"title": "スライド", "description": "即座に素晴らしいプレゼンテーションを作成。ピッチデッキからレポート、トレーニング資料まで。テーマ、レイアウト、コンテンツ構造を調整したり、既存のデッキを迅速な編集と更新で洗練させます。", "capabilities": {"0": "ピッチデッキ", "1": "トレーニング資料", "2": "レポートプレゼンテーション", "3": "テーマとレイアウトのバリエーション", "4": "コンテンツの再構築", "5": "+ その他多数"}, "imageAlt": "Nexus Enterprise Automation Platformスライド例", "fileType": "PPTX"}, "data": {"title": "データ", "description": "生データをインサイトに変換。スプレッドシートからダッシュボード、ビジュアライゼーションまで。データセットをクリーンアップし、チャートを作成し、レポートを構築したり、既存の分析を迅速な更新で洗練させます。", "capabilities": {"0": "ダッシュボード", "1": "ビジュアライゼーション", "2": "データレポート", "3": "データのクリーンアップと整理", "4": "インサイトを生成", "5": "+ その他多数"}, "imageAlt": "財務モデルダッシュボード例", "fileType": "プレビュー"}, "docs": {"title": "ドキュメント", "description": "ドキュメントを簡単に作成・編集。提案からガイド、コンテンツまで。トーン、構造、フォーマットを調整したり、既存のドキュメントを迅速な書き直しと磨きで洗練させます。", "capabilities": {"0": "提案", "1": "ガイドとマニュアル", "2": "コンテンツ", "3": "トーンとスタイルのバリエーション", "4": "フォーマットと再構築", "5": "+ その他多数"}, "imageAlt": "2025年第3四半期エグゼクティブサマリーレポート例", "fileType": "PDF"}, "research": {"title": "リサーチ", "description": "トピックを包括的に調査。市場トレンドから競合分析、深い調査まで。ソースを収集し、調査結果を統合したり、既存のリサーチを迅速な更新で洗練させます。", "capabilities": {"0": "市場トレンドを分析", "1": "競合リサーチ", "2": "トピックの深い調査", "3": "ソースを収集", "4": "調査結果を統合", "5": "+ その他多数"}, "imageAlt": "詳細な競合プロフィールリサーチ例", "fileType": "PDF"}}}, "threads": {"title": "スレッド", "newThread": "新しいスレッド", "sendMessage": "メッセージを送信", "placeholder": "メッセージを入力..."}, "billing": {"title": "請求とサブスクリプション", "subscription": "サブスクリプション", "credits": "クレジット", "popular": "人気", "creditsExplained": "クレジットの説明", "creditsExhaustedRefreshIn": "クレジットを使い果たしました。{time}後に更新されます", "trialActive": "トライアル有効", "trialBadge": "7日間トライアル", "currentPlan": "現在のプラン", "currentBadge": "現在", "scheduled": "スケジュール済み", "scheduledBadge": "スケジュール済み", "loading": "請求情報を読み込み中...", "subscriptionChangeScheduled": "サブスクリプション変更がスケジュールされました", "planWillChangeOn": "プランは {date} に変更されます。", "failedToInitiateSubscription": "サブスクリプションの開始に失敗しました。もう一度お試しください。", "subscriptionUpgraded": "サブスクリプションが {currentPrice} から {newPrice} にアップグレードされました", "subscriptionUpdated": "サブスクリプションが正常に更新されました", "cannotDowngradeDuringCommitment": "コミットメント期間中はダウングレードできません", "alreadyOnThisPlan": "既にこのプランにいます。", "localModeMessage": "ローカル開発モードで実行中 - 請求機能は無効です", "creditsExplainedPage": {"title": "クレジットの説明", "subtitle": "Kortixでのクレジットの仕組みについて知っておくべきすべて", "understandingCredits": {"title": "クレジットの理解", "description": "クレジットは、Kortixのプラットフォーム操作における共通通貨として機能します。AIエージェントが実行するすべてのアクション（データ分析からコード生成まで）は、タスクの複雑さと必要なリソースに基づいてクレジットを消費します。"}, "howCreditsWork": {"title": "クレジットの仕組み", "description": "クレジットは、AIエージェントが使用するリソースに基づいて消費されます：", "aiModelUsage": {"title": "AIモデルの使用", "description": "クレジット消費の主要な要因", "content": "異なるAIモデルは、その機能とトークン使用量に基づいて異なるコストがあります。クレジットは入力トークン（プロンプトとコンテキスト）、出力トークン（エージェントの応答）に消費され、モデル階層（Claude、GPTなど）によって異なります。"}, "pricingModel": {"title": "価格モデル", "description": "AIモデルコストに20%のマークアップ", "content": "プラットフォームのインフラストラクチャ、セキュリティ、継続的な開発をカバーするために、すべてのAPIとモデルコストに20%のマークアップを適用します。この透明な価格設定により、何に支払っているかが正確にわかります。"}}, "gettingMoreCredits": {"title": "より多くのクレジットを取得", "description": "Kortixでクレジットを取得する方法はいくつかあります：", "monthlySubscription": {"title": "月額サブスクリプションクレジット", "description": "有料プランに含まれ、毎月自動的に更新されます。これらは期限付きクレジットです。"}, "topUpCredits": {"title": "トップアップクレジット", "description": "必要なときに追加のクレジットを購入できます。これらは期限なしで、プレミアムメンバーが利用できます。"}, "promotionalGrants": {"title": "プロモーション＆イベント付与", "description": "特別なイベント、プロモーション、または紹介からのボーナスクレジット。これらは期限なしです。"}, "refunds": {"title": "返金", "description": "技術的な問題や失敗したタスクにより返金されたクレジット。これらは期限なしです。"}}, "typesOfCredits": {"title": "クレジットの種類", "description": "Kortixは2種類のクレジットを使用して、使用管理の柔軟性を提供します：", "expiringCredits": {"title": "期限付きクレジット", "description": "月額サブスクリプションクレジット", "content": "これらのクレジットは有料サブスクリプションに含まれ、サブスクリプション日付に毎月自動的に更新されます。各請求サイクルの終了時に期限切れになり、期限なしクレジットの前に常に最初に消費されます。"}, "nonExpiringCredits": {"title": "期限なしクレジット", "description": "永続的な期限なしクレジット", "content": "これらのクレジットは期限切れにならず、月をまたいで繰り越されます。トップアップ購入、返金、プロモーション付与が含まれます。期限なしクレジットは、期限付きクレジットが使い果たされた後にのみ使用されます。"}, "creditPriority": {"title": "クレジット優先順位：", "description": "Kortixを使用する場合、期限付きクレジットが最初に消費されます。期限付きクレジットが使い果たされた後にのみ、期限なしクレジットが使用されます。"}}}, "close": "閉じる", "cancelScheduledChange": "スケジュール変更をキャンセル", "threadUsage": "スレッド使用", "error": "エラー", "totalUsage": "総使用量", "usage": "使用状況", "thread": "スレッド", "lastUsed": "最終使用", "creditsUsed": "使用クレジット", "creditTransactions": "クレジット取引", "loadingTransactionHistory": "取引履歴を読み込み中...", "monthly": "月額", "yearly": "年額", "discount": "15%オフ", "perMonth": "/月", "billedYearly": "年額請求", "downgradePending": "ダウングレード保留中", "downgrade": "ダウングレード", "switchToLegacyYearly": "レガシー年額に切り替え", "pickPlan": "自分に合ったプランを選んで。", "reachedLimit": "制限に達しました。", "projectLimit": "プロジェクト制限 ({current}/{limit})", "threadLimit": "スレッド制限 ({current}/{limit})", "workerLimit": "Worker制限 ({current}/{limit})", "triggerLimit": "トリガー制限 ({current}/{limit})", "getAdditionalCredits": "追加クレジットを取得", "selectPlan": "プランを選択", "changeScheduled": "変更スケジュール済み", "notAvailable": "利用不可", "features": {"creditsPerMonth": "{count} クレジット/月", "customWorker": "{count} カスタムWorker", "customWorkers": "{count} カスタムWorkers", "privateProject": "{count} プライベートプロジェクト", "privateProjects": "プライベートプロジェクト", "customTrigger": "{count} カスタムトリガー", "integrations": "100+ 統合", "premiumAIModels": "プレミアムAIモデル", "prioritySupport": "優先サポート"}}, "auth": {"logIntoAccount": "アカウントにログイン", "createAccount": "アカウントを作成", "signInOrCreateAccount": "Kortixアカウントにログインまたは作成", "continueWithGoogle": "Googleで続ける", "continueWithGitHub": "GitHubで続ける", "orEmail": "またはメール", "emailAddress": "メールアドレス", "password": "パスワード", "confirmPassword": "パスワードを確認", "signIn": "ログイン", "signUp": "登録", "signingIn": "ログイン中...", "creatingAccount": "アカウント作成中...", "forgotPassword": "パスワードを忘れた", "dontHaveAccount": "アカウントない？", "alreadyHaveAccount": "既にアカウントある？", "acceptPrivacyTerms": "<privacyPolicy>プライバシーポリシー</privacyPolicy>と<termsOfService>利用規約</termsOfService>に同意します", "privacyPolicy": "プライバシーポリシー", "termsOfService": "利用規約", "checkYourEmail": "メールをチェック", "confirmationLinkSent": "リセットリンクを送信しました", "clickLinkToActivate": "メール内のリンクをクリックしてアカウントを有効化してください。メールが表示されない場合は、スパムフォルダを確認してください。", "returnToHome": "ホームに戻る", "backToSignIn": "ログインに戻る", "resetPassword": "パスワードリセット", "resetPasswordDescription": "メールアドレスを入力すると、リセットリンクを送信します", "sendResetLink": "リンクを送信", "cancel": "キャンセル", "pleaseEnterValidEmail": "有効なメールアドレスを入力してください", "signInFailed": "ログイン失敗。認証情報を確認してください。", "signUpFailed": "登録に失敗しました。もう一度お試しください。", "sendMagicLink": "マジックリンクを送信", "sending": "送信中...", "magicLinkSent": "マジックリンクを送信しました", "magicLinkDescription": "メール内のリンクをクリックしてログインしてください。リンクは1時間で期限切れになります。", "magicLinkExplanation": "安全なリンクをメールでお送りします。パスワードは不要です。", "byContinuingYouAgree": "続行することで、<privacyPolicy>プライバシーポリシー</privacyPolicy>および<termsOfService>利用規約</termsOfService>に同意したものとみなされます"}, "onboarding": {"welcome": "ようこそ", "next": "次へ", "skip": "スキップ"}, "tools": {"executeCommand": "コマンド実行中", "checkCommandOutput": "コマンド出力確認中", "terminateCommand": "コマンド終了中", "listCommands": "コマンド一覧取得中", "createFile": "ファイル作成中", "deleteFile": "ファイル削除中", "fullFileRewrite": "ファイル書き換え中", "strReplace": "テキスト編集中", "editFile": "ファイル編集中", "uploadFile": "ファイルアップロード中", "createDocument": "ドキュメント作成中", "updateDocument": "ドキュメント更新中", "readDocument": "ドキュメント読み取り中", "listDocuments": "ドキュメント一覧取得中", "deleteDocument": "ドキュメント削除中", "createTasks": "タスク作成中", "updateTasks": "タスク更新中", "browserNavigateTo": "ページ遷移中", "browserAct": "アクション実行中", "browserExtractContent": "コンテンツ抽出中", "browserScreenshot": "スクリーンショット撮影中", "executeDataProviderCall": "データプロバイダー呼び出し中", "getDataProviderEndpoints": "エンドポイント取得中", "ask": "質問", "wait": "待機", "complete": "タスク完了中", "crawlWebpage": "ウェブサイトクロール中", "exposePort": "ポート公開中", "scrapeWebpage": "ウェブページスクレイピング中", "webSearch": "ウェブ検索中", "loadImage": "画像読み込み中", "createPresentationOutline": "プレゼンアウトライン作成中", "createPresentation": "プレゼン作成中", "presentPresentation": "プレゼン中", "clearImagesFromContext": "コンテキストから画像をクリア中", "imageSearch": "画像検索中", "createSheet": "シート作成中", "updateSheet": "シート更新中", "viewSheet": "シート表示中", "analyzeSheet": "シート分析中", "visualizeSheet": "シート可視化中", "formatSheet": "シートフォーマット中", "updateAgent": "エージェント更新中", "getCurrentAgentConfig": "エージェント設定取得中", "searchMcpServers": "MCPサーバー検索中", "getMcpServerTools": "MCPサーバーツール取得中", "configureMcpServer": "MCPサーバー設定中", "getPopularMcpServers": "人気MCPサーバー取得中", "testMcpServerConnection": "MCPサーバー接続テスト中", "listAppEventTriggers": "イベントトリガー検索中", "createEventTrigger": "イベントトリガー作成中", "getProjectStructure": "プロジェクト構造取得中", "buildProject": "プロジェクトビルド中", "makePhoneCall": "電話発信中", "endCall": "通話終了中", "getCallDetails": "通話詳細取得中", "listCalls": "通話一覧取得中", "monitorCall": "通話監視中", "waitForCallCompletion": "完了待機中", "getPaperDetails": "論文詳細取得中", "searchAuthors": "著者検索中", "getAuthorDetails": "著者詳細取得中", "getAuthorPapers": "著者論文取得中", "getPaperCitations": "論文引用取得中", "getPaperReferences": "論文参考文献取得中", "paperSearch": "論文検索中", "discoverUserMcpServers": "ツール検出中", "createCredentialProfile": "プロファイル作成中", "getCredentialProfiles": "プロファイル取得中", "configureProfileForAgent": "エージェントにツール追加中", "createNewAgent": "新しいエージェント作成中", "searchMcpServersForAgent": "MCPサーバー検索中", "createCredentialProfileForAgent": "認証プロファイル作成中", "discoverMcpToolsForAgent": "MCPツール検出中", "configureAgentIntegration": "エージェント統合設定中", "createAgentScheduledTrigger": "スケジュールトリガー作成中", "listAgentScheduledTriggers": "エージェントスケジュールトリガー一覧取得中", "executingTool": "ツール実行中", "toolExecutedSuccessfully": "ツールが正常に実行されました", "toolExecutionFailed": "ツールの実行に失敗しました", "input": "入力", "output": "出力", "copyFileContent": "ファイル内容をコピー", "fileContentCopied": "ファイル内容がクリップボードにコピーされました", "failedToCopyFileContent": "ファイル内容のコピーに失敗しました", "noContentAvailable": "コンテンツがありません", "noContentDescription": "このツール実行では、表示する入力または出力コンテンツが生成されませんでした。", "tool": "ツール"}, "agentRunLimit": {"gotIt": "了解", "parallelRunsLimitReached": "並列実行制限に達しました", "parallelRunsLimitDescription": "許可されている最大並列エージェント実行数に達しました。", "needMoreParallelRuns": "もっと並列実行が必要？", "upgradeDescription": "プランをアップグレードして、複数のエージェントを同時に実行し、生産性を向上させよう。", "upgradePlan": "プランをアップグレード", "currentlyRunningAgents": "現在実行中のエージェント", "loadingThreads": "スレッドを読み込み中...", "foundRunningAgents": "{count} 個の実行中のエージェントが見つかりましたが、スレッドの詳細を読み込めませんでした。", "threadIds": "スレッドID: {ids}", "moreRunning": "+{count} 個が実行中", "whatCanYouDo": "何ができる？", "stopAgentsHint": "停止ボタンをクリックして実行中のエージェントを停止", "waitForCompletion": "エージェントが自動的に完了するのを待つ", "upgradeYourPlan": "プランをアップグレード", "forMoreParallelRuns": "より多くの並列実行のために", "stopAgent": "エージェントを停止", "stopThisAgent": "このエージェントを停止", "openThread": "スレッドを開く", "agentStoppedSuccessfully": "エージェントが正常に停止されました", "failedToStopAgent": "エージェントの停止に失敗しました", "upgradeToRunMore": "より多くのエージェントを並列実行するためにアップグレード"}, "errors": {"generic": "エラーが発生しました", "network": "ネットワークエラー", "unauthorized": "認証されていません", "notFound": "見つかりません"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano", "zh": "中文", "ja": "日本語", "pt": "Português", "fr": "Français", "es": "Español"}, "suna": {"samplePrompts": "サンプルプロンプト", "chooseStyle": "スタイルを選択", "chooseTemplate": "テンプレートを選択", "chooseOutputFormat": "出力形式を選択", "preferredCharts": "推奨チャート", "whatWouldYouLike": "何をやりたい？", "describeTask": "Workerに完了させたいタスクを説明...", "modes": {"image": "画像", "slides": "スライド", "data": "データ", "docs": "ドキュメント", "people": "人", "research": "リサーチ"}, "styles": {"photorealistic": "写実", "watercolor": "水彩", "digital-art": "デジタルアート", "oil-painting": "油絵", "minimalist": "ミニマル", "isometric": "アイソメ", "vintage": "ヴィンテージ", "comic": "コミック", "neon": "ネオン", "pastel": "パステル", "geometric": "幾何学", "abstract": "抽象", "anime": "アニメ", "impressionist": "印象派", "surreal": "シュール"}, "templates": {"minimalist": {"name": "ミニマル", "description": "クリーンでシンプルなデザイン"}, "minimalist_2": {"name": "ミニマル2", "description": "代替ミニマルスタイル"}, "black_and_white_clean": {"name": "白黒", "description": "クラシックモノクロ"}, "colorful": {"name": "カラフル", "description": "活気に満ちた"}, "startup": {"name": "スタートアップ", "description": "ダイナミックで革新的"}, "elevator_pitch": {"name": "エレベーターピッチ", "description": "迅速で影響力のある"}, "portfolio": {"name": "ポートフォリオ", "description": "作品を紹介"}, "textbook": {"name": "教科書", "description": "教育的で構造化された"}, "architect": {"name": "アーキテクト", "description": "プロフェッショナルで正確"}, "hipster": {"name": "ヒップスター", "description": "モダンでトレンディ"}, "green": {"name": "グリーン", "description": "自然にインスパイアされたデザイン"}, "premium_black": {"name": "プレミアムブラック", "description": "高級ダークテーマ"}, "premium_green": {"name": "プレミアムグリーン", "description": "洗練されたグリーン"}, "professor_gray": {"name": "プロフェッサーグレー", "description": "学術的"}, "gamer_gray": {"name": "ゲーマーグレー", "description": "ゲームにインスパイアされたデザイン"}, "competitor_analysis_blue": {"name": "分析ブルー", "description": "ビジネス分析に焦点"}, "numbers_clean": {"name": "ナンバーズクリーン", "description": "クリーンなデータ可視化"}, "numbers_colorful": {"name": "ナンバーズカラフル", "description": "活気のあるデータプレゼンテーション"}, "prd": {"name": "PRD", "description": "製品要件ドキュメント"}, "technical": {"name": "テクニカル", "description": "技術ドキュメント"}, "proposal": {"name": "提案書", "description": "ビジネス提案"}, "report": {"name": "レポート", "description": "詳細なレポート形式"}, "guide": {"name": "ガイド", "description": "ステップバイステップガイド"}, "wiki": {"name": "ウィキ", "description": "ナレッジベース記事"}, "policy": {"name": "ポリシー", "description": "ポリシードキュメント"}, "meeting-notes": {"name": "議事録", "description": "議事録"}}, "outputFormats": {"spreadsheet": {"name": "スプレッドシート", "description": "数式付きテーブル"}, "dashboard": {"name": "ダッシュボード", "description": "インタラクティブチャート"}, "report": {"name": "レポート", "description": "ビジュアル付き分析"}, "slides": {"name": "スライド", "description": "プレゼンテーション形式"}}, "charts": {"bar": "棒グラフ", "line": "折れ線グラフ", "pie": "円グラフ", "scatter": "散布図", "heatmap": "ヒートマップ", "bubble": "バブルチャート", "wordcloud": "ワードクラウド", "stacked": "積み上げ棒グラフ", "area": "エリアチャート"}, "prompts": {"image": {"0": "日の出の霧に包まれた山々を背景に、劇的な光の中で雄大に舞う金色のワシ", "1": "アバンギャルドなメイクとスタジオ照明、高コントラストの影を持つファッションモデルのクローズアップポートレート", "2": "自然な木製家具、観葉植物、柔らかな朝の陽光に包まれた居心地の良いスカンジナビアのリビングルーム", "3": "ネオンサイン、雨に濡れた舗装、ホログラフィックディスプレイが並ぶ未来派サイバーパンクの夜のストリートマーケット", "4": "大理石の表面に柔らかな反射を映す高級香水ボトルのエレガントな商品写真", "5": "パステルカラーの空に夢のような雲が浮かび、ロープ橋で繋がれた幻想的な浮遊島", "6": "ボケた背景に鮮やかな花びらの朝露のマクロクローズアップ", "7": "上から見たノートパソコン、コーヒー、ノート、多肉植物が並ぶモダンなワークスペースのデスクセットアップ", "8": "古代の木々、光るホタル、霧を通して差し込む幻想的な光の束がある神秘的な森の小道", "9": "幾何学的なパターンと反射を持つ現代的なガラス建築のファサードの建築ディテール", "10": "カラフルな食材、立ち上る湯気、温かい照明がある活気ある屋台のストリートフード", "11": "砂をかいた庭、苔に覆われた石、桜の花びらがある静かな日本の禅庭園", "12": "劇的な夕焼けの空を背景に、ジャンプ中のアスリートの動的なアクションショット、シルエット効果", "13": "銅の鍋、新鮮なハーブ、木製のまな板、自然な質感がある素朴な農家のキッチン", "14": "渦巻くメタリックゴールド、深い青、エメラルドグリーンの有機的なパターンを持つ抽象的な流動アート"}, "slides": {"0": "市場規模、トラクション、財務予測を含むシリーズAピッチデッキを作成", "1": "KPI、成果、戦略的イニシアチブを紹介するQ4ビジネスレビューを構築", "2": "デモ動画と顧客の証言を含む製品ローンチプレゼンテーションをデザイン", "3": "価値提案と競争優位性を説明するセールスエナブルメントデッキを開発", "4": "主要指標と今後のマイルストーンを強調する投資家向けアップデートを作成", "5": "ROIと成功指標を示す顧客ケーススタディプレゼンテーションを構築", "6": "会社のアップデートとビジョンをカバーするオールハンズプレゼンテーションをデザイン", "7": "新製品機能とワークフローのトレーニングデッキを開発", "8": "エンジニアリングチームのスケーリングに関するカンファレンストークを作成", "9": "戦略的推奨事項を含む取締役会プレゼンテーションを構築"}, "data": {"0": "異なる価格シナリオでARR成長を予測する財務モデルを構築", "1": "地域と四半期ごとのメトリクスを追跡するインタラクティブなセールスダッシュボードを作成", "2": "5万件の顧客レビューを分析し、時間の経過に伴う感情トレンドを可視化", "3": "ROIとエンゲージメントチャートでキャンペーンを追跡するコンテンツカレンダーをデザイン", "4": "ユーザーリテンションとチャーンパターンを示すコホート分析を構築", "5": "チャネルパフォーマンスを比較するマーケティングアトリビューションモデルを作成", "6": "パイプラインメトリクスと埋め込み時間分析を含む採用トラッカーを開発", "7": "シナリオモデリングを含む予算計画スプレッドシートを構築", "8": "ウェブサイトトラフィックデータを分析し、コンバージョンファネルを可視化", "9": "自動再注文アラート付きの在庫管理システムを作成"}, "docs": {"0": "AI搭載レコメンデーションエンジンの包括的なPRDを作成", "1": "スケーラブルなマイクロサービスプラットフォームの技術アーキテクチャドキュメントを起草", "2": "Q2製品ローンチのためのGo-to-Market戦略ドキュメントを作成", "3": "エンジニアリングマネージャー向けの90日間オンボーディングプレイブックを開発", "4": "例とベストプラクティスを含むAPIドキュメントガイドを作成", "5": "文化、ポリシー、福利厚生をカバーする会社ハンドブックを作成", "6": "GDPRとCCPAに準拠したデータプライバシーポリシーを起草", "7": "SaaSエンタープライズアカウント向けのカスタマーサクセスプレイブックを開発", "8": "エスカレーション手順を含むセキュリティインシデント対応計画を作成", "9": "ブランドとコンテンツの包括的なスタイルガイドを作成"}, "people": {"0": "サンフランシスコベイエリアのシリーズB以上のAI/MLスタートアップで、10年以上の経験とエンジニアリングチームのスケーリング実績があるエンジニアリングVP候補を探す", "1": "最近シリーズA/B資金調達を行ったB2B SaaS企業（ARR $10M-$50M）のCMOのリードリストを構築 - メールパターンとテックスタックを含む", "2": "トップ暗号プロジェクトでSolidity/Rust経験を持つシニアブロックチェーンエンジニアを調査、ドバイまたはシンガポールへの転勤可能", "3": "過去6ヶ月間に$2M-$15Mを調達したエンタープライズAIのシード-シリーズAスタートアップの技術創業者の見込みリストを生成", "4": "FAANGまたはユニコーン出身で5-10年の経験があり、0-1製品開発に精通したフィンテック企業のシニアプロダクトマネージャーを特定", "5": "$500K以上のIT予算でクラウド移行を計画している中規模ヘルスケアIT企業（500-5000人）のCIOとエンジニアリングVPを探す", "6": "前年比100%以上の成長を示すB2B SaaS企業で、$100K以上の取引を7年以上クロージングし、PLG経験があるセールスVPを調査", "7": "2024年に数百万ドルの予算でAIインフラを積極的に実装しているエンタープライズ企業のCTOリストを構築", "8": "モバイルファーストのコンシューマーアプリ経験と100万以上のユーザーポートフォリオを持つシニアUX/UIデザイナーを探す、積極的に探しているか機会にオープン", "9": "Kubernetes/Terraformの専門知識を持ち、1000万以上のユーザー向けインフラを構築した5-8年の経験があるクラウドネイティブスタートアップのシニアDevOpsエンジニアを特定"}, "research": {"0": "量子コンピューティングの新興トレンドと潜在的なビジネスアプリケーションを分析", "1": "AI搭載CRM分野のトップ10競合他社を機能比較で調査", "2": "EUでフィンテックアプリをローンチするための規制要件を調査", "3": "主要市場での電気自動車採用率に関する市場分析をまとめる", "4": "主要都市での商業用不動産需要に対するリモートワークの影響を研究", "5": "フォーチュン500企業間でのWeb3採用パターンを調査", "6": "持続可能なファッションブランドに対する消費者の感情を分析", "7": "希少疾患の遺伝子治療における最新の進展を調査", "8": "成功したD2Cサブスクリプションボックス企業の価格戦略を研究", "9": "AI搭載サイバーセキュリティソリューションの競争環境を調査"}}}}