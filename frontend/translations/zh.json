{"common": {"cancel": "取消", "save": "保存", "saving": "保存中...", "success": "成功", "done": "完成", "close": "关闭", "ok": "好的", "yes": "是", "no": "否", "delete": "删除", "edit": "编辑", "search": "搜索", "loading": "加载中...", "error": "出错了", "retry": "重试", "send": "发送", "back": "返回", "new": "新建", "create": "创建", "update": "更新", "remove": "移除", "confirm": "确认", "processing": "处理中...", "keep": "保留", "active": "激活", "inactive": "未激活", "next": "下一步", "upgrade": "升级", "basic": "基础版", "tryFree": "开始"}, "settings": {"title": "设置", "tabs": {"general": "常规", "plan": "计划", "billing": "账单", "usage": "使用情况", "knowledgeBase": "知识库", "integrations": "集成", "envManager": "环境管理器"}, "general": {"title": "个人资料设置", "description": "管理你的账户信息", "name": "姓名", "email": "邮箱", "namePlaceholder": "输入你的姓名", "emailCannotChange": "邮箱无法从此处更改", "saveChanges": "保存更改", "profileUpdated": "个人资料更新成功", "profileUpdateFailed": "更新个人资料失败", "language": {"title": "语言", "description": "选择你偏好的语言", "current": "当前语言", "select": "选择语言"}, "deleteAccount": {"title": "删除账户", "description": "永久删除你的账户和所有相关数据", "button": "删除账户", "scheduled": "已安排删除", "scheduledDescription": "你的账户将在 {date} 永久删除。", "canCancel": "你可以在删除日期之前随时取消此请求。", "cancelButton": "取消删除请求", "dialogTitle": "删除账户", "warning": "此操作在30天后无法撤销", "whenDelete": "当你删除账户时：", "agentsDeleted": "你的所有智能体和智能体版本将被删除", "threadsDeleted": "你的所有线程和对话将被删除", "credentialsRemoved": "你的所有凭据和集成将被移除", "subscriptionCancelled": "你的订阅将被取消", "billingRemoved": "所有账单数据将被移除", "scheduled30Days": "你的账户将在30天后安排删除", "gracePeriod": "你可以在30天宽限期内随时取消此请求。30天后，你的所有数据将被永久删除且无法恢复。", "confirmText": "输入删除以确认", "confirmPlaceholder": "delete", "keepAccount": "保留账户", "cancelDeletionTitle": "取消账户删除", "cancelDeletionDescription": "你确定要取消账户删除吗？你的账户和所有数据将被保留。", "cancelDeletion": "取消删除"}}, "billing": {"title": "账单和订阅", "description": "管理您的积分和订阅", "insufficientCredits": "积分不足", "insufficientCreditsMessage": "账单检查失败：积分不足。您的余额为 {balance}。请添加积分以继续。", "creditsExhausted": "您的积分已用完。立即升级。", "billingCheckFailed": "账单检查失败。请升级以继续。", "failedToStartAgent": "启动智能体 {agentName} 失败：{message}"}, "usage": {"title": "使用情况"}, "knowledgeBase": {"opening": "正在打开知识库", "redirecting": "正在重定向到知识库页面..."}, "integrations": "集成"}, "triggers": {"getStarted": "通过添加触发器开始", "getStartedDescription": "安排触发器以自动化操作，并在完成时获得提醒。", "search": "搜索", "scheduledTrigger": "计划触发器", "eventBasedTrigger": "基于事件的触发器", "chooseAgent": "选择智能体", "schedule": "计划", "agentInstructions": "智能体说明", "agentInstructionsDescription": "智能体的自定义提示", "assignedAgent": "已分配的智能体", "unknownAgent": "未知智能体", "technicalDetails": "技术详情", "type": "类型", "provider": "提供商", "created": "创建时间", "lastUpdated": "最后更新", "enable": "启用", "disable": "禁用", "deleteTask": "删除任务", "deleteTaskDescription": "你确定要删除 \"{name}\" 吗？此操作无法撤销，并将停止此任务的所有自动运行。", "taskEnabled": "任务已启用", "taskDisabled": "任务已禁用", "toggleTaskFailed": "切换任务失败", "taskDeletedSuccess": "任务删除成功", "deleteTaskFailed": "删除任务失败", "taskUpdatedSuccess": "任务更新成功", "updateTaskFailed": "更新任务失败", "pleaseSelectAgent": "请选择一个智能体", "defaultScheduledTriggerName": "计划触发器", "defaultScheduledTriggerDescription": "自动计划的触发器", "scheduleTriggerUpdated": "计划触发器更新成功", "scheduleTriggerCreated": "计划触发器创建成功", "upgradeForMoreScheduledTriggers": "升级以创建更多计划触发器", "upgradeForMoreAppTriggers": "升级以创建更多基于应用的触发器", "updateScheduleTriggerFailed": "更新计划触发器失败", "createScheduleTriggerFailed": "创建计划触发器失败", "editScheduledTask": "编辑计划任务", "createScheduledTask": "创建计划任务", "editAppBasedTask": "编辑基于应用的任务", "createAppBasedTask": "创建基于应用的任务", "updateAgentForTask": "更新此任务的智能体", "selectAgentForTask": "首先，选择哪个智能体应该处理此任务", "createNew": "新建", "notConfigured": "未配置", "every15Minutes": "每15分钟", "every30Minutes": "每30分钟", "everyHour": "每小时", "dailyAt9AM": "每天上午9点", "weekdaysAt9AM": "工作日上午9点", "weeklyOnMonday": "每周一", "monthlyOn1st": "每月1号"}, "sidebar": {"chats": "聊天", "agents": "智能体", "workers": "工作器", "triggers": "触发器", "newChat": "新聊天", "newAgent": "新智能体", "settings": "设置", "logout": "退出登录", "openMenu": "打开菜单", "expandSidebar": "展开侧边栏 (CMD+B)", "searchChats": "搜索聊天", "searchChatsPlaceholder": "搜索聊天...", "allTriggers": "所有触发器", "addWorkers": "添加 Workers", "addTrigger": "添加触发器", "triggerConfig": "触发器配置", "scheduledTrigger": "计划触发器", "scheduledTriggerDescription": "在特定时间运行触发器", "eventBasedTrigger": "基于事件的触发器", "eventBasedTriggerDescription": "创建在事件发生时运行的触发器", "noChatsFound": "未找到聊天", "noConversations": "还没有聊天", "search": "搜索", "searchPlaceholder": "搜索...", "searchResults": "搜索结果", "recent": "最近", "personalAccount": "个人账户", "workspaces": "工作区", "theme": "主题", "integrations": "集成", "billing": "账单", "usage": "使用情况", "adminPanel": "管理面板", "apiKeys": "API 密钥", "envManager": "环境管理器", "advanced": "高级", "plan": "计划", "knowledgeBase": "知识库", "conversation": "对话", "conversations": "对话", "startNewChat": "开始新聊天", "openInNewTab": "在新标签页中打开", "delete": "删除", "deleting": "删除中", "allThreadsLoaded": "所有线程已加载", "myWorkforce": "我的工作团队", "addTeam": "添加团队", "selectAll": "全选", "deselectAll": "取消全选", "selectAllConversations": "全选对话", "deselectAllConversations": "取消全选对话", "loadMore": "加载更多", "remaining": "剩余"}, "dashboard": {"title": "仪表板", "welcome": "欢迎", "whatWouldYouLike": "你想完成什么？", "describeWhatYouNeed": "描述你需要什么帮助...", "usageLimits": "使用限制", "threads": "线程", "customWorkers": "自定义 Workers", "scheduledTriggers": "计划触发器", "appTriggers": "应用触发器", "limitsExceeded": "你已达到聊天限制（{current}/{limit}）。升级你的计划以创建更多聊天。", "agentInitiatedSuccessfully": "智能体启动成功", "aiAssistantReady": "你的 AI 助手已准备好帮助"}, "thread": {"integrations": "集成", "connect": "连接", "seeAllIntegrations": "+ 查看所有集成", "connectIntegrations": "连接集成", "adaptive": "自适应", "adaptiveDescription": "智能上下文切换的快速响应", "autonomous": "自主", "autonomousDescription": "多步骤问题解决的深度工作模式", "chat": "聊天", "chatDescription": "简单的来回对话", "sampleAnswers": "示例答案", "samplePrompts": "示例提示", "waitingForUserResponse": "您回答后，Kortix将继续自主工作。", "taskCompleted": "任务已完成", "uploadingFiles": "正在上传 {count} 个文件...", "uploadingFilesOne": "正在上传 {count} 个文件...", "uploadingFilesMany": "正在上传 {count} 个文件...", "scrollToBottom": "滚动到底部", "tools": "工具", "instructions": "说明", "knowledge": "知识", "triggers": "触发器", "agents": "智能体", "superWorker": "超级 Worker", "searchWorkers": "搜索 workers...", "myWorkers": "我的 Workers", "running": "运行中", "minimizeToFloatingPreview": "最小化到浮动预览", "close": "关闭", "noActionsYet": "还没有操作", "workerActionsDescription": "Worker 操作和结果将在此处显示", "toolIsRunning": "工具正在运行", "toolCurrentlyExecuting": "{tool<PERSON>ame} 正在执行。完成后结果将显示在这里。", "rateThisResult": "评价此结果", "feedbackHelpsImprove": "您的反馈帮助我们改进", "additionalFeedbackOptional": "附加反馈（可选）", "helpKortixImprove": "帮助 Kortix 通过您的反馈改进", "submit": "提交", "submitting": "提交中...", "feedbackSubmittedSuccess": "反馈提交成功", "feedbackSubmitFailed": "反馈提交失败"}, "agents": {"title": "智能体", "create": "创建智能体", "createNewWorker": "创建新 Worker", "edit": "编辑智能体", "delete": "删除智能体", "name": "姓名", "description": "描述", "model": "模型", "systemPrompt": "系统提示", "systemPromptPlaceholder": "定义你的智能体应该如何行为...", "customizeIcon": "点击自定义智能体图标"}, "home": {"heroPlaceholder": "描述你想让你的 Worker 完成的任务..."}, "showcase": {"title": "Kortix：您的自主AI工作器", "subtitle": "为复杂任务而构建，为一切而设计。终极AI助手，处理一切——从简单请求到超级复杂的项目。", "tryItOut": "试试看", "kortixComputer": "Kortix Computer", "running": "运行中", "workers": {"images": {"title": "图像", "description": "按需创建图像。从产品照片到社交媒体图形再到完整插图。调整风格、照明、颜色和布局，或通过快速编辑和修饰来完善现有视觉效果。", "capabilities": {"0": "生成产品照片", "1": "创建社交媒体图形", "2": "制作插图", "3": "风格和照明变化", "4": "徽标/资产创建", "5": "+ 更多"}, "imageAlt": "增长不是线性的图形示例", "fileType": "image"}, "slides": {"title": "幻灯片", "description": "即时创建令人惊叹的演示文稿。从推介演示到报告再到培训材料。调整主题、布局、内容结构，或通过快速编辑和更新来完善现有演示文稿。", "capabilities": {"0": "推介演示", "1": "培训材料", "2": "报告演示", "3": "主题和布局变化", "4": "内容重组", "5": "+ 更多"}, "imageAlt": "Nexus企业自动化平台幻灯片示例", "fileType": "PPTX"}, "data": {"title": "数据", "description": "将原始数据转化为洞察。从电子表格到仪表板再到可视化。清理数据集、创建图表、构建报告，或通过快速更新来完善现有分析。", "capabilities": {"0": "仪表板", "1": "可视化", "2": "数据报告", "3": "清理和组织数据", "4": "生成洞察", "5": "+ 更多"}, "imageAlt": "财务模型仪表板示例", "fileType": "预览"}, "docs": {"title": "文档", "description": "轻松编写和编辑文档。从提案到指南再到内容片段。调整语气、结构、格式，或通过快速重写和润色来完善现有文档。", "capabilities": {"0": "提案", "1": "指南和手册", "2": "内容片段", "3": "语气和风格变化", "4": "格式化和重组", "5": "+ 更多"}, "imageAlt": "2025年第三季度执行摘要报告示例", "fileType": "PDF"}, "research": {"title": "研究", "description": "全面研究主题。从市场趋势到竞争分析再到深入研究。收集来源、综合发现，或通过快速更新来完善现有研究。", "capabilities": {"0": "分析市场趋势", "1": "竞争研究", "2": "深入主题研究", "3": "收集来源", "4": "综合发现", "5": "+ 更多"}, "imageAlt": "详细竞争对手档案研究示例", "fileType": "PDF"}}}, "threads": {"title": "线程", "newThread": "新线程", "sendMessage": "发送消息", "placeholder": "输入你的消息..."}, "billing": {"title": "计费与订阅", "subscription": "订阅", "credits": "积分", "popular": "热门", "creditsExplained": "积分说明", "creditsExhaustedRefreshIn": "您的积分已用完。将在{time}后刷新", "trialActive": "试用激活", "trialBadge": "7天试用", "currentPlan": "当前套餐", "currentBadge": "当前", "scheduled": "已计划", "scheduledBadge": "已计划", "loading": "正在加载账单信息...", "subscriptionChangeScheduled": "订阅更改已计划", "planWillChangeOn": "你的计划将在 {date} 更改。", "failedToInitiateSubscription": "启动订阅失败。请重试。", "subscriptionUpgraded": "订阅已从 {currentPrice} 升级到 {newPrice}", "subscriptionUpdated": "订阅更新成功", "cannotDowngradeDuringCommitment": "在承诺期内无法降级", "alreadyOnThisPlan": "你已在此计划上。", "localModeMessage": "在本地开发模式下运行 - 账单功能已禁用", "creditsExplainedPage": {"title": "积分说明", "subtitle": "关于 Kortix 积分工作原理你需要知道的一切", "understandingCredits": {"title": "了解积分", "description": "积分是 Kortix 平台操作的通用货币。你的 AI 智能体执行的每个操作——从分析数据到生成代码——都会根据任务的复杂性和所需资源消耗积分。"}, "howCreditsWork": {"title": "积分如何工作", "description": "积分根据你的 AI 智能体使用的资源消耗：", "aiModelUsage": {"title": "AI 模型使用", "description": "积分消耗的主要驱动因素", "content": "不同的 AI 模型根据其能力和 token 使用量有不同的成本。积分用于输入 token（你的提示和上下文）、输出 token（智能体响应），并因模型层级（Claude、GPT 等）而异。"}, "pricingModel": {"title": "定价模型", "description": "AI 模型成本加价 20%", "content": "我们对所有 API 和模型成本加价 20%，以覆盖平台基础设施、安全和持续开发。这种透明的定价确保你确切知道你在为什么付费。"}}, "gettingMoreCredits": {"title": "获取更多积分", "description": "在 Kortix 中有几种获取积分的方式：", "monthlySubscription": {"title": "月度订阅积分", "description": "包含在你的付费计划中，每月自动续订。这些是过期积分。"}, "topUpCredits": {"title": "充值积分", "description": "需要时购买额外积分。这些是不过期的积分，适用于高级会员。"}, "promotionalGrants": {"title": "促销和活动赠品", "description": "来自特殊活动、促销或推荐的奖励积分。这些是不过期的。"}, "refunds": {"title": "退款", "description": "由于技术问题或失败任务而退回的积分。这些是不过期的。"}}, "typesOfCredits": {"title": "积分类型", "description": "Kortix 使用两种类型的积分，让你灵活管理使用：", "expiringCredits": {"title": "过期积分", "description": "月度订阅积分", "content": "这些积分包含在你的付费订阅中，每月在你的订阅日期自动续订。它们在每个计费周期结束时过期，并且总是在任何不过期积分之前首先消耗。"}, "nonExpiringCredits": {"title": "不过期积分", "description": "永不过期的永久积分", "content": "这些积分永不过期，可以逐月结转。它们包括充值购买、退款和促销赠品。不过期积分只在你过期积分耗尽后使用。"}, "creditPriority": {"title": "积分优先级：", "description": "当你使用 Kortix 时，过期积分首先被消耗。只有在过期积分用完后，才会使用不过期积分。"}}}, "close": "关闭", "cancelScheduledChange": "取消计划更改", "threadUsage": "线程使用", "error": "错误", "totalUsage": "总使用量", "usage": "使用情况", "thread": "线程", "lastUsed": "最后使用", "creditsUsed": "已使用积分", "creditTransactions": "积分交易", "loadingTransactionHistory": "正在加载你的交易历史...", "monthly": "月付", "yearly": "年付", "discount": "15% 折扣", "perMonth": "/月", "billedYearly": "按年计费", "downgradePending": "降级待处理", "downgrade": "降级", "switchToLegacyYearly": "切换到旧版年付", "pickPlan": "选择适合你的计划。", "reachedLimit": "你已达到限制。", "projectLimit": "项目限制 ({current}/{limit})", "threadLimit": "线程限制 ({current}/{limit})", "workerLimit": "Worker 限制 ({current}/{limit})", "triggerLimit": "触发器限制 ({current}/{limit})", "getAdditionalCredits": "获取额外积分", "selectPlan": "选择套餐", "changeScheduled": "更改已计划", "notAvailable": "不可用", "features": {"creditsPerMonth": "{count} 积分/月", "customWorker": "{count} 个自定义 Worker", "customWorkers": "{count} 个自定义 Workers", "privateProject": "{count} 个私有项目", "privateProjects": "私有项目", "customTrigger": "{count} 个自定义触发器", "integrations": "100+ 集成", "premiumAIModels": "高级 AI 模型", "prioritySupport": "优先支持"}}, "auth": {"logIntoAccount": "登录你的账户", "createAccount": "创建账号", "signInOrCreateAccount": "登录或创建您的Kortix账户", "continueWithGoogle": "使用 Google 继续", "continueWithGitHub": "使用 GitHub 继续", "orEmail": "或邮箱", "emailAddress": "邮箱地址", "password": "密码", "confirmPassword": "确认你的密码", "signIn": "登录", "signUp": "注册", "signingIn": "登录中...", "creatingAccount": "创建账户中...", "forgotPassword": "忘记密码？", "dontHaveAccount": "还没账号？", "alreadyHaveAccount": "已有账号？", "acceptPrivacyTerms": "我接受 <privacyPolicy>隐私政策</privacyPolicy> 和 <termsOfService>服务条款</termsOfService>", "privacyPolicy": "隐私政策", "termsOfService": "服务条款", "checkYourEmail": "查看邮箱", "confirmationLinkSent": "我们已向以下地址发送重置链接", "clickLinkToActivate": "点击邮件中的链接以激活你的账户。如果没看到邮件，请检查垃圾邮件文件夹。", "returnToHome": "返回首页", "backToSignIn": "返回登录", "resetPassword": "重置密码", "resetPasswordDescription": "输入你的邮箱，我们会发送重置链接给你", "sendResetLink": "发送链接", "cancel": "取消", "pleaseEnterValidEmail": "请输入有效的邮箱地址", "signInFailed": "登录失败，请检查你的账号信息", "signUpFailed": "注册失败，请重试", "sendMagicLink": "发送魔法链接", "sending": "发送中...", "magicLinkSent": "我们已向以下地址发送魔法链接", "magicLinkDescription": "点击邮件中的链接以登录。链接将在1小时后过期。", "magicLinkExplanation": "我们将向您发送一个安全的登录链接。无需密码。", "byContinuingYouAgree": "继续即表示您同意我们的<privacyPolicy>隐私政策</privacyPolicy>和<termsOfService>服务条款</termsOfService>"}, "onboarding": {"welcome": "欢迎", "next": "下一步", "skip": "跳过"}, "tools": {"executeCommand": "正在执行命令", "checkCommandOutput": "正在检查命令输出", "terminateCommand": "正在终止命令", "listCommands": "正在列出命令", "createFile": "正在创建文件", "deleteFile": "正在删除文件", "fullFileRewrite": "正在重写文件", "strReplace": "正在编辑文本", "editFile": "正在编辑文件", "uploadFile": "正在上传文件", "createDocument": "正在创建文档", "updateDocument": "正在更新文档", "readDocument": "正在读取文档", "listDocuments": "正在列出文档", "deleteDocument": "正在删除文档", "createTasks": "正在创建任务", "updateTasks": "正在更新任务", "browserNavigateTo": "正在导航到页面", "browserAct": "正在执行操作", "browserExtractContent": "正在提取内容", "browserScreenshot": "正在截图", "executeDataProviderCall": "正在调用数据提供者", "getDataProviderEndpoints": "正在获取端点", "ask": "询问", "wait": "等待", "complete": "正在完成任务", "crawlWebpage": "正在爬取网站", "exposePort": "正在暴露端口", "scrapeWebpage": "正在抓取网站", "webSearch": "正在搜索网络", "loadImage": "正在加载图片", "createPresentationOutline": "正在创建演示大纲", "createPresentation": "正在创建演示", "presentPresentation": "正在演示", "clearImagesFromContext": "正在清除上下文中的图片", "imageSearch": "正在搜索图片", "createSheet": "正在创建表格", "updateSheet": "正在更新表格", "viewSheet": "正在查看表格", "analyzeSheet": "正在分析表格", "visualizeSheet": "正在可视化表格", "formatSheet": "正在格式化表格", "updateAgent": "正在更新智能体", "getCurrentAgentConfig": "正在获取智能体配置", "searchMcpServers": "正在搜索 MCP 服务器", "getMcpServerTools": "正在获取 MCP 服务器工具", "configureMcpServer": "正在配置 MCP 服务器", "getPopularMcpServers": "正在获取热门 MCP 服务器", "testMcpServerConnection": "正在测试 MCP 服务器连接", "listAppEventTriggers": "正在查找事件触发器", "createEventTrigger": "正在创建事件触发器", "getProjectStructure": "正在获取项目结构", "buildProject": "正在构建项目", "makePhoneCall": "正在拨打电话", "endCall": "正在结束通话", "getCallDetails": "正在获取通话详情", "listCalls": "正在列出通话", "monitorCall": "正在监控通话", "waitForCallCompletion": "正在等待完成", "getPaperDetails": "正在获取论文详情", "searchAuthors": "正在搜索作者", "getAuthorDetails": "正在获取作者详情", "getAuthorPapers": "正在获取作者论文", "getPaperCitations": "正在获取论文引用", "getPaperReferences": "正在获取论文参考文献", "paperSearch": "正在搜索论文", "discoverUserMcpServers": "正在发现工具", "createCredentialProfile": "正在创建配置文件", "getCredentialProfiles": "正在获取配置文件", "configureProfileForAgent": "正在向智能体添加工具", "createNewAgent": "正在创建新智能体", "searchMcpServersForAgent": "正在搜索 MCP 服务器", "createCredentialProfileForAgent": "正在创建凭据配置文件", "discoverMcpToolsForAgent": "正在发现 MCP 工具", "configureAgentIntegration": "正在配置智能体集成", "createAgentScheduledTrigger": "正在创建计划触发器", "listAgentScheduledTriggers": "正在列出智能体计划触发器", "executingTool": "正在执行工具", "toolExecutedSuccessfully": "工具执行成功", "toolExecutionFailed": "工具执行失败", "input": "输入", "output": "输出", "copyFileContent": "复制文件内容", "fileContentCopied": "文件内容已复制到剪贴板", "failedToCopyFileContent": "复制文件内容失败", "noContentAvailable": "无可用内容", "noContentDescription": "此工具执行未产生任何输入或输出内容可显示。", "tool": "工具"}, "agentRunLimit": {"gotIt": "知道了", "parallelRunsLimitReached": "已达到并行运行限制", "parallelRunsLimitDescription": "你已达到允许的最大并行智能体运行数。", "needMoreParallelRuns": "需要更多并行运行？", "upgradeDescription": "升级你的计划以同时运行多个智能体并提高生产力。", "upgradePlan": "升级计划", "currentlyRunningAgents": "当前运行的智能体", "loadingThreads": "正在加载线程...", "foundRunningAgents": "找到 {count} 个正在运行的智能体，但无法加载线程详情。", "threadIds": "线程 ID: {ids}", "moreRunning": "+{count} 个正在运行", "whatCanYouDo": "你能做什么？", "stopAgentsHint": "点击停止按钮以停止正在运行的智能体", "waitForCompletion": "等待智能体自动完成", "upgradeYourPlan": "升级你的计划", "forMoreParallelRuns": "以获得更多并行运行", "stopAgent": "停止智能体", "stopThisAgent": "停止此智能体", "openThread": "打开线程", "agentStoppedSuccessfully": "智能体已成功停止", "failedToStopAgent": "停止智能体失败", "upgradeToRunMore": "升级以并行运行更多智能体"}, "errors": {"generic": "发生错误", "network": "网络错误", "unauthorized": "未授权", "notFound": "未找到"}, "languages": {"en": "English", "de": "De<PERSON>ch", "it": "Italiano", "zh": "中文", "ja": "日本語", "pt": "Português", "fr": "Français", "es": "Español"}, "suna": {"samplePrompts": "示例提示", "chooseStyle": "选择风格", "chooseTemplate": "选择模板", "chooseOutputFormat": "选择输出格式", "preferredCharts": "首选图表", "whatWouldYouLike": "你想完成什么？", "describeTask": "描述你想让你的 Worker 完成的任务...", "modes": {"image": "图片", "slides": "幻灯片", "data": "数据", "docs": "文档", "people": "人物", "research": "研究"}, "styles": {"photorealistic": "写实", "watercolor": "水彩", "digital-art": "数字艺术", "oil-painting": "油画", "minimalist": "极简", "isometric": "等距", "vintage": "复古", "comic": "漫画", "neon": "霓虹", "pastel": "粉彩", "geometric": "几何", "abstract": "抽象", "anime": "动漫", "impressionist": "印象派", "surreal": "超现实"}, "templates": {"minimalist": {"name": "极简", "description": "简洁设计"}, "minimalist_2": {"name": "极简2", "description": "替代极简风格"}, "black_and_white_clean": {"name": "黑白", "description": "经典单色"}, "colorful": {"name": "彩色", "description": "充满活力"}, "startup": {"name": "创业", "description": "动态创新"}, "elevator_pitch": {"name": "电梯演讲", "description": "快速有力"}, "portfolio": {"name": "作品集", "description": "展示你的作品"}, "textbook": {"name": "教科书", "description": "教育结构化"}, "architect": {"name": "架构师", "description": "专业精确"}, "hipster": {"name": "潮人", "description": "现代时尚"}, "green": {"name": "绿色", "description": "自然灵感设计"}, "premium_black": {"name": "高级黑", "description": "奢华深色主题"}, "premium_green": {"name": "高级绿", "description": "精致绿色"}, "professor_gray": {"name": "教授灰", "description": "学术风格"}, "gamer_gray": {"name": "游戏灰", "description": "游戏风格设计"}, "competitor_analysis_blue": {"name": "分析蓝", "description": "商业分析导向"}, "numbers_clean": {"name": "数字简洁", "description": "简洁数据可视化"}, "numbers_colorful": {"name": "数字彩色", "description": "生动数据展示"}, "prd": {"name": "PRD", "description": "产品需求文档"}, "technical": {"name": "技术", "description": "技术文档"}, "proposal": {"name": "提案", "description": "商业提案"}, "report": {"name": "报告", "description": "详细报告格式"}, "guide": {"name": "指南", "description": "分步指南"}, "wiki": {"name": "维基", "description": "知识库文章"}, "policy": {"name": "政策", "description": "政策文档"}, "meeting-notes": {"name": "会议记录", "description": "会议记录"}}, "outputFormats": {"spreadsheet": {"name": "电子表格", "description": "带公式的表格"}, "dashboard": {"name": "仪表板", "description": "交互式图表"}, "report": {"name": "报告", "description": "带可视化的分析"}, "slides": {"name": "幻灯片", "description": "演示格式"}}, "charts": {"bar": "柱状图", "line": "折线图", "pie": "饼图", "scatter": "散点图", "heatmap": "热力图", "bubble": "气泡图", "wordcloud": "词云", "stacked": "堆叠柱状图", "area": "面积图"}, "prompts": {"image": {"0": "日出时分，一只雄伟的金色雄鹰在薄雾笼罩的山峰间翱翔，光线戏剧性", "1": "时尚模特的近距离肖像，前卫妆容，工作室灯光，高对比度阴影", "2": "舒适的斯堪的纳维亚客厅，天然木制家具，室内植物，柔和的晨光", "3": "未来主义赛博朋克风格的夜间街头市场，霓虹灯招牌，雨湿的路面，全息显示屏", "4": "优雅的产品摄影，豪华香水瓶放在大理石表面，柔和的反射", "5": "梦幻的浮岛，由绳索桥连接，在柔和的天空和梦幻的云朵中", "6": "充满活力的花瓣上晨露的微距特写，背景虚化", "7": "现代工作空间桌面设置，从上方看有笔记本电脑、咖啡、笔记本和多肉植物", "8": "神秘的森林小径，古树，发光的萤火虫，透过雾气的光束", "9": "当代玻璃建筑立面的建筑细节，几何图案和反射", "10": "充满活力的街头小吃摊，色彩丰富的食材，蒸汽升腾，温暖的灯光", "11": "宁静的日式禅意花园，耙过的沙子，长满苔藓的石头，樱花花瓣", "12": "运动员跳跃中的动态动作镜头，背景是戏剧性的日落天空，剪影效果", "13": "乡村农舍厨房，铜锅，新鲜香草，木制砧板，自然质感", "14": "抽象流动艺术，旋转的金属金色，深蓝色和翠绿色有机图案"}, "slides": {"0": "创建一个包含市场规模、牵引力和财务预测的 A 轮融资路演", "1": "构建一个展示 KPI、成果和战略举措的第四季度业务回顾", "2": "设计一个包含演示视频和客户推荐的产品发布演示", "3": "开发一个销售赋能演示，解释我们的价值主张和竞争优势", "4": "创建一个突出关键指标和即将到来的里程碑的投资者更新", "5": "构建一个展示投资回报率和成功指标的客户案例研究演示", "6": "设计一个涵盖公司更新和愿景的全员演示", "7": "为新产品功能和工作流程开发培训演示", "8": "创建一个关于扩展工程团队的会议演讲", "9": "构建一个包含战略建议的董事会会议演示"}, "data": {"0": "构建一个财务模型，预测不同定价场景下的 ARR 增长", "1": "创建一个按地区和季度跟踪指标的交互式销售仪表板", "2": "分析 5 万条客户评论，并可视化随时间的情感趋势", "3": "设计一个内容日历，跟踪带有投资回报率和参与度图表的营销活动", "4": "构建一个显示用户留存和流失模式的队列分析", "5": "创建一个比较渠道表现的营销归因模型", "6": "开发一个带有管道指标和填补时间分析的招聘跟踪器", "7": "构建一个带有场景建模的预算规划电子表格", "8": "分析网站流量数据并可视化转化漏斗", "9": "创建一个带有自动补货提醒的库存管理系统"}, "docs": {"0": "为 AI 驱动的推荐引擎编写全面的产品需求文档", "1": "为可扩展的微服务平台起草技术架构文档", "2": "为我们的第二季度产品发布创建上市策略文档", "3": "为工程经理开发 90 天入职手册", "4": "编写包含示例和最佳实践的 API 文档指南", "5": "创建涵盖文化、政策和福利的公司手册", "6": "起草符合 GDPR 和 CCPA 的数据隐私政策", "7": "为 SaaS 企业账户开发客户成功手册", "8": "编写包含升级程序的安全事件响应计划", "9": "创建涵盖品牌和内容的全面风格指南"}, "people": {"0": "在旧金山湾区的 B 轮以上 AI/ML 初创公司中寻找具有 10 年以上经验并拥有扩展工程团队成功记录的工程副总裁候选人", "1": "构建最近获得 A/B 轮融资的 B2B SaaS 公司（ARR $1000 万-$5000 万）的 CMO 潜在客户列表 - 包括电子邮件模式和技术栈", "2": "研究在顶级加密项目中具有 Solidity/Rust 经验的高级区块链工程师，愿意搬迁到迪拜或新加坡", "3": "生成过去 6 个月内筹集了 $200 万-$1500 万的企业 AI 种子-A 轮初创公司的技术创始人潜在客户列表", "4": "识别在金融科技公司中具有 5-10 年经验、来自 FAANG 或独角兽公司、精通 0-1 产品开发的高级产品经理", "5": "在中型医疗 IT 公司（500-5000 名员工）中寻找具有 $50 万以上 IT 预算并计划云迁移的 CIO 和工程副总裁", "6": "研究在 B2B SaaS 公司中显示 100% 以上同比增长、拥有 7 年以上完成 $10 万以上交易经验和 PLG 经验的销售副总裁", "7": "构建在 2024 年积极实施 AI 基础设施、拥有数百万美元预算的企业公司 CTO 列表", "8": "寻找具有移动优先消费者应用经验和 100 万以上用户作品集的高级 UX/UI 设计师，积极寻找或对机会开放", "9": "识别在云原生初创公司中具有 Kubernetes/Terraform 专业知识、拥有 5-8 年为 1000 万以上用户构建基础设施经验的高级 DevOps 工程师"}, "research": {"0": "分析量子计算的新兴趋势和潜在商业应用", "1": "研究 AI 驱动的 CRM 领域的十大竞争对手，并进行功能比较", "2": "调查在欧盟推出金融科技应用的监管要求", "3": "汇编主要市场电动汽车采用率的市场分析", "4": "研究远程工作对主要城市商业房地产需求的影响", "5": "研究财富 500 强公司中的 Web3 采用模式", "6": "分析消费者对可持续时尚品牌的情绪", "7": "调查罕见疾病基因治疗的最新进展", "8": "研究成功的 D2C 订阅盒公司的定价策略", "9": "研究 AI 驱动的网络安全解决方案的竞争格局"}}}}