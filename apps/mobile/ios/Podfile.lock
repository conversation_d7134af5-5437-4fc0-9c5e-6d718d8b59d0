PODS:
  - EASClient (1.0.7):
    - ExpoModulesCore
  - EXApplication (7.0.7):
    - ExpoModulesCore
  - EXAV (16.0.7):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (18.0.10):
    - ExpoModulesCore
  - EXImageLoader (6.0.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.15.0)
  - EXManifests (1.0.9):
    - ExpoModulesCore
  - EXNotifications (0.32.13):
    - ExpoModulesCore
  - Expo (54.0.25):
    - ExpoModulesCore
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-client (6.0.18):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (6.0.18):
    - EXManifests
    - expo-dev-launcher/Main (= 6.0.18)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-launcher/Main (6.0.18):
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-launcher/Unsafe (6.0.18):
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-menu (7.0.17):
    - expo-dev-menu/Main (= 7.0.17)
    - expo-dev-menu/ReactNativeCompatibles (= 7.0.17)
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-menu-interface (2.0.0)
  - expo-dev-menu/Main (7.0.17):
    - EXManifests
    - expo-dev-menu-interface
    - ExpoModulesCore
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (7.0.17):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - ExpoAppleAuthentication (8.0.7):
    - ExpoModulesCore
  - ExpoAsset (12.0.10):
    - ExpoModulesCore
  - ExpoAudio (1.0.15):
    - ExpoModulesCore
  - ExpoBlur (15.0.7):
    - ExpoModulesCore
  - ExpoClipboard (8.0.7):
    - ExpoModulesCore
  - ExpoCrypto (15.0.7):
    - ExpoModulesCore
  - ExpoDevice (8.0.9):
    - ExpoModulesCore
  - ExpoDocumentPicker (14.0.7):
    - ExpoModulesCore
  - ExpoFileSystem (19.0.19):
    - ExpoModulesCore
  - ExpoFont (14.0.9):
    - ExpoModulesCore
  - ExpoHaptics (15.0.7):
    - ExpoModulesCore
  - ExpoHead (6.0.15):
    - ExpoModulesCore
    - RNScreens
  - ExpoImagePicker (17.0.8):
    - ExpoModulesCore
  - ExpoKeepAwake (15.0.7):
    - ExpoModulesCore
  - ExpoLinearGradient (15.0.7):
    - ExpoModulesCore
  - ExpoLinking (8.0.9):
    - ExpoModulesCore
  - ExpoModulesCore (3.0.26):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - ExpoSplashScreen (31.0.11):
    - ExpoModulesCore
  - ExpoSystemUI (6.0.8):
    - ExpoModulesCore
  - ExpoTrackingTransparency (6.0.7):
    - ExpoModulesCore
  - ExpoWebBrowser (15.0.9):
    - ExpoModulesCore
  - EXStructuredHeaders (5.0.0)
  - ExtensionStorage (1.0.0):
    - ExpoModulesCore
  - EXUpdates (29.0.13):
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - ReachabilitySwift
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - EXUpdatesInterface (2.0.0):
    - ExpoModulesCore
  - FBLazyVector (0.81.5)
  - hermes-engine (0.81.5):
    - hermes-engine/Pre-built (= 0.81.5)
  - hermes-engine/Pre-built (0.81.5)
  - lottie-ios (4.5.0)
  - lottie-react-native (7.3.4):
    - hermes-engine
    - lottie-ios (= 4.5.0)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - PurchasesHybridCommon (17.19.1):
    - RevenueCat (= 5.48.0)
  - PurchasesHybridCommonUI (17.19.1):
    - PurchasesHybridCommon (= 17.19.1)
    - RevenueCatUI (= 5.48.0)
  - RCTDeprecation (0.81.5)
  - RCTRequired (0.81.5)
  - RCTTypeSafety (0.81.5):
    - FBLazyVector (= 0.81.5)
    - RCTRequired (= 0.81.5)
    - React-Core (= 0.81.5)
  - ReachabilitySwift (5.2.4)
  - React (0.81.5):
    - React-Core (= 0.81.5)
    - React-Core/DevSupport (= 0.81.5)
    - React-Core/RCTWebSocket (= 0.81.5)
    - React-RCTActionSheet (= 0.81.5)
    - React-RCTAnimation (= 0.81.5)
    - React-RCTBlob (= 0.81.5)
    - React-RCTImage (= 0.81.5)
    - React-RCTLinking (= 0.81.5)
    - React-RCTNetwork (= 0.81.5)
    - React-RCTSettings (= 0.81.5)
    - React-RCTText (= 0.81.5)
    - React-RCTVibration (= 0.81.5)
  - React-callinvoker (0.81.5)
  - React-Core (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core-prebuilt (0.81.5):
    - ReactNativeDependencies
  - React-Core/CoreModulesHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/Default (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/DevSupport (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.5)
    - React-Core/RCTWebSocket (= 0.81.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTAnimationHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTBlobHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTImageHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTLinkingHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTNetworkHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTSettingsHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTTextHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTVibrationHeaders (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTWebSocket (0.81.5):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-CoreModules (0.81.5):
    - RCTTypeSafety (= 0.81.5)
    - React-Core-prebuilt
    - React-Core/CoreModulesHeaders (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.81.5)
    - React-runtimeexecutor
    - ReactCommon
    - ReactNativeDependencies
  - React-cxxreact (0.81.5):
    - hermes-engine
    - React-callinvoker (= 0.81.5)
    - React-Core-prebuilt
    - React-debug (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.81.5)
    - React-perflogger (= 0.81.5)
    - React-runtimeexecutor
    - React-timing (= 0.81.5)
    - ReactNativeDependencies
  - React-debug (0.81.5)
  - React-defaultsnativemodule (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - ReactNativeDependencies
  - React-domnativemodule (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-Fabric
    - React-Fabric/bridging
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.81.5)
    - React-Fabric/attributedstring (= 0.81.5)
    - React-Fabric/bridging (= 0.81.5)
    - React-Fabric/componentregistry (= 0.81.5)
    - React-Fabric/componentregistrynative (= 0.81.5)
    - React-Fabric/components (= 0.81.5)
    - React-Fabric/consistency (= 0.81.5)
    - React-Fabric/core (= 0.81.5)
    - React-Fabric/dom (= 0.81.5)
    - React-Fabric/imagemanager (= 0.81.5)
    - React-Fabric/leakchecker (= 0.81.5)
    - React-Fabric/mounting (= 0.81.5)
    - React-Fabric/observers (= 0.81.5)
    - React-Fabric/scheduler (= 0.81.5)
    - React-Fabric/telemetry (= 0.81.5)
    - React-Fabric/templateprocessor (= 0.81.5)
    - React-Fabric/uimanager (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/animations (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/attributedstring (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/bridging (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistry (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistrynative (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.81.5)
    - React-Fabric/components/root (= 0.81.5)
    - React-Fabric/components/scrollview (= 0.81.5)
    - React-Fabric/components/view (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/legacyviewmanagerinterop (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/root (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/scrollview (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/view (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric/consistency (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/core (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/dom (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/imagemanager (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/leakchecker (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/mounting (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers/events (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/scheduler (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/telemetry (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/templateprocessor (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager/consistency (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-FabricComponents (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.81.5)
    - React-FabricComponents/textlayoutmanager (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.81.5)
    - React-FabricComponents/components/iostextinput (= 0.81.5)
    - React-FabricComponents/components/modal (= 0.81.5)
    - React-FabricComponents/components/rncore (= 0.81.5)
    - React-FabricComponents/components/safeareaview (= 0.81.5)
    - React-FabricComponents/components/scrollview (= 0.81.5)
    - React-FabricComponents/components/switch (= 0.81.5)
    - React-FabricComponents/components/text (= 0.81.5)
    - React-FabricComponents/components/textinput (= 0.81.5)
    - React-FabricComponents/components/unimplementedview (= 0.81.5)
    - React-FabricComponents/components/virtualview (= 0.81.5)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/iostextinput (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/modal (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/rncore (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/safeareaview (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/scrollview (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/switch (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/text (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/textinput (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/virtualview (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricImage (0.81.5):
    - hermes-engine
    - RCTRequired (= 0.81.5)
    - RCTTypeSafety (= 0.81.5)
    - React-Core-prebuilt
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.81.5)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-featureflags (0.81.5):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-featureflagsnativemodule (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-graphics (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - ReactNativeDependencies
  - React-hermes (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-jsi
    - React-jsiexecutor (= 0.81.5)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.5)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-idlecallbacksnativemodule (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-ImageManager (0.81.5):
    - React-Core-prebuilt
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - ReactNativeDependencies
  - React-jserrorhandler (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - ReactNativeDependencies
  - React-jsi (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsiexecutor (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.5)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspector (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-oscompat
    - React-perflogger (= 0.81.5)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspectorcdp (0.81.5):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsinspectornetwork (0.81.5):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectorcdp
    - React-performancetimeline
    - React-timing
    - ReactNativeDependencies
  - React-jsinspectortracing (0.81.5):
    - React-Core-prebuilt
    - React-oscompat
    - React-timing
    - ReactNativeDependencies
  - React-jsitooling (0.81.5):
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsitracing (0.81.5):
    - React-jsi
  - React-logger (0.81.5):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-Mapbuffer (0.81.5):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-microtasksnativemodule (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - react-native-safe-area-context (5.6.2):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.6.2)
    - react-native-safe-area-context/fabric (= 5.6.2)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - react-native-safe-area-context/common (5.6.2):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - react-native-safe-area-context/fabric (5.6.2):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - react-native-webview (13.16.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-NativeModulesApple (0.81.5):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-oscompat (0.81.5)
  - React-perflogger (0.81.5):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-performancetimeline (0.81.5):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - ReactNativeDependencies
  - React-RCTActionSheet (0.81.5):
    - React-Core/RCTActionSheetHeaders (= 0.81.5)
  - React-RCTAnimation (0.81.5):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTAppDelegate (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTBlob (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFabric (0.81.5):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-RCTFBReactNativeSpec (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec/components (= 0.81.5)
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFBReactNativeSpec/components (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-RCTImage (0.81.5):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTLinking (0.81.5):
    - React-Core/RCTLinkingHeaders (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.81.5)
  - React-RCTNetwork (0.81.5):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTRuntime (0.81.5):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - ReactNativeDependencies
  - React-RCTSettings (0.81.5):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTText (0.81.5):
    - React-Core/RCTTextHeaders (= 0.81.5)
    - Yoga
  - React-RCTVibration (0.81.5):
    - React-Core-prebuilt
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-rendererconsistency (0.81.5)
  - React-renderercss (0.81.5):
    - React-debug
    - React-utils
  - React-rendererdebug (0.81.5):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-RuntimeApple (0.81.5):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeCore (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-runtimeexecutor (0.81.5):
    - React-Core-prebuilt
    - React-debug
    - React-featureflags
    - React-jsi (= 0.81.5)
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeHermes (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-utils
    - ReactNativeDependencies
  - React-runtimescheduler (0.81.5):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - ReactNativeDependencies
  - React-timing (0.81.5):
    - React-debug
  - React-utils (0.81.5):
    - hermes-engine
    - React-Core-prebuilt
    - React-debug
    - React-jsi (= 0.81.5)
    - ReactNativeDependencies
  - ReactAppDependencyProvider (0.81.5):
    - ReactCodegen
  - ReactCodegen (0.81.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - ReactCommon (0.81.5):
    - React-Core-prebuilt
    - ReactCommon/turbomodule (= 0.81.5)
    - ReactNativeDependencies
  - ReactCommon/turbomodule (0.81.5):
    - hermes-engine
    - React-callinvoker (= 0.81.5)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-logger (= 0.81.5)
    - React-perflogger (= 0.81.5)
    - ReactCommon/turbomodule/bridging (= 0.81.5)
    - ReactCommon/turbomodule/core (= 0.81.5)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/bridging (0.81.5):
    - hermes-engine
    - React-callinvoker (= 0.81.5)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-logger (= 0.81.5)
    - React-perflogger (= 0.81.5)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/core (0.81.5):
    - hermes-engine
    - React-callinvoker (= 0.81.5)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.5)
    - React-debug (= 0.81.5)
    - React-featureflags (= 0.81.5)
    - React-jsi (= 0.81.5)
    - React-logger (= 0.81.5)
    - React-perflogger (= 0.81.5)
    - React-utils (= 0.81.5)
    - ReactNativeDependencies
  - ReactNativeDependencies (0.81.5)
  - RevenueCat (5.48.0)
  - RevenueCatUI (5.48.0):
    - RevenueCat (= 5.48.0)
  - RNCAsyncStorage (2.2.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNCMaskedView (0.3.2):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNGestureHandler (2.28.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNPaywalls (9.6.7):
    - PurchasesHybridCommonUI (= 17.19.1)
    - React-Core
  - RNPurchases (9.6.7):
    - PurchasesHybridCommon (= 17.19.1)
    - React-Core
  - RNReanimated (4.1.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNReanimated/reanimated (= 4.1.5)
    - RNWorklets
    - Yoga
  - RNReanimated/reanimated (4.1.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNReanimated/reanimated/apple (= 4.1.5)
    - RNWorklets
    - Yoga
  - RNReanimated/reanimated/apple (4.1.5):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets
    - Yoga
  - RNScreens (4.16.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNScreens/common (= 4.16.0)
    - Yoga
  - RNScreens/common (4.16.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNSoundLevel (1.3.0):
    - React
  - RNSVG (15.12.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNSVG/common (= 15.12.1)
    - Yoga
  - RNSVG/common (15.12.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNVectorIcons (10.3.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNWorklets (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets/worklets (= 0.5.1)
    - Yoga
  - RNWorklets/worklets (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets/worklets/apple (= 0.5.1)
    - Yoga
  - RNWorklets/worklets/apple (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - SelectableText (2.1.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - Yoga (0.0.0)

DEPENDENCIES:
  - EASClient (from `../node_modules/expo-eas-client/ios`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../node_modules/expo-notifications/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAppleAuthentication (from `../node_modules/expo-apple-authentication/ios`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoAudio (from `../node_modules/expo-audio/ios`)
  - ExpoBlur (from `../node_modules/expo-blur/ios`)
  - ExpoClipboard (from `../node_modules/expo-clipboard/ios`)
  - ExpoCrypto (from `../node_modules/expo-auth-session/node_modules/expo-crypto/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoDocumentPicker (from `../node_modules/expo-document-picker/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoHaptics (from `../node_modules/expo-haptics/ios`)
  - ExpoHead (from `../node_modules/expo-router/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - ExpoSystemUI (from `../node_modules/expo-system-ui/ios`)
  - ExpoTrackingTransparency (from `../node_modules/expo-tracking-transparency/ios`)
  - ExpoWebBrowser (from `../node_modules/expo-web-browser/ios`)
  - EXStructuredHeaders (from `../node_modules/expo-structured-headers/ios`)
  - "ExtensionStorage (from `../node_modules/@bacons/apple-targets/ios`)"
  - EXUpdates (from `../node_modules/expo-updates/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core-prebuilt (from `../node_modules/react-native/React-Core-prebuilt.podspec`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeDependencies (from `../node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPaywalls (from `../node_modules/react-native-purchases-ui`)
  - RNPurchases (from `../node_modules/react-native-purchases`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSoundLevel (from `../node_modules/react-native-sound-level`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - RNWorklets (from `../node_modules/react-native-worklets`)
  - "SelectableText (from `../node_modules/@rob117/react-native-selectable-text`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - lottie-ios
    - PurchasesHybridCommon
    - PurchasesHybridCommonUI
    - ReachabilitySwift
    - RevenueCat
    - RevenueCatUI

EXTERNAL SOURCES:
  EASClient:
    :path: "../node_modules/expo-eas-client/ios"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../node_modules/expo-notifications/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoAppleAuthentication:
    :path: "../node_modules/expo-apple-authentication/ios"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoAudio:
    :path: "../node_modules/expo-audio/ios"
  ExpoBlur:
    :path: "../node_modules/expo-blur/ios"
  ExpoClipboard:
    :path: "../node_modules/expo-clipboard/ios"
  ExpoCrypto:
    :path: "../node_modules/expo-auth-session/node_modules/expo-crypto/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoDocumentPicker:
    :path: "../node_modules/expo-document-picker/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../node_modules/expo-haptics/ios"
  ExpoHead:
    :path: "../node_modules/expo-router/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  ExpoSystemUI:
    :path: "../node_modules/expo-system-ui/ios"
  ExpoTrackingTransparency:
    :path: "../node_modules/expo-tracking-transparency/ios"
  ExpoWebBrowser:
    :path: "../node_modules/expo-web-browser/ios"
  EXStructuredHeaders:
    :path: "../node_modules/expo-structured-headers/ios"
  ExtensionStorage:
    :path: "../node_modules/@bacons/apple-targets/ios"
  EXUpdates:
    :path: "../node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-07-RNv0.81.0-e0fc67142ec0763c6b6153ca2bf96df815539782
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-Core-prebuilt:
    :podspec: "../node_modules/react-native/React-Core-prebuilt.podspec"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeDependencies:
    :podspec: "../node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPaywalls:
    :path: "../node_modules/react-native-purchases-ui"
  RNPurchases:
    :path: "../node_modules/react-native-purchases"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSoundLevel:
    :path: "../node_modules/react-native-sound-level"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  RNWorklets:
    :path: "../node_modules/react-native-worklets"
  SelectableText:
    :path: "../node_modules/@rob117/react-native-selectable-text"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  EASClient: 68127f1248d2b25fdc82dbbfb17be95d1c4700be
  EXApplication: 296622817d459f46b6c5fe8691f4aac44d2b79e7
  EXAV: e3fefaca88f14624bca01d9a128562a5f8e738f8
  EXConstants: fd688cef4e401dcf798a021cfb5d87c890c30ba3
  EXImageLoader: 189e3476581efe3ad4d1d3fb4735b7179eb26f05
  EXJSONUtils: 1d3e4590438c3ee593684186007028a14b3686cd
  EXManifests: 26e15640538c3d5ef028077ebcaf004b744d4932
  EXNotifications: a62e1f8e3edd258dc3b155d3caa49f32920f1c6c
  Expo: c347cdb69706ea3de83da97ce956042aafd8e62a
  expo-dev-client: cc9491ad3e25a7a89f367c30f72884584c682b77
  expo-dev-launcher: 61322d7e368e7fa78e355b8882df90f5e8eafe93
  expo-dev-menu: 66daef3e1fce372ac93fd079ad8172a9c1206cc3
  expo-dev-menu-interface: 600df12ea01efecdd822daaf13cc0ac091775533
  ExpoAppleAuthentication: bc9de6e9ff3340604213ab9031d4c4f7f802623e
  ExpoAsset: d839c8eae8124470332408427327e8f88beb2dfd
  ExpoAudio: a435784a5da9c27649d5c585b47c65ee4e25ff2a
  ExpoBlur: 2dd8f64aa31f5d405652c21d3deb2d2588b1852f
  ExpoClipboard: af650d14765f19c60ce2a1eaf9dfe6445eff7365
  ExpoCrypto: c1fbce112d1b6b79652bbe380b4fd4cc91676595
  ExpoDevice: 148accb4071873d19fba80a2506c58ffa433d620
  ExpoDocumentPicker: 2200eefc2817f19315fa18f0147e0b80ece86926
  ExpoFileSystem: 77157a101e03150a4ea4f854b4dd44883c93ae0a
  ExpoFont: cf9d90ec1d3b97c4f513211905724c8171f82961
  ExpoHaptics: 807476b0c39e9d82b7270349d6487928ce32df84
  ExpoHead: 95a6ee0be1142320bccf07961d6a1502ded5d6ac
  ExpoImagePicker: d251aab45a1b1857e4156fed88511b278b4eee1c
  ExpoKeepAwake: 1a2e820692e933c94a565ec3fbbe38ac31658ffe
  ExpoLinearGradient: a464898cb95153125e3b81894fd479bcb1c7dd27
  ExpoLinking: 77455aa013e9b6a3601de03ecfab09858ee1b031
  ExpoModulesCore: 9460546192590b0ed13493d9b01bed256d3440b0
  ExpoSplashScreen: 268b2f128dc04284c21010540a6c4dd9f95003e3
  ExpoSystemUI: 2761aa6875849af83286364811d46e8ed8ea64c7
  ExpoTrackingTransparency: f9ad2b1955c71085aa9208ed180721301614bac9
  ExpoWebBrowser: b973e1351fdcf5fec0c400997b1851f5a8219ec3
  EXStructuredHeaders: c951e77f2d936f88637421e9588c976da5827368
  ExtensionStorage: 5796de20bf181317c376d693fedb64c6781a33ba
  EXUpdates: 9b61bfe4fbc0188ed61e406ee13bfacecb9328cf
  EXUpdatesInterface: 5adf50cb41e079c861da6d9b4b954c3db9a50734
  FBLazyVector: e95a291ad2dadb88e42b06e0c5fb8262de53ec12
  hermes-engine: 9f4dfe93326146a1c99eb535b1cb0b857a3cd172
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: c34a02df495e2002c36ea2f682ae1c1ef2e11ce7
  PurchasesHybridCommon: a4837eebc889b973668af685d6c23b89a038461d
  PurchasesHybridCommonUI: bf5566763f906429584534a117a7862537b92c0c
  RCTDeprecation: 943572d4be82d480a48f4884f670135ae30bf990
  RCTRequired: 8f3cfc90cc25cf6e420ddb3e7caaaabc57df6043
  RCTTypeSafety: 16a4144ca3f959583ab019b57d5633df10b5e97c
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: 914f8695f9bf38e6418228c2ffb70021e559f92f
  React-callinvoker: 1c0808402aee0c6d4a0d8e7220ce6547af9fba71
  React-Core: c61410ef0ca6055e204a963992e363227e0fd1c5
  React-Core-prebuilt: 02f0ad625ddd47463c009c2d0c5dd35c0d982599
  React-CoreModules: 1f6d1744b5f9f2ec684a4bb5ced25370f87e5382
  React-cxxreact: 3af79478e8187b63ffc22b794cd42d3fc1f1f2da
  React-debug: dfe7f2f01b36058f2a9b94872e071eb40e063a3c
  React-defaultsnativemodule: 414bc0a2e02166cd7fa0a41e104434935ae01708
  React-domnativemodule: 9ec14a7fed0d3e4e76fd48e07d911f8934ab282d
  React-Fabric: a2c0c50b9f0ea1f67e6b4bbde55589cacb8c3213
  React-FabricComponents: 796f19e3beb5e44bcdd72bd4d8a5954d6a61d225
  React-FabricImage: f75d4b23f449364c3aeae684cd2995c2be68149a
  React-featureflags: 3285df1e581749640b797db0ab2120e9c6974da5
  React-featureflagsnativemodule: 181e9aa5afcdbc27ca560a6872d9510e0f7d9179
  React-graphics: 22b2dce86291d73fe7e37c7492d44865d9077a67
  React-hermes: e875778b496c86d07ab2ccaa36a9505d248a254b
  React-idlecallbacksnativemodule: db0dae0874018750aedb4f6f574d68c05c6478cd
  React-ImageManager: c659ddac4e196a28cefd036f5881b2c355363882
  React-jserrorhandler: 23a4e4e66c95a68300a4d01f359f9b8a5500aaa3
  React-jsi: 89d43d1e7d4d0663f8ba67e0b39eb4e4672c27de
  React-jsiexecutor: abe4874aaab90dfee5dec480680220b2f8af07e3
  React-jsinspector: 91f19a57032075d0f820fdee080080a21c4afabe
  React-jsinspectorcdp: 20537446838cf73f826cb1f78ec1180c58da6eb5
  React-jsinspectornetwork: fc9a317f3a57c33da6252c9cc160d321b85a3a48
  React-jsinspectortracing: 9dcf5dd0b7a4373704c4804cac75790b7d7977b4
  React-jsitooling: 65c430d41f8df1e5014863dbc569068d0458359b
  React-jsitracing: 12d9474cefa6ce0ac4d0affde93dddabb17ea343
  React-logger: 50fdb9a8236da90c0b1072da5c32ee03aeb5bf28
  React-Mapbuffer: 0164bc6ba0866e6314d86851620e6f1f95a1b4fb
  React-microtasksnativemodule: f5972fb404f7c363ff34b2292e72a716956c590c
  react-native-safe-area-context: c5d42c4294aa175415947e23c2ac956253abfc7d
  react-native-webview: 6b41d44f853c7f8dd8b685d8d289fe33349e4e1b
  React-NativeModulesApple: 8969913947d5b576de4ed371a939455a8daf28aa
  React-oscompat: ce47230ed20185e91de62d8c6d139ae61763d09c
  React-perflogger: 02b010e665772c7dcb859d85d44c1bfc5ac7c0e4
  React-performancetimeline: bdad83ed66209c5cd252a38ed2c7b16c535c2286
  React-RCTActionSheet: 0b14875b3963e9124a5a29a45bd1b22df8803916
  React-RCTAnimation: a7b90fd2af7bb9c084428867445a1481a8cb112e
  React-RCTAppDelegate: 3262bedd01263f140ec62b7989f4355f57cec016
  React-RCTBlob: c17531368702f1ebed5d0ada75a7cf5915072a53
  React-RCTFabric: 14cd088b95d6c00c9dadad25cf946fb1770cc898
  React-RCTFBReactNativeSpec: 8948374e74e37e65923abfffeb12bf78e0aa7318
  React-RCTImage: c68078a120d0123f4f07a5ac77bea3bb10242f32
  React-RCTLinking: cf8f9391fe7fe471f96da3a5f0435235eca18c5b
  React-RCTNetwork: ca31f7c879355760c2d9832a06ee35f517938a20
  React-RCTRuntime: 9669e40cc60ac65f09e75730e925c4a6a66debd4
  React-RCTSettings: e0e140b2ff4bf86d34e9637f6316848fc00be035
  React-RCTText: 75915bace6f7877c03a840cc7b6c622fb62bfa6b
  React-RCTVibration: 25f26b85e5e432bb3c256f8b384f9269e9529f25
  React-rendererconsistency: 51d190444383ec3b4c87321c700d1f6513c751c9
  React-renderercss: 876db927db3e30259c47be13bfa36d2c646b6c13
  React-rendererdebug: c1056f8c458501ef6022a076d47bcc9fd1a84b01
  React-RuntimeApple: 0766031d217ff53b8c5bb3a90a114c698890ccbb
  React-RuntimeCore: b3c96e5eaa457d8f5680e7986d8694a8329bcfea
  React-runtimeexecutor: d1f52523b0b66051e22be1aabd8dd5de1dbf5eef
  React-RuntimeHermes: 2396631900bdb9fb05667de66345365d0d394e2b
  React-runtimescheduler: 2032e7fa094a7494809b478ef4434588b5ab992a
  React-timing: 32f695c27e3896d6f1bef77de042781b1bc999b3
  React-utils: 9f498dae9ce2da37772ab680a37ba516a4ea0a7e
  ReactAppDependencyProvider: 1bcd3527ac0390a1c898c114f81ff954be35ed79
  ReactCodegen: 9a5f0d4c80fe9bb1ff29309a76392825aabe68c7
  ReactCommon: 08810150b1206cc44aecf5f6ae19af32f29151a8
  ReactNativeDependencies: 71ce9c28beb282aa720ea7b46980fff9669f428a
  RevenueCat: 1e61140a343a77dc286f171b3ffab99ca09a4b57
  RevenueCatUI: 625b2b2f4ee8c052d2e9939c2b906cdaecded253
  RNCAsyncStorage: c8407cc394627a3a4cf56b457d594bfa822b6ab8
  RNCMaskedView: ee2fc4739eb35be50bcf4f95daca19f316b5d50c
  RNGestureHandler: c3831b3b1b5014c6afab7fff7e4171394a3419ab
  RNPaywalls: d9f17bc5cab8555a7c0b395f5c0bdc3256b42c2a
  RNPurchases: 5f3cd4fea5ef2b3914c925b2201dd5cecd31922f
  RNReanimated: 2a5413dfcf429c7c9ce3f0030e3b7a010c2f9565
  RNScreens: 33bbd1d0f86ed957557620dc94054b71db0a94ee
  RNSoundLevel: a48da8668fbde4f792c0bea948e27ab793bee7e1
  RNSVG: ac3047fdd9c0ebac9d0b731a352bddf216af6242
  RNVectorIcons: 1db72384402bc567e995855056b8c5d7a9888780
  RNWorklets: 76fce72926e28e304afb44f0da23b2d24f2c1fa0
  SelectableText: 027d256340fbca04e5b5b95904f75302459a88de
  Yoga: 65431d43277b661ebc7289c6c5d82f64ce42cfe0

PODFILE CHECKSUM: 19257a90c1108320bf652a885989f1815b4fa281

COCOAPODS: 1.16.2
