{"common": {"cancel": "Annuler", "save": "Enregistrer", "done": "OK", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "O<PERSON>", "no": "Non", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "search": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "send": "Envoyer", "back": "Retour", "new": "Nouveau", "create": "<PERSON><PERSON><PERSON>"}, "auth": {"signIn": "Se connecter", "signOut": "Déconnexion", "signUp": "S'inscrire", "logIn": "Se connecter", "signingIn": "Connexion...", "sending": "Envoi...", "email": "Email", "emailAddress": "<PERSON><PERSON><PERSON> email", "emailPlaceholder": "Email", "password": "Mot de passe", "passwordPlaceholder": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "confirmPasswordPlaceholder": "Confirmer le mot de passe", "forgotPassword": "Mot de passe oublié?", "resetPassword": "Réinitialiser", "resetPasswordDescription": "Entre ton email et on t'enverra un lien de réinitialisation", "sendResetLink": "Envoyer le lien", "checkYourEmail": "Regarde tes emails", "resetLinkSent": "On t'a envoyé un lien à", "didntReceiveEmail": "Pas reçu l'email? Appuie pour renvoyer", "backToSignIn": "Retour au login", "magicLinkSent": "On t'a envoyé un magic link. Clique dessus dans ton email pour te connecter.", "magicLinkDescription": "Entre ton email et on t'enverra un magic link pour te connecter", "sendMagicLink": "Envoyer magic link", "resendLink": "Renvoyer le lien", "magicLinkFailed": "Échec de l'envoi du magic link", "termsRequired": "Veuillez accepter les termes et conditions", "continueWith": "Continuer avec", "continueWithApple": "Continuer avec Apple", "continueWithGoogle": "Continuer avec Google", "continueWithEmail": "Continuer avec Email", "signInWithEmail": "Se connecter avec email", "signInWithApple": "Se connecter avec Apple", "signInWithGoogle": "Se connecter avec Google", "orEmail": "ou email", "or": "ou", "orContinueWith": "Ou continuer avec", "alreadyHaveAccount": "Déjà un compte?", "dontHaveAccount": "Pas encore de compte?", "tapToContinue": "Appuie pour continuer", "drawer": {"defaultTitle": "Create an Account", "defaultMessage": "Sign up or log in to continue", "signInToChat": "Sign in to <PERSON><PERSON>", "signInToChatMessage": "Create an account or log in to start chatting with AI workers.", "signUpUnlock": "Create an Account", "signUpUnlockMessage": "Sign up to unlock full access to AI workers and features.", "welcomeBack": "Welcome Back", "welcomeBackMessage": "Log in to continue using AI workers and features.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "Invi<PERSON>", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}, "signOutConfirm": "Vraiment te déconnecter?", "createFreeAccount": "<PERSON><PERSON><PERSON> un compte gratuit", "createAccount": "<PERSON><PERSON><PERSON> un compte", "enterEmailPassword": "Entre ton email et mot de passe", "enterEmailAddress": "Entre ton adresse email", "signInFailed": "Connexion échouée. Vérifie tes identifiants.", "signUpFailed": "Inscription échouée. Réessaie.", "resetFailed": "Impossible d'envoyer l'email. Réessaie.", "passwordsDontMatch": "Les mots de passe ne correspondent pas", "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères", "welcomeTitle": "Engager Kortix pour", "rotatingPhrases": {"presentations": "présentations", "writing": "écriture", "emails": "emails", "research": "recherche", "planning": "planification", "studying": "études", "anything": "n'importe quoi."}, "agreeTerms": "J'accepte les", "userTerms": "Conditions d'Utilisation", "acknowledgePrivacy": "et reconnais la", "privacyNotice": "Politique de confidentialité", "confirmationEmailSent": "Lien de confirmation envoy<PERSON> à", "openEmailAppBtn": "Ouvrir l'App Email", "openGmailBtn": "Ouvrir l'App Gmail", "validationErrors": {"emailPasswordRequired": "<PERSON><PERSON><PERSON> d'entrer l'email et le mot de passe", "passwordsNoMatch": "Les mots de passe ne correspondent pas", "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères"}}, "onboarding": {"skip": "Passer", "next": "Suivant", "getStarted": "Commencer", "slide1": {"title": "Chat avec IA", "description": "Aie des conversations naturelles avec des assistants IA puissants qui comprennent le contexte et fournissent des réponses utiles"}, "slide2": {"title": "Ultra Rapide", "description": "Obtiens des réponses instantanées avec les derniers modèles d'IA. Ton assistant est toujours prêt à aider"}, "slide3": {"title": "Sécurisé et Privé", "description": "Tes conversations sont chiffrées et sécurisées. On respecte ta vie privée et ne partage jamais tes données"}, "slide4": {"title": "Automatiser les Tâches", "description": "Configure des triggers et workers pour automatiser tes workflows et booster ta productivité"}, "slides": {"title": "Générer des Présentations", "description": "Crée des présentations professionnelles en quelques secondes. Des pitch decks aux rapports business.", "example": "Crée un pitch deck pour ma startup"}, "research": {"title": "Recherche Approfondie", "description": "Recherche web complète avec insights instantanés, résumés et analyses.", "example": "Recherche les tendances IA en santé"}, "data": {"title": "<PERSON><PERSON><PERSON>", "description": "Analyse des données, crée des visualisations et génère de l'intelligence actionnable.", "example": "Analyse les données de ventes trimestrielles"}, "docs": {"title": "Création de Documents", "description": "Génère des rapports, rédige des emails et crée n'importe quel document avec l'assistance IA.", "example": "Rédige une proposition de projet"}, "automation": {"title": "Automatisation Intelligente", "description": "Configure des triggers pour automatiser les workflows. Programme des tâches et surveille les événements.", "example": "Envoyer des rapports quotidiens à 9h"}, "superworker": {"title": "Ton Super Worker IA", "description": "Combine toutes les fonctionnalités pour construire des workers IA personnalisés pour n'importe quelle tâche dont tu as besoin.", "example": "<PERSON><PERSON>er un agent de support client"}}, "settings": {"title": "Paramètres", "name": "Nom", "billing": "Facturation", "integrations": "Intégrations", "theme": "Thème & Icône", "language": "<PERSON><PERSON>", "advancedFeatures": "Fonctionnalités avancées", "signOut": "Déconnexion", "upgrade": "Passer à Pro", "deleteAccount": "<PERSON><PERSON><PERSON><PERSON> le Compte", "beta": "Fonctionnalités Beta", "unlockFeatures": "Débloquer toutes les fonctionnalités"}, "chat": {"newChat": "Nouveau Chat", "placeholder": "Donne une tâche à Kortix...", "inputPlaceholder": "Demande à Kortix de faire n'importe quoi...", "recording": "Enregistrement...", "cancelRecording": "Annuler", "stopRecording": "<PERSON><PERSON><PERSON><PERSON>", "threadTitle": "<PERSON><PERSON><PERSON> du chat", "editTitle": "Modifier le titre", "noMessages": "Pas encore de messages", "startConversation": "Commence une conversation", "taskCompleted": "Tâche terminée", "howWasThisResult": "Comment était ce résultat ?", "rateThisResult": "Évaluer ce résultat", "feedbackHelpsImprove": "Vos commentaires nous aident à nous améliorer", "additionalFeedbackOptional": "Commentaire supplémentaire (optionnel)", "helpKortixImprove": "Aidez Kortix à s'améliorer avec mes commentaires", "submit": "Envoyer", "submitting": "Envoi en cours...", "suggestedFollowUps": "Suggestions de suivi", "error": "<PERSON><PERSON><PERSON>", "feedbackSubmitFailed": "Échec de l'envoi du commentaire", "feedbackSubmitFailedRetry": "Échec de l'envoi du commentaire. Veuillez réessayer."}, "agents": {"selectAgent": "Choisir un Worker", "chooseAgent": "<PERSON><PERSON> un worker", "createAgent": "<PERSON><PERSON><PERSON> un Worker Perso", "newWorker": "Nouveau Worker", "myWorkers": "Mes Workers", "searchAgents": "Rechercher workers", "defaultAgent": "Kortix", "defaultDescription": "IA polyvalente pour toutes les tâches", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "Expert en écriture et débogage de code", "presenter": "Presenter", "presenterDescription": "Crée des présentations et slides stylés", "developer": "Developer", "developerDescription": "Spécialiste développement full-stack", "support": "Support", "supportDescription": "Expert en service client et support", "analyst": "Analyst", "analystDescription": "Spécialiste analyse de données et insights", "creative": "Creative", "creativeDescription": "Création de contenu et écriture créative", "researcher": "Researcher", "researcherDescription": "Expert en recherche et collecte d'infos"}, "quickActions": {"image": "Image", "slides": "Slides", "data": "Data", "docs": "Docs", "people": "Gens", "summarizePlaceholder": "Que veux-tu résumer?", "translatePlaceholder": "Que veux-tu traduire?", "explainPlaceholder": "Que veux-tu que j'explique?", "research": "Research", "chooseStyle": "<PERSON><PERSON> le style {{action}}", "imageStyles": {"abstract": "Abstrait", "anime": "Anime", "comic": "BD", "digital-art": "Digital", "geometric": "Géométrique", "impressionist": "Impressionniste", "isometric": "Isométrique", "minimalist": "Minimaliste", "neon": "Néon", "oil-painting": "Peinture à l'huile", "pastel": "Pastel", "photorealistic": "Photo", "surreal": "Surréaliste", "vintage": "Vintage", "watercolor": "<PERSON><PERSON><PERSON><PERSON>"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "Charts", "table": "Tableaux", "pie-chart": "<PERSON><PERSON><PERSON>", "line-graph": "<PERSON><PERSON><PERSON>", "statistics": "Stats", "comparison": "Comparaison", "trends": "Tendances", "summary": "Résumé"}, "documentTypes": {"essay": "Essay", "letter": "<PERSON><PERSON>", "report": "Report", "email": "Email", "article": "Article", "notes": "Notes", "blog-post": "Blog Post", "summary": "Résumé"}, "peopleTypes": {"expert": "Expert", "colleague": "<PERSON><PERSON><PERSON><PERSON>", "contact": "Contact", "team": "Team", "partner": "Partenaire", "influencer": "Influenceur", "mentor": "Mentor", "advisor": "Conseiller"}, "researchSources": {"academic": "Académique", "scientific": "Scientifique", "news": "News", "web": "Web", "books": "Livres", "articles": "Articles", "papers": "Papers", "database": "Base de données"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "Nouveau Chat", "newWorker": "Nouveau Worker", "newTrigger": "Nouveau Trigger", "searchConversations": "Rechercher chats", "noConversations": "Pas encore de chats", "startNewChat": "Commence un nouveau chat", "conversations": "Chats", "home": "Home", "settings": "Paramètres", "profile": "Profil"}, "languages": {"title": "<PERSON><PERSON>", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "Pièces jointes", "addAttachment": "Ajouter une pièce jointe", "chooseAttachment": "Qu'est-ce que tu veux joindre?", "removeAttachment": "<PERSON><PERSON><PERSON><PERSON>", "takePicture": "Prendre une Photo", "takePictureDescription": "Utiliser l'appareil photo", "chooseImages": "Choisir des Images", "chooseImagesDescription": "Sélectionner depuis ta galerie", "chooseFiles": "Choisir des Fichiers", "chooseFilesDescription": "Sélectionner documents ou autres fichiers", "photo": "Photo", "document": "Document", "audio": "Audio", "cameraPermissionRequired": "Autorisation Caméra Requise", "cameraPermissionMessage": "Active l'accès à la caméra dans tes paramètres.", "photosPermissionRequired": "Autorisation Photos Requise", "photosPermissionMessage": "Active l'accès aux photos dans tes paramètres.", "failedToOpenCamera": "Impossible d'ouvrir la caméra. Réessaie.", "failedToOpenImagePicker": "Impossible d'ouvrir la galerie. Réessaie.", "failedToOpenFilePicker": "Impossible d'ouvrir le sélecteur. Réessaie.", "fileTooLarge": "Le fichier dépasse la taille maximale autorisée.", "uploadFailed": "Échec du téléchargement des fichiers. Veuillez réessayer."}, "permissions": {"cameraTitle": "Autorisation Caméra Requise", "cameraMessage": "Veuillez activer l'accès à la caméra dans vos paramètres pour prendre des photos.", "galleryTitle": "Autorisation Galerie Requise", "galleryMessage": "Veuillez activer l'accès à la galerie dans vos paramètres pour choisir des images."}, "audio": {"transcriptionFailed": "Échec de la transcription audio. Veuillez réessayer."}, "billing": {"title": "Facturation & Abonnement", "currentPlan": "Plan Actuel", "credits": "Crédits", "remaining": "restants", "totalAvailableCredits": "Total de Crédits Disponibles", "upgrade": "<PERSON><PERSON><PERSON><PERSON>", "topUp": "Recharger", "creditBreakdown": "Détail des Crédits", "renewsInDays": "Se renouvelle dans {{days}}j", "noRenewal": "Pas de renouvellement", "extra": "Extra", "nextBilling": "Prochaine Facturation", "changePlan": "Changer de Plan", "upgradeOrDowngrade": "<PERSON><PERSON><PERSON><PERSON> ou ré<PERSON>grader", "billingPortal": "Portail de Facturation", "paymentMethods": "Méthodes de paiement", "annualCommitment": "Engagement Annuel", "activeUntil": "Actif jusqu'au {{date}}", "subscriptionCancelled": "Abonnement Annulé", "subscriptionCancelledOn": "Ton abonnement sera annulé le {{date}}", "reactivate": "<PERSON><PERSON><PERSON><PERSON>", "reactivating": "Réactivation...", "howCreditsWork": "Comment fonctionnent les crédits", "learnAboutCredits": "Apprendre sur l'utilisation des crédits", "cancelPlan": "Annuler le Plan", "usage": "Utilisation", "last30Days": "30 derniers j", "failedToLoad": "Échec du chargement des données d'abonnement", "buyCredits": "Acheter des Crédits", "availableCredits": "Crédits disponibles", "creditPackages": "Paquets de Crédits", "choosePackageBoost": "Choisis un paquet et augmente ton solde de crédits", "ranOutOfCredits": "Tu n'as plus de crédits. Améliore maintenant.", "choosePlan": "Choisis ton Plan", "getStarted": "Commencer", "selectPlan": "Sélectionner le Plan", "upgradeNow": "<PERSON><PERSON><PERSON>rer Maintenant", "downgrade": "Rétrograder", "switchPlan": "Changer de Plan", "monthly": "<PERSON><PERSON><PERSON>", "yearlyCommitment": "<PERSON><PERSON>", "save15Percent": "Économise 15%", "mostPopular": "Le Plus Populaire", "perMonth": "/mois", "billedYearly": "facturé annuellement", "billedMonthly": "facturé mensuellement", "creditsIncluded": "{{amount}} crédits/mois", "unlimitedCredits": "Crédits illimités", "everything": "Tout dans {{plan}}", "plus": "plus", "addCredits": "Ajouter des Crédits", "needMoreCredits": "Besoin de plus de crédits?", "purchaseAdditional": "Achete des crédits supplémentaires à tout moment"}, "theme": {"title": "Apparence", "preview": "<PERSON><PERSON><PERSON><PERSON>", "themeOptions": "Options de Thème", "light": "<PERSON>", "dark": "Sombre", "system": "Système", "lightDescription": "Toujours utiliser le thème clair", "darkDescription": "Toujours utiliser le thème sombre", "systemDescription": "Suivre les paramètres de l'appareil", "lightMode": "<PERSON>", "darkMode": "Mode Sombre"}, "language": {"title": "<PERSON><PERSON>", "selectLanguage": "Sélectionner la Langue"}, "beta": {"title": "Fonctionnalités Bêta", "experimentalFeatures": "Fonctionnalités Expérimentales", "earlyAccess": "Accède en avant-première aux nouvelles fonctionnalités", "advancedFeatures": "Fonctionnalités Avancées", "advancedDescription": "Accède aux fonctionnalités expérimentales et outils avancés avant leur sortie officielle", "whatsIncluded": "Ce qui est Inclus", "experimentalTools": "Outils Expérimentaux", "experimentalToolsDescription": "Teste de nouvelles fonctionnalités avant la sortie officielle", "advancedSettings": "Paramètres Avancés", "advancedSettingsDescription": "Personnalise ton expérience avec des options d'utilisateur avancé", "pleaseNote": "Note <PERSON><PERSON>", "betaWarning": "Les fonctionnalités bêta peuvent être instables et changer sans préavis. À utiliser à tes risques et périls."}, "accountDeletion": {"title": "<PERSON><PERSON><PERSON><PERSON> le Compte", "deletionScheduled": "Suppression Programmée", "scheduledFor": "<PERSON><PERSON><PERSON>", "deleteYourAccount": "Supprimer ton Compte", "actionCannotBeUndone": "Cette action ne peut pas être annulée", "accountWillBeDeleted": "Ton compte sera définitivement supprimé", "whatWillBeDeleted": "Ce qui sera Supprimé", "allAgents": "Tous tes agents et versions d'agents", "allThreads": "Tous tes fils et conversations", "allCredentials": "Toutes tes identifiants et intégrations", "subscriptionData": "Ton abonnement et données de facturation", "gracePeriod": "Période de Grâce de 30 Jours", "gracePeriodDescription": "Ton compte sera programmé pour suppression dans 30 jours. Tu peux annuler cette demande à tout moment pendant la période de grâce.", "typeDeleteToConfirm": "Tape {{text}} pour confirmer", "deletePlaceholder": "SUPPRIMER", "deleteAccount": "<PERSON><PERSON><PERSON><PERSON> le Compte", "cancelDeletion": "Annuler la Suppression", "processing": "Traitement...", "deletionScheduledSuccess": "Ton compte sera supprimé dans 30 jours. Tu peux annuler cette demande à tout moment.", "cancelDeletionTitle": "Annuler la Suppression de Compte", "cancelDeletionDescription": "Veux-tu annuler la suppression de ton compte? Ton compte et toutes les données seront préservés.", "back": "Retour", "deletionCancelled": "Suppression Annulée", "deletionCancelledSuccess": "Ton compte est en sécurité. La suppression a été annulée.", "failedToRequest": "Échec de la demande de suppression de compte", "failedToCancel": "Échec de l'annulation de suppression de compte", "cancelRequestDescription": "Tu peux annuler cette demande à tout moment avant la date de suppression. Toutes tes données seront préservées si tu annules."}, "models": {"selectModel": "<PERSON><PERSON> un Modèle", "chooseModelFor": "Choisis un modèle pour {{agent}}"}, "placeholders": {"default": "Donne une tâche à Kortix...", "search": "Rechercher...", "searchConversations": "Rechercher chats", "searchAgents": "Rechercher workers", "imageGeneration": "Décris l'image que tu veux créer...", "imageWithStyle": "Décris ton image {{style}}...", "slidesGeneration": "Quelle présentation tu veux?", "slidesWithTemplate": "Décris ta présentation {{template}}...", "dataAnalysis": "Quelle analyse de données tu veux?", "documentCreation": "Quel document tu veux?", "peopleSearch": "Qui tu cherches?", "researchQuery": "Qu'est-ce que tu veux rechercher?"}, "emptyStates": {"noConversations": "Pas encore de Cha<PERSON>", "noConversationsDescription": "Commence un nouveau Chat", "noWorkers": "Pas encore de Workers", "noWorkersDescription": "C<PERSON>e ton premier Worker", "triggers": "Triggers", "triggersDescription": "<PERSON><PERSON>ggers d'automatisation apparaîtront ici", "noResults": "Aucun résultat", "tryDifferentSearch": "Essaie un autre terme"}, "actions": {"goBack": "Retour", "returnToHome": "Retour au home", "createNew": "<PERSON><PERSON>er un nouveau {{item}}", "openConversation": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{title}}"}, "usage": {"title": "Utilisation", "totalCreditsUsed": "Total de Crédits Utilisés", "loadingUsageData": "Chargement des données...", "failedToLoad": "Échec du chargement des données", "usageStats": "Stats d'Utilisation", "conversations": "Conversations", "avgPerChat": "Moy. par <PERSON>", "usageTrend": "Tendance d'Utilisation", "lastConversations": "Dernières {{count}} conversations", "conversationBreakdown": "Détail des Conversations", "noConversationsYet": "Pas encore de conversations", "conversationHistoryAppearHere": "Ton historique apparaîtra ici", "showingTopOf": "Affichage de {{shown}} sur {{total}} conversations", "credits": "crédits", "upgradeYourPlan": "Améliore ton Plan", "upgradeDescription": "Obtiens plus de crédits et débloque des fonctionnalités premium", "topUp": "Recharger", "topUpDescription": "Recharge ton compte avec des crédits supplémentaires", "upgrade": "<PERSON><PERSON><PERSON><PERSON>", "upgradeToUltra": "Passer à Ultra", "upgradeToUltraDescription": "Obtiens le niveau le plus élevé pour un maximum de crédits"}, "nameEdit": {"title": "Modifier le Profil", "displayName": "Nom d'Affichage", "yourNamePlaceholder": "Ton nom", "emailAddress": "<PERSON><PERSON><PERSON>", "notAvailable": "Non disponible", "nameRequired": "Le nom est requis", "nameTooLong": "Le nom est trop long (max 100 caractères)", "saving": "Sauvegarde...", "saveChanges": "<PERSON><PERSON><PERSON><PERSON>", "nameUpdated": "Ton nom a été mis à jour avec succès", "failedToUpdate": "Échec de la mise à jour du nom. Réessaie."}, "integrations": {"title": "Intégrations", "description": "Connecte tes applications et services préférés pour automatiser les flux de travail et étendre les capacités de ton agent.", "connectApps": "Connecte tes Apps", "externalApps": "Applications Externes", "externalAppsDescription": "Connecter des applications et services populaires", "customMcpServers": "Serveurs MCP Personnalisés", "customMcpDescription": "Ajouter des intégrations HTTP personnalisées", "composioApps": "Applications Composio", "composioAppsDescription": "Se connecter à des centaines d'applications commerciales", "connected": "Connecté", "connectedToAgent": "Connecté à cet agent", "available": "Applications Disponibles", "connect": "Connecter", "setup": "Configurer", "manage": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher des intégrations...", "noConnected": "Aucune intégration connectée", "noConnectedDescription": "Connecte tes applications préférées pour commencer", "toolsEnabled": "{{count}} outils activés", "connectionSuccess": "Connecté avec succès!", "connectionError": "Échec de la connexion", "connecting": "Connexion...", "disconnect": "Déconnecter", "noAppsFound": "Aucune application trouvée", "tryDifferentSearch": "Essaie un terme de recherche différent", "loadingIntegrations": "Chargement des intégrations...", "failedToLoad": "Échec du chargement", "retry": "<PERSON><PERSON><PERSON><PERSON>", "appDetails": {"developer": "Développeur", "tools": "Outils", "connections": "{{count}} connexion{{plural}} prête{{plural}}", "chooseHowToConnect": "Choisis comment te connecter", "noToolsFound": "<PERSON><PERSON><PERSON> outil trouvé", "toolsAvailableAfterSetup": "+{{count}} outils disponibles après config"}, "connector": {"connectTo": "Se connecter à {{app}}", "selectConnection": "Sélectionne une connexion ou crée-en une", "createFirstConnection": "Crée ta première connexion", "useExistingConnection": "Utiliser une Connexion Existante", "profilesConnected": "{{count}} profil{{plural}} connecté{{plural}}", "createNewConnection": "Créer une Nouvelle Connexion", "connectNewAccount": "Connecter un nouveau compte {{app}}", "cancel": "Annuler", "continue": "<PERSON><PERSON><PERSON>", "selectAnOption": "Sélectionne une option", "createNewProfile": "Créer un Nouveau Profil", "chooseNameForConnection": "Choisis un nom pour cette connexion", "profileName": "Nom du Profil", "profileNamePlaceholder": "Compte {{app}}", "nameAvailable": "Nom disponible", "nameAlreadyTaken": "Ce nom est déjà utilisé", "creating": "Création...", "completeInBrowser": "Compléter dans le Navigateur", "authenticateInstructions": "Authentifie-toi dans le navigateur, puis reviens ici", "reopenBrowser": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>", "completedAuthentication": "J'ai Terminé l'Authentification", "goBack": "Retour", "allSet": "Tout est <PERSON>rêt", "connectionReady": "Ta connexion {{app}} est prête", "createdOn": "<PERSON><PERSON><PERSON> {{date}}"}, "toolsSelector": {"configureTools": "Configurer les Outils {{app}}", "selectTools": "Sélectionne les outils pour ton agent", "readyToConfigureTools": "<PERSON><PERSON><PERSON><PERSON> à configurer", "selected": "{{count}} sur {{total}} sélectionné{{plural}}", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "loadingTools": "Chargement des outils...", "failedToLoadTools": "Échec du Chargement", "noToolsAvailable": "Aucun Outil Disponible", "noToolsDescription": "Cette intégration n'a pas d'outils", "addingTools": "A<PERSON>t des Outils...", "addTool": "Ajouter {{count}} Outil", "addTools": "Ajouter {{count}} Outils", "toolsAddedSuccess": "{{count}} outils {{app}} ajoutés!", "failedToSaveTools": "Échec de la sauvegarde."}, "customMcp": {"title": "MCP <PERSON>", "description": "Se connecter à un serveur de protocole de contrôle de modèle personnalisé", "serverUrl": "URL du Serveur", "serverUrlPlaceholder": "https://votre-serveur-mcp.com", "serverName": "Nom du Serveur", "serverNamePlaceholder": "Mon Serveur MCP Personnalisé", "discoveringTools": "Découverte des Outils...", "discoverTools": "Découvrir les Outils", "enterValidUrl": "Veuillez entrer une URL HTTP ou HTTPS valide.", "enterServerName": "Veuillez entrer un nom pour ce serveur MCP.", "noToolsFound": "Aucun outil trouvé. Veuillez vérifier votre configuration.", "failedToConnect": "Échec de la connexion au serveur MCP. Veuillez vérifier votre configuration.", "toolsConfigured": "MCP <PERSON><PERSON><PERSON><PERSON>", "toolsConfiguredMessage": "{{count}} outils configurés"}}}