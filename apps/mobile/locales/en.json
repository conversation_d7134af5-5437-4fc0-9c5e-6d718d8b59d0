{"common": {"cancel": "Cancel", "save": "Save", "saving": "Saving...", "success": "Success", "done": "Done", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "delete": "Delete", "edit": "Edit", "search": "Search", "loading": "Loading...", "error": "Error", "retry": "Try again", "send": "Send", "back": "Back", "new": "New", "create": "Create", "continue": "Continue"}, "auth": {"signIn": "Sign in", "signOut": "Sign out", "signUp": "Sign up", "logIn": "Log in", "signingIn": "Signing in...", "sending": "Sending...", "email": "Email", "emailAddress": "Email address", "emailPlaceholder": "Email", "password": "Password", "passwordPlaceholder": "Password", "confirmPassword": "Confirm your password", "confirmPasswordPlaceholder": "Confirm your password", "fullNamePlaceholder": "Full name (optional)", "forgotPassword": "Forgot password?", "resetPassword": "Reset password", "resetPasswordDescription": "Enter your email and we'll send you a reset link", "sendResetLink": "Send link", "checkYourEmail": "Check your email", "resetLinkSent": "We sent a reset link to", "didntReceiveEmail": "Didn't get the email? Tap to resend", "backToSignIn": "Back to sign in", "magicLinkSent": "We sent you a magic link. Click your inbox.", "magicLinkDescription": "Enter your email and we'll send you a magic link to sign in", "sendMagicLink": "Send magic link", "resendLink": "Resend link", "magicLinkFailed": "Failed to send magic link", "termsRequired": "Please accept the terms and conditions", "continueWith": "Continue with", "continueWithApple": "Continue with Apple", "continueWithGoogle": "Continue with Google", "continueWithEmail": "Continue with <PERSON>ail", "signInWithEmail": "Sign in with email", "signUpWithEmail": "Sign up with email", "signInWithApple": "Sign in with Apple", "signInWithGoogle": "Sign in with Google", "orEmail": "or email", "or": "or", "orContinueWith": "Or continue with", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "tapToContinue": "Tap to continue", "signOutConfirm": "Sure you want to sign out?", "createFreeAccount": "Create free account", "createAccount": "Create account", "creatingAccount": "Creating account...", "enterEmailPassword": "Enter email and password", "enterEmailAddress": "Enter your email address", "enterEmail": "Enter your email", "chooseAction": "Log in or Sign up?", "signInFailed": "Sign in failed. Check your credentials.", "signUpFailed": "Sign up failed. Please try again.", "resetFailed": "Couldn't send email. Try again.", "passwordsDontMatch": "Passwords don't match", "passwordTooShort": "Password must be at least 8 characters", "bySigningUp": "By signing up, you agree to our", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "verificationEmailSent": "We've sent a verification link to", "openEmailApp": "Open email app", "goToSignIn": "Go to sign in", "verifyEmailInstructions": "Click the link in the email to verify your account, then sign in.", "welcomeTitle": "<PERSON><PERSON> for", "rotatingPhrases": {"presentations": "presentations", "writing": "writing", "emails": "emails", "research": "research", "planning": "planning", "studying": "studying", "anything": "anything."}, "agreeTerms": "I agree with", "userTerms": "User Terms And Conditions", "acknowledgePrivacy": "and acknowledge the", "privacyNotice": "Privacy notice", "confirmationEmailSent": "Confirmation link sent to", "openEmailAppBtn": "Open Email App", "openGmailBtn": "Open Gmail App", "browseAsGuest": "<PERSON><PERSON>e as Guest", "validationErrors": {"emailRequired": "Please enter a valid email address", "emailPasswordRequired": "Please enter both email and password", "passwordsNoMatch": "Passwords don't match", "passwordTooShort": "Password must be at least 8 characters"}, "drawer": {"defaultTitle": "Welcome to Kortix", "defaultMessage": "The AI Super Mega Giga app.", "signInToChat": "Welcome to Kortix", "signInToChatMessage": "The AI Super Mega Giga app.", "signUpUnlock": "Welcome to Kortix", "signUpUnlockMessage": "The AI Super Mega Giga app.", "welcomeBack": "Welcome to Kortix", "welcomeBackMessage": "The AI Super Mega Giga app.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "Guest", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}}, "onboarding": {"skip": "<PERSON><PERSON>", "next": "Next", "getStarted": "Get Started", "slide1": {"title": "Chat with AI", "description": "Have natural conversations with powerful AI assistants that understand context and provide helpful responses"}, "slide2": {"title": "Lightning Fast", "description": "Get instant responses powered by the latest AI models. Your assistant is always ready to help"}, "slide3": {"title": "Secure & Private", "description": "Your conversations are encrypted and secure. We respect your privacy and never share your data"}, "slide4": {"title": "Automate Tasks", "description": "Set up triggers and workers to automate your workflows and boost productivity"}, "slides": {"title": "Generate Presentations", "description": "Create professional presentations in seconds. From pitch decks to business reports.", "example": "Create a pitch deck for my startup"}, "research": {"title": "Deep Research", "description": "Comprehensive web research with instant insights, summaries, and analysis.", "example": "Research AI trends in healthcare"}, "data": {"title": "Data Analysis", "description": "Analyze data, create visualizations, and generate actionable intelligence.", "example": "Analyze quarterly sales data"}, "docs": {"title": "Document Creation", "description": "Generate reports, draft emails, and create any document with AI assistance.", "example": "Draft a project proposal"}, "automation": {"title": "Smart Automation", "description": "Set up triggers to automate workflows. Schedule tasks and monitor events.", "example": "Send daily reports at 9 AM"}, "superworker": {"title": "Your AI Superworker", "description": "Combine all features to build custom AI workers for any task you need.", "example": "Create a customer support agent"}}, "settings": {"title": "Settings", "name": "Name", "editName": "Edit Name", "updateName": "Update Name", "enterName": "Enter your name", "namePlaceholder": "Your name", "nameRequired": "Name is required", "nameUpdated": "Name updated successfully", "billing": "Billing", "integrations": "Integrations", "theme": "Theme & Icon", "themeTitle": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "currentTheme": "Current Theme", "language": "Language", "beta": "Beta", "betaFeatures": "Beta Features", "betaDescription": "Experimental features and advanced tools", "betaWarning": "Beta features may be unstable and could change without notice. Use at your own discretion.", "advancedFeatures": "Advanced Features", "signOut": "Sign out", "upgrade": "Upgrade to Pro", "deleteAccount": "Delete Account", "unlockFeatures": "Unlock all features"}, "chat": {"newChat": "New Chat", "placeholder": "Give Kort<PERSON> a task...", "inputPlaceholder": "Ask <PERSON><PERSON><PERSON> to do anything...", "recording": "Recording...", "cancelRecording": "Cancel recording", "stopRecording": "Stop recording", "threadTitle": "Chat title", "editTitle": "Edit title", "noMessages": "No messages yet", "startConversation": "Start a conversation", "taskCompleted": "Task completed", "howWasThisResult": "How was this result?", "rateThisResult": "Rate this result", "feedbackHelpsImprove": "Your feedback helps us improve", "additionalFeedbackOptional": "Additional feedback (optional)", "helpKortixImprove": "Help Kortix improve with my feedback", "submit": "Submit", "submitting": "Submitting...", "suggestedFollowUps": "Suggested follow-ups", "error": "Error", "feedbackSubmitFailed": "Failed to submit feedback", "feedbackSubmitFailedRetry": "Failed to submit feedback. Please try again."}, "thresdPage": {"threadActions": "Thread Actions", "shareThread": "Share Thread", "manageFiles": "Manage Files", "deleteThread": "Delete Thread"}, "agents": {"selectAgent": "Select Worker", "chooseAgent": "Choose a worker", "createAgent": "Create Custom Worker", "newWorker": "New Worker", "myWorkers": "My Workers", "searchAgents": "Search workers", "defaultAgent": "Kortix", "defaultDescription": "General purpose AI for all tasks", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "Expert in writing and debugging code", "presenter": "Presenter", "presenterDescription": "Creates engaging presentations and slides", "developer": "Developer", "developerDescription": "Full-stack development specialist", "support": "Support", "supportDescription": "Customer service and support expert", "analyst": "Analyst", "analystDescription": "Data analysis and insights specialist", "creative": "Creative", "creativeDescription": "Content creation and creative writing", "researcher": "Researcher", "researcherDescription": "Research and information gathering expert"}, "quickActions": {"image": "Image", "slides": "Slides", "data": "Data", "docs": "Docs", "people": "People", "research": "Research", "summarizePlaceholder": "What would you like to summarize?", "translatePlaceholder": "What would you like to translate?", "explainPlaceholder": "What would you like explained?", "chooseStyle": "Choose {{action}} style", "imageStyles": {"abstract": "Abstract", "anime": "Anime", "comic": "Comic", "digital-art": "Digital", "geometric": "Geometric", "impressionist": "Impressionist", "isometric": "Isometric", "minimalist": "Minimalist", "neon": "Neon", "oil-painting": "Oil Paint", "pastel": "Pastel", "photorealistic": "Photo", "surreal": "Surreal", "vintage": "Vintage", "watercolor": "Watercolor"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "Charts", "table": "Tables", "pie-chart": "Pie Chart", "line-graph": "Line Graph", "statistics": "Statistics", "comparison": "Compare", "trends": "Trends", "summary": "Summary"}, "documentTypes": {"essay": "Essay", "letter": "Letter", "report": "Report", "email": "Email", "article": "Article", "notes": "Notes", "blog-post": "Blog Post", "summary": "Summary"}, "peopleTypes": {"expert": "Expert", "colleague": "Colleague", "contact": "Contact", "team": "Team", "partner": "Partner", "influencer": "Influencer", "mentor": "Mentor", "advisor": "Advisor"}, "researchSources": {"academic": "Academic", "scientific": "Scientific", "news": "News", "web": "Web", "books": "Books", "articles": "Articles", "papers": "Papers", "database": "Database"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "New Chat", "newWorker": "New Worker", "newTrigger": "<PERSON> Trigger", "searchConversations": "Search chats", "noConversations": "No chats yet", "startNewChat": "Start a new chat", "conversations": "Chats", "home": "Home", "settings": "Settings", "profile": "Profile"}, "languages": {"title": "Select Language", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "Attachments", "addAttachment": "Add Attachment", "chooseAttachment": "What do you want to attach?", "removeAttachment": "Remove", "takePicture": "Take Picture", "takePictureDescription": "Use camera to take a photo", "chooseImages": "Choose <PERSON>", "chooseImagesDescription": "Select from your gallery", "chooseFiles": "<PERSON><PERSON>", "chooseFilesDescription": "Select documents or other files", "photo": "Photo", "document": "Document", "audio": "Audio", "cameraPermissionRequired": "Camera Permission Required", "cameraPermissionMessage": "Enable camera access in your settings.", "photosPermissionRequired": "Photos Permission Required", "photosPermissionMessage": "Enable photos access in your settings.", "failedToOpenCamera": "Couldn't open camera. Try again.", "failedToOpenImagePicker": "Couldn't open gallery. Try again.", "failedToOpenFilePicker": "Couldn't open file picker. Try again.", "fileTooLarge": "File size exceeds the maximum allowed size.", "uploadFailed": "Failed to upload files. Please try again."}, "permissions": {"cameraTitle": "Camera Permission Required", "cameraMessage": "Please enable camera access in your settings to take photos.", "galleryTitle": "Photo Library Permission Required", "galleryMessage": "Please enable photo library access in your settings to choose images."}, "audio": {"transcriptionFailed": "Failed to transcribe audio. Please try again."}, "models": {"selectModel": "Select Model", "chooseModelFor": "Choose a model for {{agent}}"}, "placeholders": {"default": "Give Kort<PERSON> a task...", "search": "Search...", "searchConversations": "Search chats", "searchAgents": "Search workers", "imageGeneration": "Describe the image you want to create...", "imageWithStyle": "Describe your {{style}} image...", "slidesGeneration": "What presentation do you need?", "slidesWithTemplate": "Describe your {{template}} presentation...", "dataAnalysis": "What data analysis do you need?", "documentCreation": "What document do you need?", "peopleSearch": "Who are you looking for?", "researchQuery": "What do you want to research?"}, "emptyStates": {"noConversations": "No Chats yet", "noConversationsDescription": "Start a new Chat", "noWorkers": "No Workers yet", "noWorkersDescription": "Create your first Worker", "triggers": "Triggers", "triggersDescription": "Your automation Triggers will appear here", "noResults": "No results", "tryDifferentSearch": "Try a different search term"}, "actions": {"goBack": "Go back", "returnToHome": "Return to home", "createNew": "Create new {{item}}", "openConversation": "Open Chat: {{title}}"}, "billing": {"title": "Billing & Subscription", "currentPlan": "Current Plan", "credits": "Credits", "creditsAvailable": "credits available", "creditsRemaining": "credits remaining", "upgradePlan": "Upgrade Plan", "choosePlan": "Choose Your Plan", "selectPlan": "Select Plan", "currentActive": "Current Plan", "startTrial": "Start 7-Day Free Trial", "starting": "Starting...", "trialActive": "Trial Active", "trialEnded": "Your trial has ended", "trialEnding": "Trial ending soon", "trialDaysRemaining": "{{days}} days remaining", "subscriptionRequired": "Subscription Required", "insufficientCredits": "Insufficient credits", "lowCredits": "Low credits", "lowCreditsWarning": "Your credit balance is low. Purchase more to continue using the service.", "purchaseCredits": "Purchase Credits", "manageBilling": "Manage Billing", "manageSubscription": "Manage Subscription", "monthlyPrice": "/month", "yearlyPrice": "/year", "billedMonthly": "billed monthly", "billedYearly": "billed yearly", "yearlyDiscount": "15% off", "savePerYear": "Save ${{amount}} per year", "neverExpires": "Never expires", "expires": "Expires {{date}}", "includedCredits": "{{amount}} included credits", "unlimitedCredits": "Unlimited", "usageLimitReached": "Usage limit reached", "upgradeRequired": "Upgrade required", "noSubscription": "No active subscription", "cancelledSubscription": "Subscription cancelled", "renewsOn": "Renews on {{date}}", "cancelledOn": "Cancelled on {{date}}", "endsOn": "Ends on {{date}}", "monthly": "Monthly", "yearly": "Yearly", "freeTrial": "7-Day Free Trial", "trialBenefits": "What's included:", "trialCredit": "$5 in Credits", "trialDuration": "7 Days Free", "trialFullAccess": "Full access to all AI models", "trialCancelAnytime": "Cancel anytime, no charge", "noChargeDuringTrial": "No charge during trial", "trialMessage": "Your card will only be charged after 7 days if you don't cancel. You can cancel anytime from billing settings.", "agreeToTerms": "By starting your trial, you agree to our Terms of Service and Privacy Policy", "packageAmount": "${{amount}}", "selectAmount": "Select Amount", "customAmount": "Custom Amount", "purchaseAmount": "Purchase ${{amount}}", "purchasing": "Processing...", "purchaseComplete": "Purchase complete!", "purchaseFailed": "Purchase failed", "checkoutCancelled": "Checkout cancelled", "returnToApp": "Return to app", "viewBilling": "View Billing", "balance": "Balance", "lifetime": "Lifetime", "expiring": "Expiring", "nonExpiring": "Non-expiring", "loading": "Loading billing information...", "error": "Failed to load billing information", "tryAgain": "Try again", "remaining": "remaining", "totalAvailableCredits": "Total Available Credits", "upgrade": "Upgrade", "topUp": "Top up", "creditBreakdown": "Credit Breakdown", "renewsInDays": "Renews in {{days}}d", "noRenewal": "No renewal", "extra": "Extra", "nextBilling": "Next Billing", "changePlan": "Change Plan", "upgradeOrDowngrade": "Upgrade or downgrade", "billingPortal": "Billing Portal", "paymentMethods": "Payment methods", "annualCommitment": "Annual Commitment", "activeUntil": "Active until {{date}}", "subscriptionCancelled": "Subscription Cancelled", "subscriptionCancelledOn": "Your subscription will be cancelled on {{date}}", "reactivate": "Reactivate", "reactivating": "Reactivating...", "howCreditsWork": "How credits work", "learnAboutCredits": "Learn about credit usage", "cancelPlan": "Cancel Plan", "usage": "Usage", "last30Days": "Last 30d", "failedToLoad": "Failed to load subscription data", "buyCredits": "Buy Credits", "availableCredits": "Available credits", "creditPackages": "Credit Packages", "choosePackageBoost": "Choose a package and boost your credit balance", "ranOutOfCredits": "You ran out of credits. Upgrade now.", "getStarted": "Get Started", "upgradeNow": "Upgrade Now", "downgrade": "Downgrade", "switchPlan": "Switch Plan", "yearlyCommitment": "Yearly", "save15Percent": "Save 15%", "mostPopular": "Most Popular", "perMonth": "/month", "creditsIncluded": "{{amount}} credits/month", "everything": "Everything in {{plan}}", "plus": "plus", "addCredits": "Add Credits", "needMoreCredits": "Need more credits?", "purchaseAdditional": "Purchase additional credits anytime"}, "theme": {"title": "Appearance", "preview": "Preview", "themeOptions": "Theme Options", "light": "Light", "dark": "Dark", "system": "System", "lightDescription": "Always use light theme", "darkDescription": "Always use dark theme", "systemDescription": "Follow device settings", "lightMode": "Light Mode", "darkMode": "Dark Mode"}, "language": {"title": "Language", "selectLanguage": "Select Language"}, "beta": {"title": "Beta Features", "experimentalFeatures": "Experimental Features", "earlyAccess": "Get early access to new capabilities", "advancedFeatures": "Advanced Features", "advancedDescription": "Access experimental features and advanced tools before they're released to everyone", "whatsIncluded": "What's Included", "experimentalTools": "Experimental Tools", "experimentalToolsDescription": "Try out new capabilities before official release", "advancedSettings": "Advanced Settings", "advancedSettingsDescription": "Fine-tune your experience with power user options", "pleaseNote": "Please Note", "betaWarning": "Beta features may be unstable and could change without notice. Use at your own discretion."}, "accountDeletion": {"title": "Delete Account", "deletionScheduled": "Deletion Scheduled", "scheduledFor": "Scheduled For", "deleteYourAccount": "Delete Your Account", "actionCannotBeUndone": "This action cannot be undone", "accountWillBeDeleted": "Your account will be permanently deleted", "whatWillBeDeleted": "What Will Be Deleted", "allAgents": "All your agents and agent versions", "allThreads": "All your threads and conversations", "allCredentials": "All your credentials and integrations", "subscriptionData": "Your subscription and billing data", "gracePeriod": "30-Day Grace Period", "gracePeriodDescription": "Your account will be scheduled for deletion in 30 days. You can cancel this request anytime during the grace period.", "typeDeleteToConfirm": "Type {{text}} to confirm", "deletePlaceholder": "DELETE", "deleteAccount": "Delete Account", "cancelDeletion": "Cancel Deletion", "processing": "Processing...", "deletionScheduledSuccess": "Your account will be deleted in 30 days. You can cancel this request anytime.", "cancelDeletionTitle": "<PERSON><PERSON> Account Deletion", "cancelDeletionDescription": "Do you want to cancel the deletion of your account? Your account and all data will be preserved.", "back": "Back", "deletionCancelled": "Deletion Cancelled", "deletionCancelledSuccess": "Your account is safe. The deletion has been cancelled.", "failedToRequest": "Failed to request account deletion", "failedToCancel": "Failed to cancel account deletion", "cancelRequestDescription": "You can cancel this request anytime before the deletion date. All your data will be preserved if you cancel."}, "integrations": {"title": "Integrations", "description": "Connect your favorite apps and services to automate workflows and extend your agent's capabilities.", "connectApps": "Connect your Apps", "externalApps": "External Apps", "externalAppsDescription": "Connect popular apps & services", "customMcpServers": "Custom MCP Servers", "customMcpDescription": "Add custom HTTP-based integrations", "composioApps": "Composio Apps", "composioAppsDescription": "Connect to hundreds of business applications and services", "connected": "Connected", "connectedToAgent": "Connected to this agent", "available": "Available Apps", "connect": "Connect", "setup": "Setup", "manage": "Manage", "searchPlaceholder": "Search integrations...", "noConnected": "No connected integrations", "noConnectedDescription": "Connect your favorite apps to get started", "toolsEnabled": "{{count}} tools enabled", "connectionSuccess": "Connected successfully!", "connectionError": "Connection failed", "connecting": "Connecting...", "disconnect": "Disconnect", "noAppsFound": "No apps found", "tryDifferentSearch": "Try a different search term", "loadingIntegrations": "Loading integrations...", "failedToLoad": "Failed to load apps", "retry": "Retry", "appDetails": {"developer": "Developer", "tools": "Tools", "connections": "{{count}} connection{{plural}} ready", "chooseHowToConnect": "Choose how to connect", "noToolsFound": "No tools found for this integration", "toolsAvailableAfterSetup": "+{{count}} more tools available after setup"}, "connector": {"connectTo": "Connect to {{app}}", "selectConnection": "Select a connection or create a new one", "createFirstConnection": "Create your first connection", "useExistingConnection": "Use Existing Connection", "profilesConnected": "{{count}} profile{{plural}} already connected", "createNewConnection": "Create New Connection", "connectNewAccount": "Connect a new {{app}} account", "cancel": "Cancel", "continue": "Continue", "selectAnOption": "Select an option", "createNewProfile": "Create New Profile", "chooseNameForConnection": "Choose a name for this connection", "profileName": "Profile Name", "profileNamePlaceholder": "{{app}} Account", "nameAvailable": "Name available", "nameAlreadyTaken": "This name is already taken", "creating": "Creating...", "completeInBrowser": "Complete in Browser", "authenticateInstructions": "Please authenticate in the browser window, then return here when finished", "reopenBrowser": "Reopen browser", "completedAuthentication": "I've Completed Authentication", "goBack": "Go Back", "allSet": "All Set", "connectionReady": "Your {{app}} connection is ready", "createdOn": "Created {{date}}"}, "toolsSelector": {"configureTools": "Configure {{app}} Tools", "selectTools": "Select tools to add to your agent", "readyToConfigureTools": "Ready to configure tools", "selected": "{{count}} of {{total}} selected", "selectAll": "Select All", "deselectAll": "Deselect All", "loadingTools": "Loading tools...", "failedToLoadTools": "Failed to Load Tools", "noToolsAvailable": "No Tools Available", "noToolsDescription": "This integration doesn't have any tools to configure", "addingTools": "Adding Tools...", "addTool": "Add {{count}} Tool", "addTools": "Add {{count}} Tools", "toolsAddedSuccess": "Added {{count}} {{app}} tools to your agent!", "failedToSaveTools": "Failed to save tools. Check console for details."}, "customMcp": {"title": "Custom MCP", "description": "Connect to a custom Model Control Protocol server", "serverUrl": "Server URL", "serverUrlPlaceholder": "https://your-mcp-server.com", "serverName": "Server Name", "serverNamePlaceholder": "My Custom MCP Server", "discoveringTools": "Discovering Tools...", "discoverTools": "Discover Tools", "enterValidUrl": "Please enter a valid HTTP or HTTPS URL.", "enterServerName": "Please enter a name for this MCP server.", "noToolsFound": "No tools found. Please check your configuration.", "failedToConnect": "Failed to connect to the MCP server. Please check your configuration.", "toolsConfigured": "Custom MCP Added", "toolsConfiguredMessage": "{{count}} tools configured"}}, "loading": {"threads": "Loading chats...", "triggers": "Loading triggers..."}, "errors": {"loadingThreads": "Failed to load chats", "loadingTriggers": "Failed to load triggers", "tryAgain": "Please try again later"}, "usage": {"title": "Usage", "totalCreditsUsed": "Total Credits Used", "loadingUsageData": "Loading usage data...", "failedToLoad": "Failed to load usage data", "usageStats": "Usage Stats", "conversations": "Conversations", "avgPerChat": "Avg per Chat", "usageTrend": "Usage Trend", "lastConversations": "Last {{count}} conversations", "conversationBreakdown": "Conversation Breakdown", "noConversationsYet": "No conversations yet", "conversationHistoryAppearHere": "Your conversation history will appear here", "showingTopOf": "Showing top {{shown}} of {{total}} conversations", "credits": "credits", "upgradeYourPlan": "Upgrade Your Plan", "upgradeDescription": "Get more credits and unlock premium features", "topUp": "Top Up", "topUpDescription": "Top up your account with additional credits", "upgrade": "Upgrade", "upgradeToUltra": "Upgrade to Ultra", "upgradeToUltraDescription": "Get the highest tier for maximum credits"}, "nameEdit": {"title": "Edit Profile", "displayName": "Display Name", "yourNamePlaceholder": "Your name", "emailAddress": "Email Address", "notAvailable": "Not available", "nameRequired": "Name is required", "nameTooLong": "Name is too long (max 100 characters)", "saving": "Saving...", "saveChanges": "Save Changes", "nameUpdated": "Your name has been updated successfully", "failedToUpdate": "Failed to update name. Please try again."}, "notifications": {"title": "Notifications", "description": "Manage how you receive notifications", "preferences": "Notification Preferences", "channels": "Notification Channels", "emailNotifications": "Email Notifications", "emailDescription": "Receive notifications via email", "pushNotifications": "Push Notifications", "pushDescription": "Get notified about agent runs, task completions, and important updates", "inAppNotifications": "In-App Notifications", "inAppDescription": "See notifications within the app", "deviceTokens": "Registered Devices", "noDevices": "No devices registered", "deviceRegistered": "This device is registered for push notifications", "registerDevice": "Register This Device", "unregisterDevice": "Unregister Device", "settingsUpdated": "Notification preferences updated", "settingsFailed": "Failed to update preferences", "deviceRegisteredSuccess": "Device registered successfully", "deviceRegistrationFailed": "Failed to register device", "deviceUnregisteredSuccess": "<PERSON>ce unregistered successfully", "deviceUnregistrationFailed": "Failed to unregister device", "permissionRequired": "Enable Notifications", "permissionMessage": "To receive notifications about your agent runs, task completions, and important updates, please enable notifications in your device settings.", "openSettings": "Open Settings", "enableAll": "Enable All", "disableAll": "Disable All"}}