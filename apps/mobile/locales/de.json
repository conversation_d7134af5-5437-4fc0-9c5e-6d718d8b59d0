{"common": {"cancel": "Abbrechen", "save": "Speichern", "done": "<PERSON><PERSON><PERSON>", "close": "Schließen", "ok": "OK", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "loading": "Lädt...", "error": "<PERSON><PERSON>", "retry": "Noch<PERSON> versuchen", "send": "Senden", "back": "Zurück", "new": "<PERSON>eu", "create": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"signIn": "Anmelden", "signOut": "Abmelden", "signUp": "Registrieren", "logIn": "Anmelden", "signingIn": "Wird angemeldet...", "sending": "Wird gesendet...", "email": "E-Mail", "emailAddress": "E-Mail-Adresse", "emailPlaceholder": "E-Mail", "password": "Passwort", "passwordPlaceholder": "Passwort", "confirmPassword": "Passwort bestätigen", "confirmPasswordPlaceholder": "Passwort bestätigen", "forgotPassword": "Passwort vergessen?", "resetPassword": "Passwort zurücksetzen", "resetPasswordDescription": "Gib deine E-Mail ein und wir schicken dir einen Link zum Zurücksetzen", "sendResetLink": "<PERSON> senden", "checkYourEmail": "Check deine E-Mails", "resetLinkSent": "Wir haben dir einen Link geschickt an", "didntReceiveEmail": "Keine E-Mail bekommen? Hier tippen zum erneut senden", "backToSignIn": "<PERSON><PERSON><PERSON> zum Login", "magicLinkSent": "Wir haben dir einen Magic Link geschickt. Klicke darauf in deiner E-Mail, um dich anzumelden.", "magicLinkDescription": "Gib deine E-Mail ein und wir schicken dir einen Magic Link zum Anmelden", "sendMagicLink": "Magic Link senden", "resendLink": "<PERSON> erneut senden", "magicLinkFailed": "Magic Link konnte nicht gesendet werden", "termsRequired": "Bitte akzeptiere die Nutzungsbedingungen", "continueWith": "<PERSON><PERSON> mit", "continueWithApple": "<PERSON>t <PERSON> fortfahren", "continueWithGoogle": "Mit Google fortfahren", "continueWithEmail": "Mit E-Mail fortfahren", "signInWithEmail": "Mit E-Mail anmelden", "signInWithApple": "Mit Apple anmelden", "signInWithGoogle": "Mit Google anmelden", "orEmail": "oder E-Mail", "or": "oder", "orContinueWith": "Oder weiter mit", "alreadyHaveAccount": "Schon einen Account?", "dontHaveAccount": "Noch keinen Account?", "tapToContinue": "Tippen um fortzu<PERSON>hren", "drawer": {"defaultTitle": "Create an Account", "defaultMessage": "Sign up or log in to continue", "signInToChat": "Sign in to <PERSON><PERSON>", "signInToChatMessage": "Create an account or log in to start chatting with AI workers.", "signUpUnlock": "Create an Account", "signUpUnlockMessage": "Sign up to unlock full access to AI workers and features.", "welcomeBack": "Welcome Back", "welcomeBackMessage": "Log in to continue using AI workers and features.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "Gas<PERSON>", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}, "signOutConfirm": "Wirklich abmelden?", "createFreeAccount": "Kostenlosen Account erstellen", "createAccount": "Account erstellen", "enterEmailPassword": "Bitte E-Mail und Passwort eingeben", "enterEmailAddress": "Bitte E-Mail-Ad<PERSON><PERSON> e<PERSON>ben", "signInFailed": "Login fehlgeschlagen. Check deine Zugangsdaten.", "signUpFailed": "Registrierung fehlgeschlagen. Versuch's nochmal.", "resetFailed": "Konnte E-Mail nicht senden. Versuch's nochmal.", "passwordsDontMatch": "Passwörter stimmen nicht überein", "passwordTooShort": "Passwort muss mindestens 8 Zeichen haben", "welcomeTitle": "<PERSON><PERSON> Ko<PERSON>", "rotatingPhrases": {"presentations": "Präsentationen", "writing": "Schreiben", "emails": "E-Mails", "research": "Recherche", "planning": "Planung", "studying": "<PERSON><PERSON><PERSON>", "anything": "alles."}, "agreeTerms": "Ich stimme den", "userTerms": "Nutzungsbedingungen", "acknowledgePrivacy": "zu und erkenne die", "privacyNotice": "Datenschutzerklärung", "confirmationEmailSent": "Bestätigungslink gesendet an", "openEmailAppBtn": "E-Mail-<PERSON><PERSON>", "openGmailBtn": "Gmail-<PERSON><PERSON>", "validationErrors": {"emailPasswordRequired": "Bitte E-Mail und Passwort eingeben", "passwordsNoMatch": "Passwörter stimmen nicht überein", "passwordTooShort": "Passwort muss mindestens 8 Zeichen haben"}}, "onboarding": {"skip": "Überspringen", "next": "<PERSON><PERSON>", "getStarted": "<PERSON> geht's", "slide1": {"title": "Chat mit KI", "description": "Führe natürliche Gespräche mit mächtigen KI-Assistenten, die den Kontext verstehen und hilfreiche Antworten geben"}, "slide2": {"title": "Blitzschnell", "description": "Erhalte sofortige Antworten mit den neuesten KI-Modellen. Dein Assistent ist immer bereit zu helfen"}, "slide3": {"title": "Sicher & Privat", "description": "Deine Gespräche sind verschlüsselt und sicher. Wir respektieren deine Privatsphäre und teilen niemals deine Daten"}, "slide4": {"title": "Aufgaben Automatisieren", "description": "<PERSON><PERSON> und Workers ein, um deine Workflows zu automatisieren und die Produktivität zu steigern"}, "slides": {"title": "Präsentationen Erstellen", "description": "Erstelle professionelle Präsentationen in Sekunden. Von Pitch Decks bis hin zu Business-Reports.", "example": "<PERSON><PERSON><PERSON> ein Pitch Deck für mein Startup"}, "research": {"title": "Tiefe Recherche", "description": "Umfassende Web-Recherche mit sofortigen Insights, Zusammenfassungen und Analysen.", "example": "Recherchiere KI-Trends im Gesundheitswesen"}, "data": {"title": "Datenanalyse", "description": "<PERSON><PERSON><PERSON><PERSON>, erstelle Visualisierungen und generiere umsetzbare Intelligenz.", "example": "Analy<PERSON>re quartalsweise Verkaufsdaten"}, "docs": {"title": "Dokument-Erstellung", "description": "Generiere Berichte, entwerfe E-Mails und erstelle beliebige Dokumente mit KI-Unterstützung.", "example": "Entwerfe einen Projektvorschlag"}, "automation": {"title": "Intelligente Automatisierung", "description": "<PERSON><PERSON> ein, um Workflows zu automatisieren. Plane Aufgaben und überwache Events.", "example": "Täglich um 9 Uhr Berichte senden"}, "superworker": {"title": "<PERSON><PERSON>-Superworker", "description": "Kombiniere alle Features, um maßgeschneiderte KI-Workers für jede Aufgabe zu erstellen, die du brauchst.", "example": "<PERSON><PERSON><PERSON> einen Kundenservice-Agenten"}}, "settings": {"title": "Einstellungen", "name": "Name", "billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "integrations": "Integrationen", "theme": "Theme & Icon", "language": "<PERSON><PERSON><PERSON>", "advancedFeatures": "Erweiterte Funktionen", "signOut": "Abmelden", "upgrade": "Zu Pro upgraden", "beta": "Beta", "deleteAccount": "Konto löschen", "unlockFeatures": "Alle Features freischalten"}, "chat": {"newChat": "<PERSON><PERSON><PERSON>", "placeholder": "Gib Kortix eine Aufgabe...", "recording": "Nimmt auf...", "cancelRecording": "Aufnahme abbrechen", "stopRecording": "<PERSON><PERSON>nah<PERSON> stoppen", "threadTitle": "Chat-<PERSON><PERSON>l", "editTitle": "Titel bearbeiten", "noMessages": "<PERSON>ch keine Nachrichten", "startConversation": "Starte eine Unterhaltung", "taskCompleted": "Aufgabe abgeschlossen", "howWasThisResult": "Wie war dieses <PERSON><PERSON><PERSON><PERSON>?", "rateThisResult": "<PERSON><PERSON> be<PERSON>ten", "feedbackHelpsImprove": "<PERSON><PERSON> hilft uns, uns zu verbessern", "additionalFeedbackOptional": "Zusätzliches Feedback (optional)", "helpKortixImprove": "<PERSON><PERSON> mit meine<PERSON>back, sich zu verbessern", "submit": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Wird gesendet...", "suggestedFollowUps": "Vorgeschlagene Nachfragen", "error": "<PERSON><PERSON>", "feedbackSubmitFailed": "Feedback konnte nicht übermittelt werden", "feedbackSubmitFailedRetry": "Feedback konnte nicht übermittelt werden. Bitte versuche es erneut."}, "agents": {"selectAgent": "<PERSON> w<PERSON>hlen", "chooseAgent": "<PERSON><PERSON><PERSON><PERSON> einen Worker aus", "createAgent": "Eigenen Worker erstellen", "newWorker": "<PERSON><PERSON>er Worker", "myWorkers": "Meine Workers", "searchAgents": "Worker suchen", "defaultAgent": "Kortix", "defaultDescription": "Allzweck-KI für alle Aufgaben", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "Experte für Code schreiben und debuggen", "presenter": "Presenter", "presenterDescription": "Erstellt coole Präsentationen und Folien", "developer": "Developer", "developerDescription": "Full-<PERSON><PERSON>-Spezialist", "support": "Support", "supportDescription": "Experte für Kundenservice und Support", "analyst": "Analyst", "analystDescription": "Spezialist für Datenanalyse und Insights", "creative": "Creative", "creativeDescription": "Content Creation und kreatives Schreiben", "researcher": "Researcher", "researcherDescription": "Experte für Recherche und Infos sammeln"}, "quickActions": {"image": "Bild", "slides": "Slides", "data": "Daten", "docs": "Docs", "people": "Le<PERSON>", "research": "Research", "chooseStyle": "{{action}}-<PERSON><PERSON> w<PERSON><PERSON><PERSON>", "imageStyles": {"abstract": "Abstrakt", "anime": "Anime", "comic": "Comic", "digital-art": "Digital", "geometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "impressionist": "Impressionistisch", "isometric": "Isometrisch", "minimalist": "Minimalistisch", "neon": "Neon", "oil-painting": "Ö<PERSON>mä<PERSON>", "pastel": "<PERSON><PERSON>", "photorealistic": "Foto", "surreal": "Surreal", "vintage": "Vintage", "watercolor": "<PERSON><PERSON><PERSON>"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "Charts", "table": "Tabellen", "pie-chart": "Kreisdiagramm", "line-graph": "Liniendiagramm", "statistics": "Statistiken", "comparison": "Vergleich", "trends": "Trends", "summary": "Zusammenfassung"}, "documentTypes": {"essay": "Essay", "letter": "Brief", "report": "Report", "email": "E-Mail", "article": "Artikel", "notes": "Notizen", "blog-post": "Blog-Post", "summary": "Zusammenfassung"}, "peopleTypes": {"expert": "Experte", "colleague": "Kollege", "contact": "Kontakt", "team": "Team", "partner": "Partner", "influencer": "Influencer", "mentor": "Mentor", "advisor": "Berater"}, "researchSources": {"academic": "Akademisch", "scientific": "Wissenschaftlich", "news": "News", "web": "Web", "books": "<PERSON><PERSON><PERSON>", "articles": "Artikel", "papers": "Papers", "database": "Datenbank"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "<PERSON><PERSON><PERSON>", "newWorker": "<PERSON><PERSON>er Worker", "newTrigger": "<PERSON><PERSON><PERSON>", "searchConversations": "<PERSON>ts durchsuchen", "noConversations": "<PERSON><PERSON> keine <PERSON>", "startNewChat": "Starte einen neuen Chat", "conversations": "Chats", "home": "Home", "settings": "Einstellungen", "profile": "Profil"}, "languages": {"title": "Sprache wählen", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "<PERSON><PERSON><PERSON><PERSON>", "addAttachment": "<PERSON><PERSON>", "chooseAttachment": "Was möchtest du anhängen?", "removeAttachment": "Entfernen", "takePicture": "Foto aufnehmen", "takePictureDescription": "<PERSON>t der Kamera ein Foto machen", "chooseImages": "Bilder auswählen", "chooseImagesDescription": "Aus deiner Galerie wählen", "chooseFiles": "<PERSON><PERSON> au<PERSON>wählen", "chooseFilesDescription": "Dokumente oder andere Dateien wählen", "photo": "Foto", "document": "Dokument", "audio": "Audio", "cameraPermissionRequired": "Kamera-<PERSON><PERSON><PERSON> n<PERSON>g", "cameraPermissionMessage": "Bitte erlaube den Kamera-Zugriff in deinen Einstellungen.", "photosPermissionRequired": "Fotos-<PERSON><PERSON><PERSON>", "photosPermissionMessage": "Bitte erlaube den Zugriff auf deine Fotos in den Einstellungen.", "failedToOpenCamera": "<PERSON><PERSON>a konnte nicht geöffnet werden. Versuch's nochmal.", "failedToOpenImagePicker": "Bildauswahl konnte nicht geöffnet werden. <PERSON>ersuch's nochmal.", "failedToOpenFilePicker": "Dateiauswahl konnte nicht geöffnet werden. <PERSON>ers<PERSON>'s nochmal."}, "billing": {"title": "Abrechnung & Abonnement", "currentPlan": "Aktueller Plan", "credits": "Credits", "remaining": "übrig", "totalAvailableCredits": "Gesamt Verfügbare Credits", "upgrade": "Upgraden", "topUp": "Aufladen", "creditBreakdown": "Credit-Übersicht", "renewsInDays": "<PERSON><PERSON><PERSON>t sich in {{days}}T", "noRenewal": "<PERSON><PERSON>", "extra": "Extra", "nextBilling": "Nächste Abrechnung", "changePlan": "Plan Ändern", "upgradeOrDowngrade": "Upgrade oder Downgrade", "billingPortal": "Abrechnungsportal", "paymentMethods": "Zahlungsmethoden", "annualCommitment": "Jahresverpflichtung", "activeUntil": "Aktiv bis {{date}}", "subscriptionCancelled": "Abonnement Gekündigt", "subscriptionCancelledOn": "De<PERSON> wird am {{date}} g<PERSON><PERSON><PERSON><PERSON>", "reactivate": "Reaktivieren", "reactivating": "Reaktivierung...", "howCreditsWork": "<PERSON><PERSON> <PERSON>", "learnAboutCredits": "Lerne über Credit-Nutzung", "cancelPlan": "Plan Kündigen", "usage": "<PERSON><PERSON>ung", "last30Days": "Letzte 30T", "failedToLoad": "Fehler beim Laden der Abonnement-Daten", "buyCredits": "<PERSON>", "availableCredits": "Verfügbare Credits", "creditPackages": "Credit-Pakete", "choosePackageBoost": "W<PERSON>hle ein Paket und erhöhe dein Credit-Guthaben", "ranOutOfCredits": "Dir sind die Credits ausgegangen. Jetzt upgraden.", "choosePlan": "Wähle deinen Plan", "getStarted": "Loslegen", "selectPlan": "Plan Auswählen", "upgradeNow": "Jetzt Upgraden", "downgrade": "Downgraden", "switchPlan": "Plan Wechseln", "monthly": "<PERSON><PERSON><PERSON>", "yearlyCommitment": "<PERSON><PERSON><PERSON><PERSON>", "save15Percent": "Spare 15%", "mostPopular": "Beliebteste", "perMonth": "/Monat", "billedYearly": "j<PERSON><PERSON><PERSON> abgerechnet", "billedMonthly": "monatlich abgerechnet", "creditsIncluded": "{{amount}} Credits/Monat", "unlimitedCredits": "Unbegrenzte Credits", "everything": "Alles in {{plan}}", "plus": "plus", "addCredits": "<PERSON>uf<PERSON>", "needMoreCredits": "<PERSON><PERSON> <PERSON>?", "purchaseAdditional": "<PERSON><PERSON>e jederzeit zusätzliche Credits"}, "theme": {"title": "<PERSON><PERSON><PERSON>", "preview": "Vorschau", "themeOptions": "Theme-Op<PERSON><PERSON>", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System", "lightDescription": "Immer helles Theme verwenden", "darkDescription": "Immer dunkles Theme verwenden", "systemDescription": "Geräteeinstellungen folgen", "lightMode": "<PERSON><PERSON>", "darkMode": "Dunkler Modus"}, "language": {"title": "<PERSON><PERSON><PERSON>", "selectLanguage": "Sprache Auswählen"}, "beta": {"title": "Beta-Funktionen", "experimentalFeatures": "Experimentelle Funktionen", "earlyAccess": "<PERSON><PERSON><PERSON> zu neuen Fähigkeiten", "advancedFeatures": "Erweiterte Funktionen", "advancedDescription": "<PERSON><PERSON><PERSON> zu experimentellen Funktionen und erweiterten Tools bevor sie für alle verfügbar sind", "whatsIncluded": "Was Enthalten Ist", "experimentalTools": "<PERSON><PERSON>", "experimentalToolsDescription": "Probiere neue Fähigkeiten vor der offiziellen Veröffentlichung aus", "advancedSettings": "Erweiterte Einstellungen", "advancedSettingsDescription": "Passe deine Erfahrung mit Power-User-Optionen an", "pleaseNote": "<PERSON><PERSON>", "betaWarning": "Beta-Funktionen können instabil sein und sich ohne Ankündigung ändern. Nutzung auf eigene Gefahr."}, "accountDeletion": {"title": "<PERSON><PERSON>", "deletionScheduled": "Löschung Geplant", "scheduledFor": "<PERSON><PERSON><PERSON>", "deleteYourAccount": "<PERSON><PERSON>", "actionCannotBeUndone": "Diese Aktion kann nicht rückgängig gemacht werden", "accountWillBeDeleted": "<PERSON><PERSON> wird dauerhaft gel<PERSON>t", "whatWillBeDeleted": "Was Gelöscht Wird", "allAgents": "Alle deine Agenten und Agent-Versionen", "allThreads": "Alle deine Threads und Konversationen", "allCredentials": "Alle deine Anmeldedaten und Integrationen", "subscriptionData": "Deine Abonnement- und Abrechnungsdaten", "gracePeriod": "30-<PERSON><PERSON>", "gracePeriodDescription": "De<PERSON> wird in 30 Tagen zur Löschung vorgesehen. Du kannst diese Anfrage jederzeit während der Kulanzfrist abbrechen.", "typeDeleteToConfirm": "T<PERSON><PERSON> {{text}} zur Bestätigung", "deletePlaceholder": "LÖSCHEN", "deleteAccount": "<PERSON><PERSON>", "cancelDeletion": "Löschung Abbrechen", "processing": "Verarbeitung...", "deletionScheduledSuccess": "<PERSON><PERSON> wird in 30 Tagen gelöscht. Du kannst diese Anfrage jederzeit abbrechen.", "cancelDeletionTitle": "Kontolöschung Abbrechen", "cancelDeletionDescription": "Möchtest du die Löschung deines Kontos abbrechen? Dein Konto und alle Daten bleiben erhalten.", "back": "Zurück", "deletionCancelled": "Löschung Abgebrochen", "deletionCancelledSuccess": "<PERSON><PERSON> ist sicher. Die Löschung wurde abgebrochen.", "failedToRequest": "Fehler beim Anfordern der Kontolöschung", "failedToCancel": "Fehler beim Abbrechen der Kontolöschung", "cancelRequestDescription": "Du kannst diese Anfrage jederzeit vor dem Löschungsdatum abbrechen. Alle deine Daten werden erhalten, wenn du abbrichst."}, "models": {"selectModel": "<PERSON><PERSON>", "chooseModelFor": "<PERSON><PERSON><PERSON><PERSON> ein Modell für {{agent}}"}, "placeholders": {"default": "Gib Kortix eine Aufgabe...", "search": "Suchen...", "searchConversations": "<PERSON>ts durchsuchen", "searchAgents": "Workers suchen", "imageGeneration": "Beschreibe das Bild, das du erstellen möchtest...", "imageWithStyle": "Beschreibe dein {{style}}-Bild...", "slidesGeneration": "Was für eine Präsentation brauchst du?", "slidesWithTemplate": "Beschreibe deine {{template}}-Präsentation...", "dataAnalysis": "<PERSON>e Datenanalyse brauchst du?", "documentCreation": "Was für ein Dokument brauchst du?", "peopleSearch": "Wen suchst du?", "researchQuery": "Was möchtest du recherchieren?"}, "emptyStates": {"noConversations": "<PERSON><PERSON> keine <PERSON>", "noConversationsDescription": "Starte einen neuen Chat", "noWorkers": "Noch keine Workers", "noWorkersDescription": "<PERSON><PERSON><PERSON> deinen ersten <PERSON>", "triggers": "Triggers", "triggersDescription": "Deine Automatisierungs-Triggers erscheinen hier", "noResults": "<PERSON><PERSON>", "tryDifferentSearch": "Probier einen anderen <PERSON>"}, "actions": {"goBack": "Zurück", "returnToHome": "<PERSON>urück zum Home-Screen", "createNew": "Neuen {{item}} erstellen", "openConversation": "<PERSON><PERSON>: {{title}}"}, "usage": {"title": "<PERSON><PERSON>ung", "totalCreditsUsed": "Gesamt Verbrauchte Credits", "loadingUsageData": "Lade Nutzungsdaten...", "failedToLoad": "Fehler beim Laden der Daten", "usageStats": "Nutzungsstatistiken", "conversations": "Konversationen", "avgPerChat": "Durchschnitt pro Chat", "usageTrend": "Nutzungstrend", "lastConversations": "Letzte {{count}} Konversationen", "conversationBreakdown": "Konversations-Übersicht", "noConversationsYet": "Noch keine Konversationen", "conversationHistoryAppearHere": "<PERSON>ine Konversationshistorie erscheint hier", "showingTopOf": "Zeige {{shown}} von {{total}} Konversationen", "credits": "Credits", "upgradeYourPlan": "Upgrade deinen Plan", "upgradeDescription": "Hol dir mehr Credits und schalte Premium-Features frei", "topUp": "Aufladen", "topUpDescription": "Lade dein Konto mit zusätzlichen Credits auf", "upgrade": "Upgraden", "upgradeToUltra": "Upgrade auf Ultra", "upgradeToUltraDescription": "Ho<PERSON> dir den höchsten Tier für maximale Credits"}, "nameEdit": {"title": "<PERSON><PERSON>", "displayName": "Anzeigename", "yourNamePlaceholder": "<PERSON><PERSON>", "emailAddress": "E-Mail-Adresse", "notAvailable": "Nicht verfügbar", "nameRequired": "Name ist erforderlich", "nameTooLong": "Name ist zu lang (max 100 Zeichen)", "saving": "Wird gespeichert...", "saveChanges": "Änderungen Speichern", "nameUpdated": "<PERSON>in <PERSON> wurde erfolgreich aktualisiert", "failedToUpdate": "Fehler beim Aktualisieren des Namens. Bitte erneut versuchen."}, "integrations": {"title": "Integrationen", "description": "Verbinde deine Lieblings-<PERSON><PERSON> und -<PERSON><PERSON><PERSON>, um Workflows zu automatisieren und die Fähigkeiten deines Agenten zu erweitern.", "connectApps": "Verbinde deine Apps", "externalApps": "Externe Apps", "externalAppsDescription": "Beliebte Apps & Dienste verbinden", "customMcpServers": "Benutzerdefinierte MCP-Server", "customMcpDescription": "HTTP-basierte Integrationen hinzufügen", "composioApps": "Composio Apps", "composioAppsDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> von Geschäftsanwendungen verbinden", "connected": "Verbunden", "connectedToAgent": "Mit diesem Agenten verbunden", "available": "Verfügbare Apps", "connect": "Verbinden", "setup": "Einrichten", "manage": "<PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON>en suchen...", "noConnected": "Keine Integrationen verbunden", "noConnectedDescription": "Verbinde deine Lieblings-<PERSON><PERSON>, um zu beginnen", "toolsEnabled": "{{count}} <PERSON><PERSON> aktiviert", "connectionSuccess": "Erfolgreich verbunden!", "connectionError": "Verbindung fehlgeschlagen", "connecting": "Verbindung wird hergestellt...", "disconnect": "<PERSON><PERSON><PERSON>", "noAppsFound": "<PERSON><PERSON> gefunden", "tryDifferentSearch": "<PERSON><PERSON><PERSON> einen anderen <PERSON>", "loadingIntegrations": "Integrationen werden geladen...", "failedToLoad": "Laden fehlgeschlagen", "retry": "<PERSON><PERSON><PERSON> versuchen", "appDetails": {"developer": "<PERSON><PERSON><PERSON><PERSON>", "tools": "Tools", "connections": "{{count}} Verbindung{{plural}} bereit", "chooseHowToConnect": "<PERSON><PERSON><PERSON><PERSON>, wie du verbinden möchtest", "noToolsFound": "<PERSON><PERSON> gefunden", "toolsAvailableAfterSetup": "+{{count}} weitere Tools nach Einrichtung verfügbar"}, "connector": {"connectTo": "Mit {{app}} verbinden", "selectConnection": "<PERSON><PERSON>hle eine Verbindung oder erstelle eine neue", "createFirstConnection": "<PERSON><PERSON><PERSON> deine erste Verbindung", "useExistingConnection": "Vorhandene Verbindung verwenden", "profilesConnected": "{{count}} Profil{{plural}} bereits verbunden", "createNewConnection": "Neue Verbindung erstellen", "connectNewAccount": "Neues {{app}}-Konto verbinden", "cancel": "Abbrechen", "continue": "<PERSON><PERSON>", "selectAnOption": "<PERSON><PERSON>hle eine Option", "createNewProfile": "Neues Profil er<PERSON>llen", "chooseNameForConnection": "<PERSON><PERSON>hle einen Namen für diese Verbindung", "profileName": "Profilname", "profileNamePlaceholder": "{{app}}-Konto", "nameAvailable": "Name verfügbar", "nameAlreadyTaken": "Dieser Name wird bereits verwendet", "creating": "Wird erstellt...", "completeInBrowser": "<PERSON><PERSON> a<PERSON>ch<PERSON>n", "authenticateInstructions": "Bitte authentifiziere dich im Browserfenster und kehre dann hierher zurück", "reopenBrowser": "<PERSON><PERSON>er er<PERSON><PERSON>", "completedAuthentication": "Authentifizierung abgeschlossen", "goBack": "Zurück", "allSet": "Alles bereit", "connectionReady": "Deine {{app}}-Verbindung ist bereit", "createdOn": "Erstellt am {{date}}"}, "toolsSelector": {"configureTools": "{{app}}-<PERSON><PERSON> konfigurieren", "selectTools": "Wähle Tools für deinen Agenten", "readyToConfigureTools": "Bereit zum Konfigurieren", "selected": "{{count}} von {{total}} ausgewählt", "selectAll": "Alle auswählen", "deselectAll": "Alle abwählen", "loadingTools": "Tools werden geladen...", "failedToLoadTools": "Laden fehlgeschlagen", "noToolsAvailable": "<PERSON><PERSON> verfügbar", "noToolsDescription": "Diese Integration hat keine Tools", "addingTools": "Tools werden hinzugefügt...", "addTool": "{{count}} <PERSON><PERSON> hi<PERSON><PERSON>", "addTools": "{{count}} <PERSON><PERSON> hi<PERSON><PERSON>ü<PERSON>", "toolsAddedSuccess": "{{count}} {{app}}-Tools hinzugefügt!", "failedToSaveTools": "Speichern fehlgeschlagen."}, "customMcp": {"title": "Benutzerdefiniertes MCP", "description": "Verbindung zu einem benutzerdefinierten Model Control Protocol Server herstellen", "serverUrl": "Server-URL", "serverUrlPlaceholder": "https://ihr-mcp-server.com", "serverName": "Servername", "serverNamePlaceholder": "<PERSON><PERSON>er MCP Server", "discoveringTools": "Tools werden entdeckt...", "discoverTools": "<PERSON><PERSON>", "enterValidUrl": "Bitte geben Si<PERSON> eine gültige HTTP- oder HTTPS-URL ein.", "enterServerName": "Bitte geben Sie einen Namen für diesen MCP-Server ein.", "noToolsFound": "<PERSON><PERSON> Tools gefunden. Bitte überprüfen Sie Ihre Konfiguration.", "failedToConnect": "Verbindung zum MCP-Server fehlgeschlagen. Bitte überprüfen Sie Ihre Konfiguration.", "toolsConfigured": "Benutzerdefiniertes MCP hinzugefügt", "toolsConfiguredMessage": "{{count}} <PERSON><PERSON> konfiguriert"}}}