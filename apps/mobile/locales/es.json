{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "done": "Listo", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "Sí", "no": "No", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "search": "Buscar", "loading": "Cargando...", "error": "Error", "retry": "Reintentar", "send": "Enviar", "back": "Atrás", "new": "Nuevo", "create": "<PERSON><PERSON><PERSON>"}, "auth": {"signIn": "In<PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON>", "signUp": "Registrarse", "logIn": "In<PERSON><PERSON>", "signingIn": "Iniciando se<PERSON>...", "sending": "Enviando...", "email": "Email", "emailAddress": "Dirección de email", "emailPlaceholder": "Email", "password": "Contraseña", "passwordPlaceholder": "Contraseña", "confirmPassword": "Confirma tu contraseña", "confirmPasswordPlaceholder": "Confirma tu contraseña", "forgotPassword": "¿Olvidaste tu contraseña?", "resetPassword": "Restablecer contraseña", "resetPasswordDescription": "Ingresa tu email y te enviaremos un link para restablecer tu contraseña", "sendResetLink": "Enviar link", "checkYourEmail": "Revisa tu email", "resetLinkSent": "Te enviamos un link a", "didntReceiveEmail": "¿No recibiste el email? Toca para reenviar", "backToSignIn": "<PERSON>ver al login", "magicLinkSent": "Te enviamos un magic link. Haz clic en él en tu email para iniciar sesión.", "magicLinkDescription": "Ingresa tu email y te enviaremos un magic link para iniciar sesión", "sendMagicLink": "Enviar magic link", "resendLink": "Reenviar link", "magicLinkFailed": "Error al enviar magic link", "termsRequired": "Por favor acepta los términos y condiciones", "continueWith": "Continuar con", "continueWithApple": "Continuar con <PERSON>", "continueWithGoogle": "Continuar con <PERSON>", "continueWithEmail": "Con<PERSON><PERSON><PERSON> con Email", "signInWithEmail": "Iniciar con email", "signInWithApple": "Iniciar con <PERSON>", "signInWithGoogle": "Iniciar con <PERSON>", "orEmail": "o email", "or": "o", "orContinueWith": "O continuar con", "alreadyHaveAccount": "¿Ya tienes cuenta?", "dontHaveAccount": "¿No tienes cuenta?", "tapToContinue": "Toca para continuar", "drawer": {"defaultTitle": "Create an Account", "defaultMessage": "Sign up or log in to continue", "signInToChat": "Sign in to <PERSON><PERSON>", "signInToChatMessage": "Create an account or log in to start chatting with AI workers.", "signUpUnlock": "Create an Account", "signUpUnlockMessage": "Sign up to unlock full access to AI workers and features.", "welcomeBack": "Welcome Back", "welcomeBackMessage": "Log in to continue using AI workers and features.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "<PERSON><PERSON><PERSON><PERSON>", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}, "signOutConfirm": "¿Seguro que quieres cerrar sesión?", "createFreeAccount": "<PERSON>rear cuenta gratis", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "enterEmailPassword": "Ingresa email y contraseña", "enterEmailAddress": "Ingresa tu email", "signInFailed": "Error al iniciar sesión. Verifica tus datos.", "signUpFailed": "Error al registrarse. Intenta de nuevo.", "resetFailed": "No pudimos enviar el email. Intenta de nuevo.", "passwordsDontMatch": "Las contraseñas no coinciden", "passwordTooShort": "La contraseña debe tener al menos 8 caracteres", "welcomeTitle": "Contrata Kortix para", "rotatingPhrases": {"presentations": "presentaciones", "writing": "redacción", "emails": "emails", "research": "investigación", "planning": "planificación", "studying": "estudiar", "anything": "cualquier cosa."}, "agreeTerms": "Acepto los", "userTerms": "Términos y Condiciones de Usuario", "acknowledgePrivacy": "y reconozco el", "privacyNotice": "Aviso de privacidad", "confirmationEmailSent": "Enlace de confirmación enviado a", "openEmailAppBtn": "<PERSON><PERSON><PERSON>", "openGmailBtn": "Abrir App <PERSON>", "validationErrors": {"emailPasswordRequired": "Por favor ingresa email y contraseña", "passwordsNoMatch": "Las contraseñas no coinciden", "passwordTooShort": "La contraseña debe tener al menos 8 caracteres"}}, "onboarding": {"skip": "Saltar", "next": "Siguient<PERSON>", "getStarted": "Comenzar", "slide1": {"title": "Chat con IA", "description": "Ten conversaciones naturales con asistentes IA poderosos que entienden el contexto y brindan respuestas útiles"}, "slide2": {"title": "<PERSON><PERSON><PERSON>", "description": "Obtén respuestas instantáneas con los últimos modelos de IA. Tu asistente siempre está listo para ayudar"}, "slide3": {"title": "Seguro y Privado", "description": "Tus conversaciones están encriptadas y seguras. Respetamos tu privacidad y nunca compartimos tus datos"}, "slide4": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Configura triggers y workers para automatizar tus flujos de trabajo y aumentar la productividad"}, "slides": {"title": "Generar Presentaciones", "description": "Crea presentaciones profesionales en segundos. Desde pitch decks hasta reportes de negocio.", "example": "Crea un pitch deck para mi startup"}, "research": {"title": "Investigación Profunda", "description": "Investigación web integral con insights instantáneos, resúmenes y análisis.", "example": "Investiga tendencias de IA en salud"}, "data": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, crea visualizaciones y genera inteligencia accionable.", "example": "Analiza datos de ventas trimestrales"}, "docs": {"title": "Creación de Documentos", "description": "Genera reportes, borradores de emails y crea cualquier documento con asistencia de IA.", "example": "Redacta una propuesta de proyecto"}, "automation": {"title": "Automatización Inteligente", "description": "Configura triggers para automatizar flujos de trabajo. Programa tareas y monitorea eventos.", "example": "Enviar reportes diarios a las 9 AM"}, "superworker": {"title": "Tu Super Worker IA", "description": "Combina todas las características para construir workers de IA personalizados para cualquier tarea que necesites.", "example": "Crear un agente de soporte al cliente"}}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nombre", "billing": "Facturación", "integrations": "Integraciones", "theme": "Theme e Icono", "language": "Idioma", "advancedFeatures": "Funciones avanzadas", "signOut": "<PERSON><PERSON><PERSON>", "upgrade": "Actualizar a Pro", "beta": "Funciones Beta", "deleteAccount": "Eliminar Cuenta", "unlockFeatures": "Desbloquear todas las funciones"}, "chat": {"newChat": "Nuevo Chat", "placeholder": "Dale a Kortix una tarea...", "inputPlaceholder": "Pídele a Kortix hacer cualquier cosa...", "recording": "Grabando...", "cancelRecording": "Cancelar grabación", "stopRecording": "Detener grabación", "threadTitle": "<PERSON><PERSON><PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "noMessages": "Aún no hay mensajes", "startConversation": "Empieza una conversación", "taskCompleted": "<PERSON><PERSON> completada", "howWasThisResult": "¿Cómo fue este resultado?", "rateThisResult": "Califica este resultado", "feedbackHelpsImprove": "Tu comentario nos ayuda a mejorar", "additionalFeedbackOptional": "Comentario adicional (opcional)", "helpKortixImprove": "<PERSON><PERSON>da a Kortix a mejorar con mi comentario", "submit": "Enviar", "submitting": "Enviando...", "suggestedFollowUps": "Sugerencias de seguimiento", "error": "Error", "feedbackSubmitFailed": "Error al enviar el comentario", "feedbackSubmitFailedRetry": "Error al enviar el comentario. Por favor, inténtalo de nuevo."}, "agents": {"selectAgent": "Elegir Worker", "chooseAgent": "Elige un worker", "createAgent": "Crear Worker Personalizado", "newWorker": "Nuevo Worker", "myWorkers": "Mis Workers", "searchAgents": "Buscar workers", "defaultAgent": "Kortix", "defaultDescription": "IA para todo tipo de tareas", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "Experto en escribir y depurar código", "presenter": "Presenter", "presenterDescription": "Crea presentaciones y slides increíbles", "developer": "Developer", "developerDescription": "Especialista en desarrollo full-stack", "support": "Support", "supportDescription": "Experto en atención al cliente y soporte", "analyst": "Analyst", "analystDescription": "Especialista en análisis de datos e insights", "creative": "Creative", "creativeDescription": "Creación de contenido y escritura creativa", "researcher": "Researcher", "researcherDescription": "Experto en investigación y recopilación de info"}, "quickActions": {"image": "Imagen", "slides": "Slides", "data": "Data", "docs": "Docs", "people": "Gente", "summarizePlaceholder": "¿Qué te gustaría resumir?", "translatePlaceholder": "¿Qué te gustaría traducir?", "explainPlaceholder": "¿Qué te gustaría que te explique?", "research": "Research", "chooseStyle": "Elige estilo de {{action}}", "imageStyles": {"abstract": "Abstracto", "anime": "Anime", "comic": "<PERSON><PERSON><PERSON>", "digital-art": "Digital", "geometric": "Geométrico", "impressionist": "Impresionista", "isometric": "Isométrico", "minimalist": "Minimalista", "neon": "Neón", "oil-painting": "<PERSON><PERSON>", "pastel": "Pastel", "photorealistic": "Foto", "surreal": "Surrealista", "vintage": "Vintage", "watercolor": "<PERSON><PERSON><PERSON><PERSON>"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "Charts", "table": "Tablas", "pie-chart": "Gráfico Circular", "line-graph": "Gráfico de Líneas", "statistics": "Estadísticas", "comparison": "Comparar", "trends": "Tendencias", "summary": "Resumen"}, "documentTypes": {"essay": "Essay", "letter": "Carta", "report": "Report", "email": "Email", "article": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notas", "blog-post": "Blog Post", "summary": "Resumen"}, "peopleTypes": {"expert": "Experto", "colleague": "Colega", "contact": "Contacto", "team": "Team", "partner": "Partner", "influencer": "Influencer", "mentor": "Mentor", "advisor": "<PERSON><PERSON><PERSON>"}, "researchSources": {"academic": "Académico", "scientific": "Científico", "news": "News", "web": "Web", "books": "Libros", "articles": "<PERSON><PERSON><PERSON><PERSON>", "papers": "Papers", "database": "Base de datos"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "Nuevo Chat", "newWorker": "Nuevo Worker", "newTrigger": "Nuevo Trigger", "searchConversations": "Buscar chats", "noConversations": "Aún no hay chats", "startNewChat": "Inicia un nuevo chat", "conversations": "Chats", "home": "Home", "settings": "<PERSON><PERSON><PERSON><PERSON>", "profile": "Perfil"}, "languages": {"title": "Elegir <PERSON>", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "<PERSON><PERSON><PERSON>", "addAttachment": "<PERSON><PERSON><PERSON><PERSON>", "chooseAttachment": "¿Qué quieres adjuntar?", "removeAttachment": "Eliminar", "takePicture": "<PERSON><PERSON>", "takePictureDescription": "Usar la cámara para tomar una foto", "chooseImages": "<PERSON>eg<PERSON>", "chooseImagesDescription": "Seleccionar de tu galería", "chooseFiles": "Elegir Archivos", "chooseFilesDescription": "Seleccionar documentos u otros archivos", "photo": "Foto", "document": "Documento", "audio": "Audio", "cameraPermissionRequired": "Permiso de Cámara Necesario", "cameraPermissionMessage": "Activa el acceso a la cámara en tus ajustes.", "photosPermissionRequired": "Permiso de Fotos Necesario", "photosPermissionMessage": "Activa el acceso a fotos en tus ajustes.", "failedToOpenCamera": "No se pudo abrir la cámara. Intenta de nuevo.", "failedToOpenImagePicker": "No se pudo abrir la galería. Intenta de nuevo.", "failedToOpenFilePicker": "No se pudo abrir el selector de archivos. Intenta de nuevo.", "fileTooLarge": "El archivo excede el tamaño máximo permitido.", "uploadFailed": "Error al subir archivos. Por favor intenta de nuevo."}, "permissions": {"cameraTitle": "Permiso de Cámara Necesario", "cameraMessage": "Por favor activa el acceso a la cámara en tus ajustes para tomar fotos.", "galleryTitle": "Permiso de Galería Necesario", "galleryMessage": "Por favor activa el acceso a la galería en tus ajustes para elegir imágenes."}, "audio": {"transcriptionFailed": "Error al transcribir el audio. Por favor intenta de nuevo."}, "billing": {"title": "Facturación & Suscripción", "currentPlan": "Plan Actual", "credits": "C<PERSON>dit<PERSON>", "remaining": "restantes", "totalAvailableCredits": "Créditos Totales Disponibles", "upgrade": "<PERSON><PERSON><PERSON>", "topUp": "Recargar", "creditBreakdown": "Desglose de Créditos", "renewsInDays": "Se renueva en {{days}}d", "noRenewal": "Sin renovación", "extra": "Extra", "nextBilling": "Próxima Facturación", "changePlan": "Cambiar Plan", "upgradeOrDowngrade": "Me<PERSON>rar o degradar", "billingPortal": "Portal de Facturación", "paymentMethods": "Métodos de pago", "annualCommitment": "Compromiso <PERSON>", "activeUntil": "Activo hasta {{date}}", "subscriptionCancelled": "Suscripción Cancelada", "subscriptionCancelledOn": "Tu suscripción será cancelada el {{date}}", "reactivate": "Reactivar", "reactivating": "Reactivando...", "howCreditsWork": "Cómo funcionan los créditos", "learnAboutCredits": "Aprende sobre el uso de créditos", "cancelPlan": "Cancelar Plan", "usage": "<PERSON><PERSON>", "last30Days": "Últ<PERSON>s 30d", "failedToLoad": "Error al cargar datos de suscripción", "buyCredits": "<PERSON><PERSON><PERSON>", "availableCredits": "Créditos disponibles", "creditPackages": "Paquetes de Créditos", "choosePackageBoost": "Elige un paquete y aumenta tu saldo de créditos", "ranOutOfCredits": "Te quedaste sin créditos. Me<PERSON>ra ahora.", "choosePlan": "Elige tu Plan", "getStarted": "Comenzar", "selectPlan": "Seleccionar Plan", "upgradeNow": "<PERSON><PERSON><PERSON>", "downgrade": "Degradar", "switchPlan": "Cambiar Plan", "monthly": "<PERSON><PERSON><PERSON>", "yearlyCommitment": "<PERSON><PERSON>", "save15Percent": "Ahorra 15%", "mostPopular": "Más Popular", "perMonth": "/mes", "billedYearly": "facturado anualmente", "billedMonthly": "fact<PERSON>do mensualmente", "creditsIncluded": "{{amount}} créditos/mes", "unlimitedCredits": "Créditos ilimitados", "everything": "Todo en {{plan}}", "plus": "más", "addCredits": "<PERSON><PERSON><PERSON><PERSON>", "needMoreCredits": "¿Necesitas más créditos?", "purchaseAdditional": "Compra créditos adicionales en cualquier momento"}, "theme": {"title": "Apariencia", "preview": "Vista Previa", "themeOptions": "Opciones de Tema", "light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "system": "Sistema", "lightDescription": "Usar siempre tema claro", "darkDescription": "Usar siempre tema oscuro", "systemDescription": "Seguir configuración del dispositivo", "lightMode": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON>"}, "language": {"title": "Idioma", "selectLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "beta": {"title": "Funciones Beta", "experimentalFeatures": "Funciones Experimentales", "earlyAccess": "Obtén acceso temprano a nuevas capacidades", "advancedFeatures": "Funciones Avanzadas", "advancedDescription": "Accede a funciones experimentales y herramientas avanzadas antes de que se liberen a todos", "whatsIncluded": "<PERSON><PERSON>", "experimentalTools": "Herramientas Experimentales", "experimentalToolsDescription": "Prueba nuevas capacidades antes del lanzamiento oficial", "advancedSettings": "Configuración Avanzada", "advancedSettingsDescription": "Ajusta tu experiencia con opciones de usuario avanzado", "pleaseNote": "Ten en Cuenta", "betaWarning": "Las funciones beta pueden ser inestables y cambiar sin previo aviso. Úsalas bajo tu propio riesgo."}, "accountDeletion": {"title": "Eliminar Cuenta", "deletionScheduled": "Eliminación Programada", "scheduledFor": "Programada Para", "deleteYourAccount": "Eliminar tu Cuenta", "actionCannotBeUndone": "Esta acción no se puede deshacer", "accountWillBeDeleted": "Tu cuenta será eliminada permanentemente", "whatWillBeDeleted": "Qué será Eli<PERSON>do", "allAgents": "Todos tus agentes y versiones de agentes", "allThreads": "Todos tus hilos y conversaciones", "allCredentials": "Todas tus credenciales e integraciones", "subscriptionData": "Tu suscripción y datos de facturación", "gracePeriod": "Período de Gracia de 30 Días", "gracePeriodDescription": "Tu cuenta será programada para eliminación en 30 días. Puedes cancelar esta solicitud en cualquier momento durante el período de gracia.", "typeDeleteToConfirm": "Escribe {{text}} para confirmar", "deletePlaceholder": "ELIMINAR", "deleteAccount": "Eliminar Cuenta", "cancelDeletion": "Cancelar Eliminación", "processing": "Procesando...", "deletionScheduledSuccess": "Tu cuenta será eliminada en 30 días. Puedes cancelar esta solicitud en cualquier momento.", "cancelDeletionTitle": "Cancelar Eliminación de Cuenta", "cancelDeletionDescription": "¿Quieres cancelar la eliminación de tu cuenta? Tu cuenta y todos los datos serán preservados.", "back": "Atrás", "deletionCancelled": "Eliminación Cancelada", "deletionCancelledSuccess": "Tu cuenta está segura. La eliminación ha sido cancelada.", "failedToRequest": "Error al solicitar eliminación de cuenta", "failedToCancel": "Error al cancelar eliminación de cuenta", "cancelRequestDescription": "Puedes cancelar esta solicitud en cualquier momento antes de la fecha de eliminación. Todos tus datos serán preservados si cancelas."}, "models": {"selectModel": "Elegir <PERSON>o", "chooseModelFor": "Elige un modelo para {{agent}}"}, "placeholders": {"default": "Dale a Kortix una tarea...", "search": "Buscar...", "searchConversations": "Buscar chats", "searchAgents": "Buscar workers", "imageGeneration": "Describe la imagen que quieres crear...", "imageWithStyle": "Describe tu imagen {{style}}...", "slidesGeneration": "¿Qué presentación necesitas?", "slidesWithTemplate": "Describe tu presentación {{template}}...", "dataAnalysis": "¿Qué análisis de datos necesitas?", "documentCreation": "¿Qué documento necesitas?", "peopleSearch": "¿A quién buscas?", "researchQuery": "¿Qué quieres investigar?"}, "emptyStates": {"noConversations": "Aún no hay Chats", "noConversationsDescription": "Inicia un nuevo Chat", "noWorkers": "Aún no hay Workers", "noWorkersDescription": "Crea tu primer Worker", "triggers": "Triggers", "triggersDescription": "Tus Triggers de automatización aparecerán aquí", "noResults": "Sin resultados", "tryDifferentSearch": "Prueba otro término de bús<PERSON>da"}, "actions": {"goBack": "Volver", "returnToHome": "Volver al home", "createNew": "<PERSON>rear nuevo {{item}}", "openConversation": "<PERSON><PERSON><PERSON>: {{title}}"}, "usage": {"title": "<PERSON><PERSON>", "totalCreditsUsed": "Créditos Totales Usados", "loadingUsageData": "Cargando datos de uso...", "failedToLoad": "Error al cargar datos de uso", "usageStats": "Estadísticas de Uso", "conversations": "Conversaciones", "avgPerChat": "<PERSON><PERSON><PERSON> por <PERSON>", "usageTrend": "Tendencia de Uso", "lastConversations": "Últimas {{count}} conversaciones", "conversationBreakdown": "Desglose de Conversaciones", "noConversationsYet": "Aún no hay conversaciones", "conversationHistoryAppearHere": "Tu historial de conversaciones aparecerá aquí", "showingTopOf": "Mostrando {{shown}} de {{total}} conversaciones", "credits": "c<PERSON><PERSON><PERSON>", "upgradeYourPlan": "Mejora tu Plan", "upgradeDescription": "Obtén más créditos y desbloquea funciones premium", "topUp": "Recargar", "topUpDescription": "Recarga tu cuenta con créditos adicionales", "upgrade": "<PERSON><PERSON><PERSON>", "upgradeToUltra": "Mejorar a <PERSON>", "upgradeToUltraDescription": "Obtén el nivel más alto para créditos máximos"}, "nameEdit": {"title": "<PERSON><PERSON>", "displayName": "Nombre a Mostrar", "yourNamePlaceholder": "Tu nombre", "emailAddress": "Dirección de Email", "notAvailable": "No disponible", "nameRequired": "El nombre es obligatorio", "nameTooLong": "El nombre es muy largo (máx 100 caracteres)", "saving": "Guardando...", "saveChanges": "Guardar Cambios", "nameUpdated": "Tu nombre se ha actualizado correctamente", "failedToUpdate": "Error al actualizar el nombre. Inténtalo de nuevo."}, "integrations": {"title": "Integraciones", "description": "Conecta tus aplicaciones y servicios favoritos para automatizar flujos de trabajo y ampliar las capacidades de tu agente.", "connectApps": "Conecta tus <PERSON>", "externalApps": "Aplicaciones Externas", "externalAppsDescription": "Conecta aplicaciones y servicios populares", "customMcpServers": "Servidores MCP Personalizados", "customMcpDescription": "Agrega integraciones personalizadas basadas en HTTP", "composioApps": "Aplicaciones Composio", "composioAppsDescription": "Conéctate a cientos de aplicaciones empresariales y servicios", "connected": "Conectado", "connectedToAgent": "Conectado a este agente", "available": "Aplicaciones Disponibles", "connect": "Conectar", "setup": "Configurar", "manage": "Gestionar", "searchPlaceholder": "Buscar integraciones...", "noConnected": "Sin integraciones conectadas", "noConnectedDescription": "Conecta tus aplicaciones favoritas para comenzar", "toolsEnabled": "{{count}} herramientas habilitadas", "connectionSuccess": "¡Conectado exitosamente!", "connectionError": "Conexión fallida", "connecting": "Conectando...", "disconnect": "Desconectar", "noAppsFound": "No se encontraron aplicaciones", "tryDifferentSearch": "Prueba un término de búsqueda diferente", "loadingIntegrations": "Cargando integraciones...", "failedToLoad": "Error al cargar aplicaciones", "retry": "Reintentar", "appDetails": {"developer": "Desarrollador", "tools": "Herramientas", "connections": "{{count}} conexión{{plural}} lista{{plural}}", "chooseHowToConnect": "Elige cómo conectar", "noToolsFound": "No se encontraron herramientas para esta integración", "toolsAvailableAfterSetup": "+{{count}} herramientas más disponibles después de la configuración"}, "connector": {"connectTo": "Conectar a {{app}}", "selectConnection": "Selecciona una conexión o crea una nueva", "createFirstConnection": "Crea tu primera conexión", "useExistingConnection": "Usar Conexión Existente", "profilesConnected": "{{count}} perfil{{plural}} ya conectado{{plural}}", "createNewConnection": "Crear Nueva Conexión", "connectNewAccount": "Conectar una nueva cuenta de {{app}}", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "selectAnOption": "Selecciona una opción", "createNewProfile": "Crear Nuevo Perfil", "chooseNameForConnection": "Elige un nombre para esta conexión", "profileName": "Nombre del Perfil", "profileNamePlaceholder": "Cuenta de {{app}}", "nameAvailable": "Nombre disponible", "nameAlreadyTaken": "Este nombre ya está en uso", "creating": "Creando...", "completeInBrowser": "Completar en el Navegador", "authenticateInstructions": "Por favor autentica en la ventana del navegador, luego regresa aquí cuando hayas terminado", "reopenBrowser": "<PERSON><PERSON><PERSON><PERSON>", "completedAuthentication": "He Completado la Autenticación", "goBack": "Volver", "allSet": "<PERSON><PERSON>", "connectionReady": "Tu conexión con {{app}} está lista", "createdOn": "<PERSON><PERSON><PERSON> el {{date}}"}, "toolsSelector": {"configureTools": "Configu<PERSON> de {{app}}", "selectTools": "Selecciona herramientas para agregar a tu agente", "readyToConfigureTools": "Listo para configurar herramientas", "selected": "{{count}} de {{total}} se<PERSON><PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deselectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingTools": "<PERSON><PERSON><PERSON>...", "failedToLoadTools": "<PERSON><PERSON>r al Cargar Herramientas", "noToolsAvailable": "No Hay Herramientas Disponibles", "noToolsDescription": "Esta integración no tiene herramientas para configurar", "addingTools": "Agregando <PERSON>...", "addTool": "Agregar {{count}} Herramienta", "addTools": "Agregar {{count}} Herramientas", "toolsAddedSuccess": "¡Agregadas {{count}} herramientas de {{app}} a tu agente!", "failedToSaveTools": "Error al guardar herramientas. Revisa la consola para más detalles."}, "customMcp": {"title": "MCP Personalizado", "description": "Conectar a un servidor de Protocolo de Control de Modelo personalizado", "serverUrl": "URL del Servidor", "serverUrlPlaceholder": "https://tu-servidor-mcp.com", "serverName": "Nombre del Servidor", "serverNamePlaceholder": "Mi Servidor MCP Personalizado", "discoveringTools": "Descubriendo <PERSON>...", "discoverTools": "<PERSON><PERSON><PERSON><PERSON>", "enterValidUrl": "Por favor ingresa una URL HTTP o HTTPS válida.", "enterServerName": "Por favor ingresa un nombre para este servidor MCP.", "noToolsFound": "No se encontraron herramientas. Por favor verifica tu configuración.", "failedToConnect": "No se pudo conectar al servidor MCP. Por favor verifica tu configuración.", "toolsConfigured": "MCP <PERSON><PERSON><PERSON>", "toolsConfiguredMessage": "{{count}} herramientas configuradas"}}}