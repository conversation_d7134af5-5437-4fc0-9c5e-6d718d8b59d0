{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON>", "ok": "OK", "yes": "Sì", "no": "No", "delete": "Elimina", "edit": "Modifica", "search": "Cerca", "loading": "Caricamento...", "error": "Errore", "retry": "<PERSON><PERSON><PERSON><PERSON>", "send": "Invia", "back": "Indietro", "new": "Nuovo", "create": "<PERSON><PERSON>"}, "auth": {"signIn": "Accedi", "signOut": "<PERSON><PERSON><PERSON>", "signUp": "Registrati", "signingIn": "Accesso in corso...", "sending": "Invio...", "email": "Email", "emailAddress": "Indirizzo email", "emailPlaceholder": "Indirizzo email", "password": "Password", "passwordPlaceholder": "Password", "forgotPassword": "Password dimenticata?", "resetPassword": "Reim<PERSON>a password", "resetPasswordDescription": "Inserisci la tua email e ti invieremo un link per reimpostare la password", "sendResetLink": "Invia link", "checkYourEmail": "Controlla la tua email", "resetLinkSent": "Ti abbiamo inviato un link a", "didntReceiveEmail": "Non hai ricevuto l'email? Tocca per rinviare", "backToSignIn": "Torna al login", "magicLinkSent": "Ti abbiamo inviato un magic link. Cliccalo nella tua email per accedere.", "magicLinkDescription": "Inserisci la tua email e ti invieremo un magic link per accedere", "sendMagicLink": "Invia magic link", "resendLink": "Rinvia link", "magicLinkFailed": "Invio magic link fallito", "termsRequired": "Si prega di accettare i termini e le condizioni", "continueWith": "Continua con", "continueWithApple": "Continua con Apple", "continueWithGoogle": "Continua con Google", "continueWithEmail": "Continua con Email", "signInWithEmail": "Accedi con email", "signInWithApple": "Accedi con Apple", "signInWithGoogle": "Accedi con Google", "orEmail": "o email", "orContinueWith": "Oppure continua con", "alreadyHaveAccount": "Hai già un account?", "dontHaveAccount": "Non hai un account?", "tapToContinue": "Tocca per continuare", "drawer": {"defaultTitle": "Create an Account", "defaultMessage": "Sign up or log in to continue", "signInToChat": "Sign in to <PERSON><PERSON>", "signInToChatMessage": "Create an account or log in to start chatting with AI workers.", "signUpUnlock": "Create an Account", "signUpUnlockMessage": "Sign up to unlock full access to AI workers and features.", "welcomeBack": "Welcome Back", "welcomeBackMessage": "Log in to continue using AI workers and features.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "Ospite", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}, "signOutConfirm": "<PERSON><PERSON><PERSON> di voler uscire?", "createFreeAccount": "Crea account gratuito", "enterEmailPassword": "Inserisci email e password", "enterEmailAddress": "Inserisci il tuo indirizzo email", "signInFailed": "Accesso fallito. Controlla le tue credenziali.", "resetFailed": "Impossibile inviare l'email. Riprova.", "passwordsDontMatch": "Le password non corrispondono", "passwordTooShort": "La password deve avere almeno 8 caratteri", "welcomeTitle": "<PERSON><PERSON><PERSON> per", "rotatingPhrases": {"presentations": "presentazioni", "writing": "scrittura", "emails": "email", "research": "ricerca", "planning": "pianificazione", "studying": "studio", "anything": "qualsiasi cosa."}, "agreeTerms": "Accetto i", "userTerms": "Termini e Condizioni Utente", "acknowledgePrivacy": "e riconosco l'", "privacyNotice": "Informativa sulla privacy", "confirmationEmailSent": "Link di conferma inviato a", "openEmailAppBtn": "Apri App Email", "openGmailBtn": "Apri App Gmail", "validationErrors": {"emailPasswordRequired": "Inserisci email e password", "passwordsNoMatch": "Le password non corrispondono", "passwordTooShort": "La password deve avere almeno 8 caratteri"}}, "onboarding": {"skip": "Salta", "next": "<PERSON><PERSON>", "getStarted": "Inizia", "slide1": {"title": "Chat con IA", "description": "Abbi conversazioni naturali con potenti assistenti IA che comprendono il contesto e forniscono risposte utili"}, "slide2": {"title": "Veloce come un Fulmine", "description": "Ottieni risposte istantanee con i modelli IA più recenti. Il tuo assistente è sempre pronto ad aiutare"}, "slide3": {"title": "Sicuro e Privato", "description": "Le tue conversazioni sono crittografate e sicure. Rispettiamo la tua privacy e non condividiamo mai i tuoi dati"}, "slide4": {"title": "Automatizza le Attività", "description": "Imposta trigger e worker per automatizzare i tuoi flussi di lavoro e aumentare la produttività"}, "slides": {"title": "Genera Presentazioni", "description": "<PERSON>rea presentazioni professionali in pochi secondi. Da pitch deck a report aziendali.", "example": "Crea un pitch deck per la mia startup"}, "research": {"title": "Ricerca Approfondita", "description": "Ricerca web completa con insights istantanei, riassunti e analisi.", "example": "Ricerca le tendenze IA nella sanità"}, "data": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, crea visualizzazioni e genera intelligence azionabile.", "example": "Analizza i dati di vendita trimestrali"}, "docs": {"title": "Creazione Documenti", "description": "Genera report, bozze di email e crea qualsiasi documento con l'assistenza IA.", "example": "Redigi una proposta di progetto"}, "automation": {"title": "Automazione Intelligente", "description": "Imposta trigger per automatizzare i flussi di lavoro. Programma attività e monitora eventi.", "example": "Invia report gior<PERSON><PERSON> alle 9:00"}, "superworker": {"title": "Il Tuo Super Worker IA", "description": "Combina tutte le funzionalità per costruire worker IA personalizzati per qualsiasi attività di cui hai bisogno.", "example": "Crea un agente di supporto clienti"}}, "settings": {"title": "Impostazioni", "name": "Nome", "billing": "Fatturazione", "integrations": "Integrazioni", "theme": "Tema & Icona", "language": "<PERSON><PERSON>", "advancedFeatures": "Funzionalità avanzate", "signOut": "<PERSON><PERSON><PERSON>", "upgrade": "Passa a Pro", "beta": "Funzionalità Beta", "deleteAccount": "Elimina Account", "unlockFeatures": "Sblocca tutte le funzionalità"}, "chat": {"newChat": "Nuova Chat", "placeholder": "Dai a Kortix un compito...", "recording": "Registrazione...", "cancelRecording": "Annulla registrazione", "stopRecording": "Interrompi registrazione", "threadTitle": "<PERSON><PERSON> chat", "editTitle": "Modifica titolo", "noMessages": "Ancora nessun messaggio", "startConversation": "Inizia una conversazione", "taskCompleted": "Attività completata", "howWasThisResult": "Com'è stato questo risultato?", "rateThisResult": "Valuta questo risultato", "feedbackHelpsImprove": "Il tuo feedback ci aiuta a migliorare", "additionalFeedbackOptional": "Feedback aggiuntivo (opzionale)", "helpKortixImprove": "Aiuta Kortix a migliorare con il mio feedback", "submit": "Invia", "submitting": "Invio in corso...", "suggestedFollowUps": "Suggerimenti di follow-up", "error": "Errore", "feedbackSubmitFailed": "Invio del feedback fallito", "feedbackSubmitFailedRetry": "Invio del feedback fallito. Riprova."}, "agents": {"selectAgent": "Seleziona Worker", "chooseAgent": "Scegli un worker", "createAgent": "<PERSON><PERSON>", "newWorker": "Nuovo Worker", "myWorkers": "<PERSON> miei <PERSON>", "searchAgents": "Cerca workers", "defaultAgent": "Kortix", "defaultDescription": "IA generica per tutti i compiti", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "<PERSON><PERSON><PERSON> in scrittura e debug di codice", "presenter": "Presenter", "presenterDescription": "Crea presentazioni e slide coinvolgenti", "developer": "Developer", "developerDescription": "<PERSON><PERSON> s<PERSON> full-stack", "support": "Support", "supportDescription": "Esperto servizio clienti e supporto", "analyst": "Analyst", "analystDescription": "Specialista analisi dati e insights", "creative": "Creative", "creativeDescription": "Creazione contenuti e scrittura creativa", "researcher": "Researcher", "researcherDescription": "Esperto ricerca e raccolta informazioni"}, "quickActions": {"image": "<PERSON><PERSON><PERSON><PERSON>", "slides": "Slides", "data": "<PERSON><PERSON>", "docs": "Docs", "people": "<PERSON>e", "research": "Research", "chooseStyle": "<PERSON><PERSON><PERSON> stile {{action}}", "imageStyles": {"abstract": "<PERSON><PERSON><PERSON>", "anime": "Anime", "comic": "<PERSON><PERSON><PERSON>", "digital-art": "Digitale", "geometric": "Geometrico", "impressionist": "Impressionista", "isometric": "Isometrico", "minimalist": "Minimalista", "neon": "Neon", "oil-painting": "<PERSON><PERSON>", "pastel": "<PERSON><PERSON>", "photorealistic": "Foto", "surreal": "Surreale", "vintage": "Vintage", "watercolor": "<PERSON><PERSON><PERSON><PERSON>"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "<PERSON><PERSON>", "table": "<PERSON><PERSON><PERSON>", "pie-chart": "Grafico a Torta", "line-graph": "Grafico a Linee", "statistics": "Statistiche", "comparison": "Confronta", "trends": "Tendenze", "summary": "Riepilogo"}, "documentTypes": {"essay": "Essay", "letter": "<PERSON><PERSON>", "report": "Report", "email": "Email", "article": "Articolo", "notes": "Note", "blog-post": "Blog Post", "summary": "Riepilogo"}, "peopleTypes": {"expert": "<PERSON><PERSON><PERSON>", "colleague": "Collega", "contact": "Contat<PERSON>", "team": "Team", "partner": "Partner", "influencer": "Influencer", "mentor": "Mentor", "advisor": "Consulente"}, "researchSources": {"academic": "Accademico", "scientific": "Scientifico", "news": "News", "web": "Web", "books": "Libri", "articles": "Articoli", "papers": "Papers", "database": "Database"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "Nuovo Chat", "newWorker": "Nuovo Worker", "newTrigger": "<PERSON><PERSON><PERSON>", "searchConversations": "Cerca chats", "noConversations": "Ancora nessun chat", "startNewChat": "Inizia un nuovo chat", "conversations": "Chats", "home": "Home", "settings": "Impostazioni", "profile": "<PERSON>ilo"}, "languages": {"title": "Seleziona Lingua", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "Allegati", "addAttachment": "Aggiungi Allegato", "chooseAttachment": "Cosa vuoi allegare?", "removeAttachment": "<PERSON><PERSON><PERSON><PERSON>", "takePicture": "Scatta Foto", "takePictureDescription": "Usa la fotocamera per scattare una foto", "chooseImages": "<PERSON><PERSON><PERSON>", "chooseImagesDescription": "Seleziona dalla tua galleria", "chooseFiles": "Scegli File", "chooseFilesDescription": "Seleziona documenti o altri file", "photo": "Foto", "document": "Documento", "audio": "Audio", "cameraPermissionRequired": "Permesso Fotocamera Necessario", "cameraPermissionMessage": "Abilita l'accesso alla fotocamera nelle impostazioni.", "photosPermissionRequired": "<PERSON><PERSON>so Foto Necessario", "photosPermissionMessage": "Abilita l'accesso alle foto nelle impostazioni.", "failedToOpenCamera": "Impossibile aprire la fotocamera. Riprova.", "failedToOpenImagePicker": "Impossibile aprire la galleria. Riprova.", "failedToOpenFilePicker": "Impossibile aprire il selettore file. Riprova."}, "billing": {"title": "Fatturazione & Abbonamento", "currentPlan": "Piano Attuale", "credits": "Crediti", "remaining": "<PERSON><PERSON><PERSON>", "totalAvailableCredits": "Crediti Totali Disponibili", "upgrade": "<PERSON><PERSON><PERSON>", "topUp": "Ricarica", "creditBreakdown": "Dettaglio Crediti", "renewsInDays": "Si rinnova tra {{days}}g", "noRenewal": "<PERSON><PERSON><PERSON>", "extra": "Extra", "nextBilling": "Prossima Fatturazione", "changePlan": "Cambia Piano", "upgradeOrDowngrade": "Migliora o declassa", "billingPortal": "Portale di Fatturazione", "paymentMethods": "Metodi di pagamento", "annualCommitment": "Impegno Annuale", "activeUntil": "Attivo fino al {{date}}", "subscriptionCancelled": "Abbonamento Cancellato", "subscriptionCancelledOn": "Il tuo abbonamento sarà cancellato il {{date}}", "reactivate": "<PERSON><PERSON><PERSON><PERSON>", "reactivating": "Riattivazione...", "howCreditsWork": "Come funzionano i crediti", "learnAboutCredits": "Scopri l'utilizzo dei crediti", "cancelPlan": "Cancella Piano", "usage": "<PERSON><PERSON><PERSON><PERSON>", "last30Days": "Ultimi 30g", "failedToLoad": "Errore nel caricamento dati abbonamento", "buyCredits": "Acquista Crediti", "availableCredits": "Crediti disponibili", "creditPackages": "<PERSON><PERSON><PERSON> di Crediti", "choosePackageBoost": "Scegli un pacchetto e aumenta il tuo saldo crediti", "ranOutOfCredits": "Hai finito i crediti. Migliora ora.", "choosePlan": "<PERSON><PERSON><PERSON> il tuo Piano", "getStarted": "Inizia", "selectPlan": "Seleziona Piano", "upgradeNow": "<PERSON><PERSON><PERSON>", "downgrade": "Declassa", "switchPlan": "Cambia Piano", "monthly": "<PERSON><PERSON><PERSON>", "yearlyCommitment": "Annuale", "save15Percent": "Risparmia 15%", "mostPopular": "<PERSON><PERSON>", "perMonth": "/mese", "billedYearly": "fatturato annualmente", "billedMonthly": "fatturato mensi<PERSON>", "creditsIncluded": "{{amount}} crediti/mese", "unlimitedCredits": "Crediti illimitati", "everything": "<PERSON><PERSON> in {{plan}}", "plus": "più", "addCredits": "Aggiungi Crediti", "needMoreCredits": "Hai bisogno di più crediti?", "purchaseAdditional": "Acquista crediti aggiuntivi in qualsiasi momento"}, "theme": {"title": "Aspetto", "preview": "Anteprima", "themeOptions": "Opzioni Tema", "light": "Chiaro", "dark": "<PERSON><PERSON>", "system": "Sistema", "lightDescription": "Usa sempre tema chiaro", "darkDescription": "Usa sempre tema scuro", "systemDescription": "Segui impostazioni dispositivo", "lightMode": "Modalit<PERSON>", "darkMode": "Modalità <PERSON>"}, "language": {"title": "<PERSON><PERSON>", "selectLanguage": "Seleziona Lingua"}, "beta": {"title": "Funzionalità Beta", "experimentalFeatures": "Funzionalità Sperimentali", "earlyAccess": "Ottieni accesso anticipato alle nuove funzionalità", "advancedFeatures": "Funzionalità Avanzate", "advancedDescription": "Accedi a funzionalità sperimentali e strumenti avanzati prima che vengano rilasciati a tutti", "whatsIncluded": "Cosa è Incluso", "experimentalTools": "Strumenti Sperimentali", "experimentalToolsDescription": "Prova nuove funzionalità prima del rilascio ufficiale", "advancedSettings": "Impostazioni Avanzate", "advancedSettingsDescription": "Personalizza la tua esperienza con opzioni per utenti avanzati", "pleaseNote": "<PERSON><PERSON>", "betaWarning": "Le funzionalità beta potrebbero essere instabili e cambiare senza preavviso. Usa a tua discrezione."}, "accountDeletion": {"title": "Elimina Account", "deletionScheduled": "Eliminazione Programmata", "scheduledFor": "Programmata Per", "deleteYourAccount": "Elimina il tuo Account", "actionCannotBeUndone": "Questa azione non può essere annullata", "accountWillBeDeleted": "Il tuo account sarà eliminato permanentemente", "whatWillBeDeleted": "Cosa Sarà Eliminato", "allAgents": "Tutti i tuoi agenti e versioni degli agenti", "allThreads": "Tutti i tuoi thread e conversazioni", "allCredentials": "<PERSON>tte le tue credenziali e integrazioni", "subscriptionData": "I tuoi dati di abbonamento e fatturazione", "gracePeriod": "Periodo di Grazia di 30 Giorni", "gracePeriodDescription": "Il tuo account sarà programmato per l'eliminazione tra 30 giorni. Puoi annullare questa richiesta in qualsiasi momento durante il periodo di grazia.", "typeDeleteToConfirm": "Digita {{text}} per confermare", "deletePlaceholder": "ELIMINA", "deleteAccount": "Elimina Account", "cancelDeletion": "Annulla Eliminazione", "processing": "Elaborazione...", "deletionScheduledSuccess": "Il tuo account sarà eliminato tra 30 giorni. Puoi annullare questa richiesta in qualsiasi momento.", "cancelDeletionTitle": "Annulla Eliminazione Account", "cancelDeletionDescription": "Vuoi annullare l'eliminazione del tuo account? Il tuo account e tutti i dati saranno preservati.", "back": "Indietro", "deletionCancelled": "Eliminazione Annullata", "deletionCancelledSuccess": "Il tuo account è al sicuro. L'eliminazione è stata annullata.", "failedToRequest": "Impossibile richiedere l'eliminazione dell'account", "failedToCancel": "Impossibile annullare l'eliminazione dell'account", "cancelRequestDescription": "Puoi annullare questa richiesta in qualsiasi momento prima della data di eliminazione. Tutti i tuoi dati saranno preservati se annulli."}, "models": {"selectModel": "Seleziona Modello", "chooseModelFor": "<PERSON><PERSON><PERSON> un modello per {{agent}}"}, "placeholders": {"default": "Dai a Kortix un compito...", "search": "Cerca...", "searchConversations": "Cerca chat", "searchAgents": "Cerca workers", "imageGeneration": "Descrivi l'immagine che vuoi creare...", "imageWithStyle": "Descrivi la tua immagine {{style}}...", "slidesGeneration": "Che presentazione ti serve?", "slidesWithTemplate": "Descrivi la tua presentazione {{template}}...", "dataAnalysis": "Che analisi dati ti serve?", "documentCreation": "Che documento ti serve?", "peopleSearch": "Chi stai cercando?", "researchQuery": "Cosa vuoi ricercare?"}, "emptyStates": {"noConversations": "<PERSON><PERSON><PERSON> nessun <PERSON>", "noConversationsDescription": "Inizia un nuovo Chat", "noWorkers": "Ancora nessun Worker", "noWorkersDescription": "Crea il tuo primo Worker", "triggers": "Triggers", "triggersDescription": "I tuoi Triggers di automazione appariranno qui", "noResults": "<PERSON><PERSON><PERSON> r<PERSON>", "tryDifferentSearch": "Prova un altro termine di ricerca"}, "actions": {"goBack": "Indietro", "returnToHome": "Torna alla home", "createNew": "<PERSON>rea nuovo {{item}}", "openConversation": "<PERSON><PERSON>: {{title}}"}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "totalCreditsUsed": "Crediti Totali Utilizzati", "loadingUsageData": "Caricamento dati utilizzo...", "failedToLoad": "Errore nel caricamento dati", "usageStats": "Statistiche di Utilizzo", "conversations": "Conversazioni", "avgPerChat": "Media per Chat", "usageTrend": "Tendenza di Utilizzo", "lastConversations": "Ultime {{count}} conversazioni", "conversationBreakdown": "Dettaglio Conversazioni", "noConversationsYet": "Ancora nessuna conversazione", "conversationHistoryAppearHere": "La tua cronologia apparirà qui", "showingTopOf": "Mostrando {{shown}} di {{total}} conversazioni", "credits": "crediti", "upgradeYourPlan": "<PERSON><PERSON><PERSON> il tuo Piano", "upgradeDescription": "Ottieni più crediti e sblocca funzionalità premium", "topUp": "Ricarica", "topUpDescription": "Ricarica il tuo account con crediti aggiuntivi", "upgrade": "<PERSON><PERSON><PERSON>", "upgradeToUltra": "Passa a Ultra", "upgradeToUltraDescription": "O<PERSON><PERSON> il livello più alto per crediti massimi"}, "nameEdit": {"title": "Modifica Profilo", "displayName": "<PERSON><PERSON>", "yourNamePlaceholder": "Il tuo nome", "emailAddress": "<PERSON><PERSON><PERSON><PERSON>", "notAvailable": "Non disponibile", "nameRequired": "Il nome è obbligatorio", "nameTooLong": "Il nome è troppo lungo (max 100 caratteri)", "saving": "Salvataggio...", "saveChanges": "<PERSON><PERSON>", "nameUpdated": "Il tuo nome è stato aggiornato con successo", "failedToUpdate": "Errore nell'aggiornare il nome. Riprova."}, "integrations": {"title": "Integrazioni", "description": "<PERSON><PERSON><PERSON> le tue app e servizi preferiti per automatizzare i flussi di lavoro ed estendere le capacità del tuo agente.", "connectApps": "<PERSON><PERSON><PERSON> le tue App", "externalApps": "App <PERSON>", "externalAppsDescription": "Connetti app e servizi popolari", "customMcpServers": "Server MC<PERSON>", "customMcpDescription": "Aggiungi integrazioni personalizzate basate su HTTP", "composioApps": "App Composio", "composioAppsDescription": "Connettiti a centinaia di applicazioni aziendali", "connected": "<PERSON><PERSON><PERSON>", "connectedToAgent": "Con<PERSON>o a questo agente", "available": "App Disponibili", "connect": "<PERSON><PERSON><PERSON>", "setup": "Configura", "manage": "<PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "Cerca integrazioni...", "noConnected": "Nessuna integrazione connessa", "noConnectedDescription": "<PERSON><PERSON><PERSON> le tue app preferite per iniziare", "toolsEnabled": "{{count}} strumenti abilitati", "connectionSuccess": "Connesso con successo!", "connectionError": "Connessione fallita", "connecting": "Connessione...", "disconnect": "<PERSON><PERSON><PERSON>", "noAppsFound": "Nessuna app trovata", "tryDifferentSearch": "Prova un termine di ricerca diverso", "loadingIntegrations": "Caricamento integrazioni...", "failedToLoad": "Caricamento fallito", "retry": "<PERSON><PERSON><PERSON><PERSON>", "appDetails": {"developer": "Sviluppatore", "tools": "Strumenti", "connections": "{{count}} connessione{{plural}} pronta{{plural}}", "chooseHowToConnect": "<PERSON><PERSON><PERSON> come connetterti", "noToolsFound": "<PERSON><PERSON><PERSON> strumento trovato", "toolsAvailableAfterSetup": "+{{count}} strumenti disponibili dopo la configurazione"}, "connector": {"connectTo": "<PERSON><PERSON><PERSON> a {{app}}", "selectConnection": "Seleziona una connessione o creane una nuova", "createFirstConnection": "Crea la tua prima connessione", "useExistingConnection": "Usa Connessione Esistente", "profilesConnected": "{{count}} profilo{{plural}} già connesso{{plural}}", "createNewConnection": "Crea Nuova Connessione", "connectNewAccount": "Connetti nuovo account {{app}}", "cancel": "<PERSON><PERSON><PERSON>", "continue": "Continua", "selectAnOption": "Seleziona un'opzione", "createNewProfile": "Crea Nuovo Profilo", "chooseNameForConnection": "Sc<PERSON><PERSON> un nome per questa connessione", "profileName": "<PERSON><PERSON>", "profileNamePlaceholder": "Account {{app}}", "nameAvailable": "Nome disponibile", "nameAlreadyTaken": "Questo nome è già in uso", "creating": "Creazione...", "completeInBrowser": "Comple<PERSON> nel <PERSON>", "authenticateInstructions": "Autenticati nella finestra del browser, poi torna qui quando hai finito", "reopenBrowser": "Riapri browser", "completedAuthentication": "Ho Completato l'Autenticazione", "goBack": "Indietro", "allSet": "<PERSON><PERSON>", "connectionReady": "La tua connessione con {{app}} è pronta", "createdOn": "<PERSON><PERSON><PERSON> il {{date}}"}, "toolsSelector": {"configureTools": "Configura Strumenti {{app}}", "selectTools": "Seleziona strumenti da aggiungere al tuo agente", "readyToConfigureTools": "Pronto per configurare", "selected": "{{count}} di {{total}} selezionati", "selectAll": "<PERSON><PERSON><PERSON><PERSON>", "deselectAll": "Desele<PERSON><PERSON>", "loadingTools": "Caricamento strumenti...", "failedToLoadTools": "Caricamento Fallito", "noToolsAvailable": "Nessuno Strumento Disponibile", "noToolsDescription": "Questa integrazione non ha strumenti", "addingTools": "Aggiunta Strumenti...", "addTool": "Aggiungi {{count}} Strumento", "addTools": "Aggiungi {{count}} Strumenti", "toolsAddedSuccess": "{{count}} strumenti {{app}} aggiunti!", "failedToSaveTools": "Salvatag<PERSON> fallito."}, "customMcp": {"title": "MCP Personalizzato", "description": "Connetti a un server di Protocollo di Controllo Modello personalizzato", "serverUrl": "URL del Server", "serverUrlPlaceholder": "https://tuo-server-mcp.com", "serverName": "Nome del Server", "serverNamePlaceholder": "Il Mio Server MCP Personalizzato", "discoveringTools": "Scoperta Strumenti...", "discoverTools": "<PERSON><PERSON><PERSON>", "enterValidUrl": "Inserisci un URL HTTP o HTTPS valido.", "enterServerName": "Inserisci un nome per questo server MCP.", "noToolsFound": "Nessuno strumento trovato. Controlla la tua configurazione.", "failedToConnect": "Connessione al server MCP fallita. Controlla la tua configurazione.", "toolsConfigured": "MCP Personalizzato <PERSON>", "toolsConfiguredMessage": "{{count}} strumenti configurati"}}}