{"common": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "done": "Pronto", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "delete": "Excluir", "edit": "<PERSON><PERSON>", "search": "Buscar", "loading": "Carregando...", "error": "Erro", "retry": "Tentar de novo", "send": "Enviar", "back": "Voltar", "new": "Novo", "create": "<PERSON><PERSON><PERSON>"}, "auth": {"signIn": "Entrar", "signOut": "<PERSON><PERSON>", "signUp": "Cadastrar", "signingIn": "Entrando...", "sending": "Enviando...", "email": "Email", "emailAddress": "Endereço de email", "emailPlaceholder": "Endereço de email", "password": "<PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON>", "forgotPassword": "Esque<PERSON>u a senha?", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPasswordDescription": "Digite seu email e vamos te enviar um link para redefinir", "sendResetLink": "Enviar link", "checkYourEmail": "Confere teu email", "resetLinkSent": "Enviamos um link para", "didntReceiveEmail": "Não recebeu o email? Toque para reenviar", "backToSignIn": "Voltar ao login", "magicLinkSent": "Enviamos um magic link. Clica nele no teu email para entrar.", "magicLinkDescription": "Digite teu email e vamos te enviar um magic link para entrar", "sendMagicLink": "Enviar magic link", "resendLink": "Reenviar link", "magicLinkFailed": "Falha ao enviar magic link", "termsRequired": "Por favor aceite os termos e condições", "continueWith": "Continuar com", "continueWithApple": "Continuar com Apple", "continueWithGoogle": "Continuar com Google", "continueWithEmail": "Continuar com Email", "signInWithEmail": "Entrar com email", "signInWithApple": "Entrar com Apple", "signInWithGoogle": "Entrar com Google", "orEmail": "ou email", "orContinueWith": "Ou continuar com", "alreadyHaveAccount": "Já tem conta?", "dontHaveAccount": "Não tem conta?", "tapToContinue": "Toque para continuar", "drawer": {"defaultTitle": "Create an Account", "defaultMessage": "Sign up or log in to continue", "signInToChat": "Sign in to <PERSON><PERSON>", "signInToChatMessage": "Create an account or log in to start chatting with AI workers.", "signUpUnlock": "Create an Account", "signUpUnlockMessage": "Sign up to unlock full access to AI workers and features.", "welcomeBack": "Welcome Back", "welcomeBackMessage": "Log in to continue using AI workers and features.", "signUpToContinue": "Sign up to continue", "signUpToContinueMessage": "Please sign up or sign in to create and select workers, connect apps and much more"}, "guest": {"label": "Visitante", "title": "Browse Without\nan Account", "description": "Try Kortix with limited features. No signup required!", "continue": "Continue as Guest", "signUp": "Sign Up Instead", "agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy"}, "signOutConfirm": "Tem certeza que quer sair?", "createFreeAccount": "<PERSON><PERSON><PERSON> conta gr<PERSON>", "enterEmailPassword": "Digite email e senha", "enterEmailAddress": "Digite seu endereço de email", "signInFailed": "Falha ao entrar. Confere teus dados.", "resetFailed": "Não conseguimos enviar o email. Tenta de novo.", "logIn": "Entrar", "passwordsDontMatch": "<PERSON><PERSON> n<PERSON>m", "passwordTooShort": "Senha deve ter pelo menos 8 caracteres", "welcomeTitle": "Contrate o Kortix para", "rotatingPhrases": {"presentations": "apresentações", "writing": "escrita", "emails": "emails", "research": "pesquisa", "planning": "planejamento", "studying": "estudos", "anything": "qualquer coisa."}, "agreeTerms": "Concordo com os", "userTerms": "Termos e Condições do Usuário", "acknowledgePrivacy": "e reconheço o", "privacyNotice": "Aviso de privacidade", "confirmationEmailSent": "Link de confirmação enviado para", "openEmailAppBtn": "<PERSON><PERSON><PERSON>", "openGmailBtn": "Abrir App do Gmail", "validationErrors": {"emailPasswordRequired": "Por favor digite email e senha", "passwordsNoMatch": "<PERSON><PERSON> n<PERSON>m", "passwordTooShort": "Senha deve ter pelo menos 8 caracteres"}}, "onboarding": {"skip": "<PERSON><PERSON>", "next": "Próximo", "getStarted": "<PERSON><PERSON><PERSON>", "slide1": {"title": "Chat com IA", "description": "Tenha conversas naturais com assistentes IA poderosos que entendem o contexto e fornecem respostas úteis"}, "slide2": {"title": "Super Rápido", "description": "Obtenha respostas instantâneas com os modelos de IA mais recentes. Seu assistente está sempre pronto para ajudar"}, "slide3": {"title": "Seguro & Privado", "description": "Suas conversas são criptografadas e seguras. Respeitamos sua privacidade e nunca compartilhamos seus dados"}, "slide4": {"title": "Automatiza<PERSON>", "description": "Configure triggers e workers para automatizar seus fluxos de trabalho e aumentar a produtividade"}, "slides": {"title": "<PERSON><PERSON><PERSON>", "description": "Crie apresentações profissionais em segundos. De pitch decks a relatórios empresariais.", "example": "Crie um pitch deck para minha startup"}, "research": {"title": "Pesquisa Profunda", "description": "Pesquisa web abrangente com insights instantâneos, resumos e análises.", "example": "Pesquise tendências de IA na saúde"}, "data": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> dad<PERSON>, crie visualizações e gere inteligência acionável.", "example": "Analise dados de vendas trimestrais"}, "docs": {"title": "Criação de Documentos", "description": "Gere relatórios, rascu<PERSON>e emails e crie qualquer documento com assistência IA.", "example": "Rascunhe uma proposta de projeto"}, "automation": {"title": "Automação Inteligente", "description": "Configure triggers para automatizar fluxos de trabalho. Agende tarefas e monitore eventos.", "example": "Enviar relatórios diários às 9h"}, "superworker": {"title": "Seu Super Worker IA", "description": "Combine todos os recursos para construir workers IA personalizados para qualquer tarefa que você precisar.", "example": "Crie um agente de suporte ao cliente"}}, "settings": {"title": "Configurações", "name": "Nome", "billing": "Cobrança", "integrations": "Integrações", "theme": "Tema & Ícone", "language": "Idioma", "advancedFeatures": "Recursos avançados", "signOut": "<PERSON><PERSON>", "upgrade": "Fazer upgrade para Pro", "deleteAccount": "Excluir Conta", "beta": "Recursos Beta", "unlockFeatures": "Desbloquear todos os recursos"}, "chat": {"newChat": "Novo Chat", "placeholder": "Dê uma tarefa para o Kortix...", "recording": "Gravando...", "cancelRecording": "Cancelar gravação", "stopRecording": "Parar gravação", "threadTitle": "Título do chat", "editTitle": "<PERSON><PERSON>", "noMessages": "Ainda sem mensagens", "startConversation": "Começar uma conversa", "taskCompleted": "Tarefa concluída", "howWasThisResult": "Como foi este resultado?", "rateThisResult": "Avaliar este resultado", "feedbackHelpsImprove": "Teu feedback nos ajuda a melhorar", "additionalFeedbackOptional": "Feedback adicional (opcional)", "helpKortixImprove": "Ajuda Kortix a melhorar com meu feedback", "submit": "Enviar", "submitting": "Enviando...", "suggestedFollowUps": "Sugestões de acompanhamento", "error": "Erro", "feedbackSubmitFailed": "Falha ao enviar feedback", "feedbackSubmitFailedRetry": "Falha ao enviar feedback. Por favor, tente novamente."}, "agents": {"selectAgent": "Selecionar Worker", "chooseAgent": "Escolhe um worker", "createAgent": "Criar Worker Personalizado", "newWorker": "Novo Worker", "myWorkers": "Meus Workers", "searchAgents": "Buscar workers", "defaultAgent": "Kortix", "defaultDescription": "IA para todas as tarefas", "superWorker": "Super Worker", "coder": "Coder", "coderDescription": "Expert em escrever e debugar código", "presenter": "Presenter", "presenterDescription": "Cria apresentações e slides incríveis", "developer": "Developer", "developerDescription": "Especialista em desenvolvimento full-stack", "support": "Support", "supportDescription": "Expert em atendimento ao cliente e suporte", "analyst": "Analyst", "analystDescription": "Especialista em análise de dados e insights", "creative": "Creative", "creativeDescription": "Criação de conteúdo e escrita criativa", "researcher": "Researcher", "researcherDescription": "Expert em pesquisa e coleta de informações"}, "quickActions": {"image": "Imagem", "slides": "Slides", "data": "<PERSON><PERSON>", "docs": "Docs", "people": "<PERSON><PERSON><PERSON><PERSON>", "research": "Research", "chooseStyle": "<PERSON>sco<PERSON>he estilo de {{action}}", "imageStyles": {"abstract": "Abstrato", "anime": "Anime", "comic": "Q<PERSON>rin<PERSON>", "digital-art": "Digital", "geometric": "Geométrico", "impressionist": "Impressionista", "isometric": "Isométrico", "minimalist": "Minimalista", "neon": "Neon", "oil-painting": "Pintura a Óleo", "pastel": "Pastel", "photorealistic": "Foto", "surreal": "Surrealista", "vintage": "Vintage", "watercolor": "Aquarela"}, "slidesTemplates": {"business": "Business", "pitch-deck": "Pitch Deck", "report": "Report", "educational": "Educational", "creative": "Creative", "minimal": "Minimal", "marketing": "Marketing", "training": "Training"}, "dataTypes": {"chart": "Grá<PERSON><PERSON>", "table": "<PERSON><PERSON><PERSON>", "pie-chart": "Gráfico de Pizza", "line-graph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statistics": "Estatísticas", "comparison": "Comparar", "trends": "Tendências", "summary": "Resumo"}, "documentTypes": {"essay": "Essay", "letter": "Carta", "report": "Report", "email": "Email", "article": "Artigo", "notes": "Notas", "blog-post": "Post de Blog", "summary": "Resumo"}, "peopleTypes": {"expert": "Expert", "colleague": "Colega", "contact": "Contato", "team": "Time", "partner": "<PERSON><PERSON><PERSON>", "influencer": "Influencer", "mentor": "Mentor", "advisor": "Consultor"}, "researchSources": {"academic": "Acadêmico", "scientific": "Científico", "news": "Notícias", "web": "Web", "books": "<PERSON><PERSON>", "articles": "Artigos", "papers": "Papers", "database": "Banco de dados"}}, "menu": {"chats": "Chats", "workers": "Workers", "triggers": "Triggers", "newChat": "Novo Chat", "newWorker": "Novo Worker", "newTrigger": "<PERSON><PERSON>", "searchConversations": "Buscar chats", "noConversations": "<PERSON>da sem chats", "startNewChat": "Começar um novo chat", "conversations": "Chats", "home": "Home", "settings": "Configurações", "profile": "Perfil"}, "languages": {"title": "Selecionar Idioma", "en": "English", "es": "Español", "fr": "Français", "de": "De<PERSON>ch", "zh": "中文", "ja": "日本語", "pt": "Português", "it": "Italiano", "ru": "Русский", "ko": "한국어", "ar": "العربية", "hi": "हिन्दी"}, "attachments": {"title": "Anexos", "addAttachment": "<PERSON><PERSON><PERSON><PERSON>", "chooseAttachment": "O que você quer anexar?", "removeAttachment": "Remover", "takePicture": "<PERSON><PERSON><PERSON>", "takePictureDescription": "Usar a câmera para tirar uma foto", "chooseImages": "Escolher Imagens", "chooseImagesDescription": "Selecionar da sua galeria", "chooseFiles": "<PERSON><PERSON><PERSON><PERSON>", "chooseFilesDescription": "Selecionar documentos ou outros arquivos", "photo": "Foto", "document": "Documento", "audio": "<PERSON><PERSON><PERSON>", "cameraPermissionRequired": "Permissão de Câmera Necessária", "cameraPermissionMessage": "Ative o acesso à câmera nas configurações.", "photosPermissionRequired": "Permissão de Fotos Necessária", "photosPermissionMessage": "Ative o acesso às fotos nas configurações.", "failedToOpenCamera": "Não conseguimos abrir a câmera. Tenta de novo.", "failedToOpenImagePicker": "Não conseguimos abrir a galeria. Tenta de novo.", "failedToOpenFilePicker": "Não conseguimos abrir o seletor de arquivos. Tenta de novo."}, "billing": {"title": "Faturação & Assinatura", "currentPlan": "Plano Atual", "credits": "C<PERSON>dit<PERSON>", "remaining": "restantes", "totalAvailableCredits": "Total de Créditos Disponíveis", "upgrade": "<PERSON><PERSON><PERSON>", "topUp": "<PERSON><PERSON><PERSON><PERSON>", "creditBreakdown": "Detalhamento de Créditos", "renewsInDays": "Renova em {{days}}d", "noRenewal": "Sem renovação", "extra": "Extra", "nextBilling": "Próxima Faturação", "changePlan": "<PERSON><PERSON>", "upgradeOrDowngrade": "<PERSON><PERSON><PERSON> ou re<PERSON><PERSON><PERSON>", "billingPortal": "Portal de Faturação", "paymentMethods": "Métodos de pagamento", "annualCommitment": "Compromisso <PERSON>", "activeUntil": "Ativo até {{date}}", "subscriptionCancelled": "Assinatura Cancelada", "subscriptionCancelledOn": "Sua assinatura será cancelada em {{date}}", "reactivate": "<PERSON><PERSON><PERSON>", "reactivating": "Reativando...", "howCreditsWork": "Como os créditos funcionam", "learnAboutCredits": "Aprenda sobre uso de créditos", "cancelPlan": "Cancelar Plano", "usage": "<PERSON><PERSON>", "last30Days": "Últ<PERSON>s 30d", "failedToLoad": "Erro ao carregar dados da assinatura", "buyCredits": "<PERSON><PERSON><PERSON>", "availableCredits": "Créditos disponíveis", "creditPackages": "Pacotes de Créditos", "choosePackageBoost": "Escolha um pacote e aumente seu saldo de créditos", "ranOutOfCredits": "Você ficou sem créditos. Melhore agora.", "choosePlan": "Escolha seu Plano", "getStarted": "<PERSON><PERSON><PERSON>", "selectPlan": "Selecionar Plano", "upgradeNow": "<PERSON><PERSON><PERSON>", "downgrade": "Re<PERSON><PERSON><PERSON>", "switchPlan": "Trocar Plano", "monthly": "Mensal", "yearlyCommitment": "<PERSON><PERSON>", "save15Percent": "Economize 15%", "mostPopular": "Mais <PERSON>", "perMonth": "/mês", "billedYearly": "faturado an<PERSON>mente", "billedMonthly": "faturado mensalmente", "creditsIncluded": "{{amount}} créditos/mês", "unlimitedCredits": "Créditos ilimitados", "everything": "<PERSON>do em {{plan}}", "plus": "mais", "addCredits": "<PERSON><PERSON><PERSON><PERSON>", "needMoreCredits": "Precisa de mais créditos?", "purchaseAdditional": "Compre créditos adicionais a qualquer momento"}, "theme": {"title": "Aparência", "preview": "Visualização", "themeOptions": "Opções de Tema", "light": "<PERSON><PERSON><PERSON>", "dark": "Escuro", "system": "Sistema", "lightDescription": "Sempre usar tema claro", "darkDescription": "Sempre usar tema escuro", "systemDescription": "Seguir configurações do dispositivo", "lightMode": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON>"}, "language": {"title": "Idioma", "selectLanguage": "Selecionar Idioma"}, "beta": {"title": "Recursos Beta", "experimentalFeatures": "Recursos Experimentais", "earlyAccess": "<PERSON><PERSON> acesso antecipado a novas capacidades", "advancedFeatures": "Recursos <PERSON>", "advancedDescription": "Acesse recursos experimentais e ferramentas avançadas antes de serem lançados para todos", "whatsIncluded": "O que Está Incluído", "experimentalTools": "Ferramentas Experimentais", "experimentalToolsDescription": "Experimente novas capacidades antes do lançamento oficial", "advancedSettings": "Configurações Avançadas", "advancedSettingsDescription": "Ajuste sua experiência com opções de usuário avançado", "pleaseNote": "Observação", "betaWarning": "Recursos beta podem ser instáveis e mudar sem aviso prévio. Use por sua conta e risco."}, "accountDeletion": {"title": "Excluir Conta", "deletionScheduled": "Exclusão Agendada", "scheduledFor": "Agendada Para", "deleteYourAccount": "Excluir sua Conta", "actionCannotBeUndone": "Esta ação não pode ser desfeita", "accountWillBeDeleted": "Sua conta será excluída permanentemente", "whatWillBeDeleted": "O que Será Excluído", "allAgents": "Todos os seus agentes e versões de agentes", "allThreads": "<PERSON>das as suas threads e conversações", "allCredentials": "<PERSON><PERSON> as suas credenciais e integrações", "subscriptionData": "Seus dados de assinatura e cobrança", "gracePeriod": "Período de Carência de 30 Dias", "gracePeriodDescription": "Sua conta será agendada para exclusão em 30 dias. Você pode cancelar esta solicitação a qualquer momento durante o período de carência.", "typeDeleteToConfirm": "Digite {{text}} para confirmar", "deletePlaceholder": "EXCLUIR", "deleteAccount": "Excluir Conta", "cancelDeletion": "<PERSON><PERSON><PERSON>", "processing": "Processando...", "deletionScheduledSuccess": "Sua conta será excluída em 30 dias. Você pode cancelar esta solicitação a qualquer momento.", "cancelDeletionTitle": "Cancelar Exclusão de Conta", "cancelDeletionDescription": "Você quer cancelar a exclusão da sua conta? Sua conta e todos os dados serão preservados.", "back": "Voltar", "deletionCancelled": "Exclusão Cancelada", "deletionCancelledSuccess": "Sua conta está segura. A exclusão foi cancelada.", "failedToRequest": "Falha ao solicitar exclusão de conta", "failedToCancel": "Falha ao cancelar exclusão de conta", "cancelRequestDescription": "Você pode cancelar esta solicitação a qualquer momento antes da data de exclusão. Todos os seus dados serão preservados se cancelar."}, "models": {"selectModel": "Selecionar Modelo", "chooseModelFor": "Escolhe um modelo para {{agent}}"}, "placeholders": {"default": "Dê uma tarefa para o Kortix...", "search": "Buscar...", "searchConversations": "Buscar chats", "searchAgents": "Buscar workers", "imageGeneration": "Descreve a imagem que você quer criar...", "imageWithStyle": "Descreve sua imagem {{style}}...", "slidesGeneration": "Que apresentação você precisa?", "slidesWithTemplate": "Descreve sua apresentação {{template}}...", "dataAnalysis": "Que análise de dados você precisa?", "documentCreation": "Que documento você precisa?", "peopleSearch": "Quem você está procurando?", "researchQuery": "O que você quer pesquisar?"}, "emptyStates": {"noConversations": "<PERSON><PERSON>", "noConversationsDescription": "Comece um novo Chat", "noWorkers": "<PERSON><PERSON> sem Workers", "noWorkersDescription": "Crie seu primeiro Worker", "triggers": "Triggers", "triggersDescription": "Seus Triggers de automação vão aparecer aqui", "noResults": "Sem resultados", "tryDifferentSearch": "Tenta outro termo de busca"}, "actions": {"goBack": "Voltar", "returnToHome": "Voltar para home", "createNew": "Criar novo {{item}}", "openConversation": "<PERSON><PERSON><PERSON>: {{title}}"}, "usage": {"title": "<PERSON><PERSON>", "totalCreditsUsed": "Total de Créditos Usados", "loadingUsageData": "Carregando dados de uso...", "failedToLoad": "Erro ao carregar dados", "usageStats": "Estatísticas de Uso", "conversations": "Conversas", "avgPerChat": "<PERSON><PERSON><PERSON> p<PERSON>", "usageTrend": "Tendência de Uso", "lastConversations": "Últimas {{count}} conversas", "conversationBreakdown": "Detalhamento de Conversas", "noConversationsYet": "Ainda sem conversas", "conversationHistoryAppearHere": "Seu histórico vai aparecer aqui", "showingTopOf": "Mostrando {{shown}} de {{total}} conversas", "credits": "c<PERSON><PERSON><PERSON>", "upgradeYourPlan": "Mel<PERSON> seu Plano", "upgradeDescription": "Ganhe mais créditos e desbloqueie recursos premium", "topUp": "<PERSON><PERSON><PERSON><PERSON>", "topUpDescription": "Recarregue sua conta com créditos adicionais", "upgrade": "<PERSON><PERSON><PERSON>", "upgradeToUltra": "Upgrade para Ultra", "upgradeToUltraDescription": "Pegue o nível mais alto para créditos máximos"}, "nameEdit": {"title": "<PERSON><PERSON>", "displayName": "Nome de Exibição", "yourNamePlaceholder": "Seu nome", "emailAddress": "Endereço de Email", "notAvailable": "Não disponível", "nameRequired": "Nome é obrigatório", "nameTooLong": "Nome muito longo (máx 100 caracteres)", "saving": "Salvando...", "saveChanges": "<PERSON><PERSON>", "nameUpdated": "Seu nome foi atualizado com sucesso", "failedToUpdate": "Erro ao atualizar nome. Tente novamente."}, "integrations": {"title": "Integrações", "description": "Conecte seus aplicativos e serviços favoritos para automatizar fluxos de trabalho e estender as capacidades do seu agente.", "connectApps": "Conecte seus <PERSON>", "externalApps": "Aplicativos Externos", "externalAppsDescription": "Conectar aplicativos e serviços populares", "customMcpServers": "Servidores MCP Personalizados", "customMcpDescription": "Adicionar integrações personalizadas baseadas em HTTP", "composioApps": "Aplicativos Composio", "composioAppsDescription": "Conectar-se a centenas de aplicações empresariais", "connected": "Conectado", "connectedToAgent": "Conectado a este agente", "available": "Aplicativos Disponíveis", "connect": "Conectar", "setup": "Configurar", "manage": "Gerenciar", "searchPlaceholder": "Buscar integrações...", "noConnected": "Sem integrações conectadas", "noConnectedDescription": "Conecte seus aplicativos favoritos para começar", "toolsEnabled": "{{count}} ferrament<PERSON> ativadas", "connectionSuccess": "Conectado com sucesso!", "connectionError": "Falha na conexão", "connecting": "Conectando...", "disconnect": "Desconectar", "noAppsFound": "Nenhum aplicativo encontrado", "tryDifferentSearch": "Tente um termo de busca diferente", "loadingIntegrations": "Carregando integrações...", "failedToLoad": "Falha ao carregar", "retry": "Tentar novamente", "appDetails": {"developer": "<PERSON><PERSON><PERSON><PERSON>", "tools": "Ferramentas", "connections": "{{count}} conexão{{plural}} pronta{{plural}}", "chooseHowToConnect": "Escolha como conectar", "noToolsFound": "<PERSON>enhuma ferramenta encontrada", "toolsAvailableAfterSetup": "+{{count}} ferramentas disponíveis após configuração"}, "connector": {"connectTo": "Conectar a {{app}}", "selectConnection": "Selecione uma conexão ou crie uma nova", "createFirstConnection": "Crie sua primeira conexão", "useExistingConnection": "Usar Conexão Existente", "profilesConnected": "{{count}} perfil{{plural}} já conectado{{plural}}", "createNewConnection": "Criar Nova Conexão", "connectNewAccount": "Conectar nova conta {{app}}", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "selectAnOption": "Selecione uma opção", "createNewProfile": "Criar <PERSON>", "chooseNameForConnection": "Escolha um nome para esta conexão", "profileName": "Nome do Perfil", "profileNamePlaceholder": "Conta {{app}}", "nameAvailable": "Nome disponível", "nameAlreadyTaken": "Este nome já está em uso", "creating": "Criando...", "completeInBrowser": "Completar no Navegador", "authenticateInstructions": "Por favor, autentique na janela do navegador e retorne aqui quando terminar", "reopenBrowser": "<PERSON><PERSON><PERSON><PERSON>", "completedAuthentication": "Concluí a Autenticação", "goBack": "Voltar", "allSet": "<PERSON><PERSON>", "connectionReady": "Sua conexão com {{app}} está pronta", "createdOn": "<PERSON><PERSON><PERSON> em {{date}}"}, "toolsSelector": {"configureTools": "Configurar Ferramentas {{app}}", "selectTools": "Selecione ferramentas para adicionar ao seu agente", "readyToConfigureTools": "Pronto para configurar", "selected": "{{count}} de {{total}} selecionadas", "selectAll": "Selecion<PERSON>", "deselectAll": "Desselecionar Todas", "loadingTools": "Carregando ferramentas...", "failedToLoadTools": "<PERSON>al<PERSON> a<PERSON>ar", "noToolsAvailable": "Nenhuma Ferramenta Disponível", "noToolsDescription": "Esta integração não possui ferramentas", "addingTools": "Adicionando Ferramentas...", "addTool": "Adicionar {{count}} Ferramenta", "addTools": "Adicionar {{count}} Ferramentas", "toolsAddedSuccess": "{{count}} ferramentas {{app}} adicionadas!", "failedToSaveTools": "<PERSON>alha ao salvar."}, "customMcp": {"title": "MCP Personalizado", "description": "Conectar a um servidor de Protocolo de Controle de Modelo personalizado", "serverUrl": "URL do Servidor", "serverUrlPlaceholder": "https://seu-servidor-mcp.com", "serverName": "Nome do Servidor", "serverNamePlaceholder": "<PERSON><PERSON> MCP Personalizado", "discoveringTools": "Descobrindo Ferramentas...", "discoverTools": "Descobrir Ferramentas", "enterValidUrl": "Por favor, insira uma URL HTTP ou HTTPS válida.", "enterServerName": "Por favor, insira um nome para este servidor MCP.", "noToolsFound": "Nenhuma ferramenta encontrada. Por favor, verifique sua configuração.", "failedToConnect": "Falha ao conectar ao servidor MCP. Por favor, verifique sua configuração.", "toolsConfigured": "MCP Personalizado Adicionado", "toolsConfiguredMessage": "{{count}} ferramentas configuradas"}}}